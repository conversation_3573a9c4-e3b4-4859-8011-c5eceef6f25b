import{_ as m,av as M,aw as L,ax as j,ay as Y,l as o,d as H,az as z,aA as _,ah as q,am as K,ai as O,ag as Q,aB as U,aC as V,aD as W}from"./AugmentMessage-DSvQSfka.js";import{G as k}from"./graph-DMfaAq5a.js";import{l as Z}from"./layout-CLcxAsI6.js";import{i as S}from"./_baseUniq-DM7Qg9tM.js";import{c as $}from"./clone-Dh8Inf5T.js";import{m as B}from"./_basePickBy-BVIZpLlx.js";import"./IconButtonAugment-C4xMcLhX.js";import"./SpinnerAugment-CL9SZpf8.js";import"./CardAugment-bwPj7Y67.js";import"./arrow-up-right-from-square-DUrpll74.js";import"./index-BAWb-tvr.js";import"./message-broker-SEbJxN6J.js";import"./async-messaging-CtwQrvzD.js";import"./BaseTextInput-BAWt2_LS.js";import"./types-CGlLNakm.js";import"./file-paths-BPg3etNg.js";import"./mcp-logo-BBF9ZFwB.js";import"./CalloutAugment-C-hloZHD.js";import"./folder-opened-B3jucdqG.js";import"./index-BskWw2a8.js";import"./diff-utils-CRbaKECg.js";import"./LanguageIcon-BQz1eaw5.js";import"./preload-helper-Dv6uf1Os.js";import"./index-iuo-Ho0S.js";import"./keypress-DD1aQVr0.js";import"./await_block-DQGV_eqb.js";import"./chat-context-CLxziAX3.js";import"./types-DDm27S8B.js";import"./utils-0kgBFNBQ.js";import"./ra-diff-ops-model-DUTpCop3.js";import"./CollapseButtonAugment-Ba7Np-LE.js";import"./ButtonAugment-iwbEjzvh.js";import"./MaterialIcon-Bh8QWD0w.js";import"./CopyButton-BWQVMcne.js";import"./copy-BFy87Ryv.js";import"./ellipsis-B3ZqaMmA.js";import"./IconFilePath-j5ToM371.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-C3pDA31_.js";import"./augment-logo-CDzRYJ1a.js";import"./terminal-D0NX0vvY.js";import"./pen-to-square-DY0HDzb8.js";import"./TextAreaAugment-CiMTZgUO.js";function X(e){var r={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:ee(e),edges:ne(e)};return S(e.graph())||(r.value=$(e.graph())),r}function ee(e){return B(e.nodes(),function(r){var n=e.node(r),i=e.parent(r),d={v:r};return S(n)||(d.value=n),S(i)||(d.parent=i),d})}function ne(e){return B(e.edges(),function(r){var n=e.edge(r),i={v:r.v,w:r.w};return S(r.name)||(i.name=r.name),S(n)||(i.value=n),i})}var l=new Map,N=new Map,P=new Map,re=m(()=>{N.clear(),P.clear(),l.clear()},"clear"),D=m((e,r)=>{const n=N.get(r)||[];return o.trace("In isDescendant",r," ",e," = ",n.includes(e)),n.includes(e)},"isDescendant"),te=m((e,r)=>{const n=N.get(r)||[];return o.info("Descendants of ",r," is ",n),o.info("Edge is ",e),e.v!==r&&e.w!==r&&(n?n.includes(e.v)||D(e.v,r)||D(e.w,r)||n.includes(e.w):(o.debug("Tilt, ",r,",not in descendants"),!1))},"edgeInCluster"),A=m((e,r,n,i)=>{o.warn("Copying children of ",e,"root",i,"data",r.node(e),i);const d=r.children(e)||[];e!==i&&d.push(e),o.warn("Copying (nodes) clusterId",e,"nodes",d),d.forEach(s=>{if(r.children(s).length>0)A(s,r,n,i);else{const a=r.node(s);o.info("cp ",s," to ",i," with parent ",e),n.setNode(s,a),i!==r.parent(s)&&(o.warn("Setting parent",s,r.parent(s)),n.setParent(s,r.parent(s))),e!==i&&s!==e?(o.debug("Setting parent",s,e),n.setParent(s,e)):(o.info("In copy ",e,"root",i,"data",r.node(e),i),o.debug("Not Setting parent for node=",s,"cluster!==rootId",e!==i,"node!==clusterId",s!==e));const c=r.edges(s);o.debug("Copying Edges",c),c.forEach(p=>{o.info("Edge",p);const E=r.edge(p.v,p.w,p.name);o.info("Edge data",E,i);try{te(p,i)?(o.info("Copying as ",p.v,p.w,E,p.name),n.setEdge(p.v,p.w,E,p.name),o.info("newGraph edges ",n.edges(),n.edge(n.edges()[0]))):o.info("Skipping copy of edge ",p.v,"-->",p.w," rootId: ",i," clusterId:",e)}catch(C){o.error(C)}})}o.debug("Removing node",s),r.removeNode(s)})},"copy"),J=m((e,r)=>{const n=r.children(e);let i=[...n];for(const d of n)P.set(d,e),i=[...i,...J(d,r)];return i},"extractDescendants"),oe=m((e,r,n)=>{const i=e.edges().filter(c=>c.v===r||c.w===r),d=e.edges().filter(c=>c.v===n||c.w===n),s=i.map(c=>({v:c.v===r?n:c.v,w:c.w===r?r:c.w})),a=d.map(c=>({v:c.v,w:c.w}));return s.filter(c=>a.some(p=>c.v===p.v&&c.w===p.w))},"findCommonEdges"),I=m((e,r,n)=>{const i=r.children(e);if(o.trace("Searching children of id ",e,i),i.length<1)return e;let d;for(const s of i){const a=I(s,r,n),c=oe(r,n,a);if(a){if(!(c.length>0))return a;d=a}}return d},"findNonClusterChild"),G=m(e=>l.has(e)&&l.get(e).externalConnections&&l.has(e)?l.get(e).id:e,"getAnchorId"),ae=m((e,r)=>{if(!e||r>10)o.debug("Opting out, no graph ");else{o.debug("Opting in, graph "),e.nodes().forEach(function(n){e.children(n).length>0&&(o.warn("Cluster identified",n," Replacement id in edges: ",I(n,e,n)),N.set(n,J(n,e)),l.set(n,{id:I(n,e,n),clusterData:e.node(n)}))}),e.nodes().forEach(function(n){const i=e.children(n),d=e.edges();i.length>0?(o.debug("Cluster identified",n,N),d.forEach(s=>{D(s.v,n)^D(s.w,n)&&(o.warn("Edge: ",s," leaves cluster ",n),o.warn("Descendants of XXX ",n,": ",N.get(n)),l.get(n).externalConnections=!0)})):o.debug("Not a cluster ",n,N)});for(let n of l.keys()){const i=l.get(n).id,d=e.parent(i);d!==n&&l.has(d)&&!l.get(d).externalConnections&&(l.get(n).id=d)}e.edges().forEach(function(n){const i=e.edge(n);o.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(n)),o.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(e.edge(n)));let d=n.v,s=n.w;if(o.warn("Fix XXX",l,"ids:",n.v,n.w,"Translating: ",l.get(n.v)," --- ",l.get(n.w)),l.get(n.v)||l.get(n.w)){if(o.warn("Fixing and trying - removing XXX",n.v,n.w,n.name),d=G(n.v),s=G(n.w),e.removeEdge(n.v,n.w,n.name),d!==n.v){const a=e.parent(d);l.get(a).externalConnections=!0,i.fromCluster=n.v}if(s!==n.w){const a=e.parent(s);l.get(a).externalConnections=!0,i.toCluster=n.w}o.warn("Fix Replacing with XXX",d,s,n.name),e.setEdge(d,s,i,n.name)}}),o.warn("Adjusted Graph",X(e)),R(e,0),o.trace(l)}},"adjustClustersAndEdges"),R=m((e,r)=>{var d,s;if(o.warn("extractor - ",r,X(e),e.children("D")),r>10)return void o.error("Bailing out");let n=e.nodes(),i=!1;for(const a of n){const c=e.children(a);i=i||c.length>0}if(i){o.debug("Nodes = ",n,r);for(const a of n)if(o.debug("Extracting node",a,l,l.has(a)&&!l.get(a).externalConnections,!e.parent(a),e.node(a),e.children("D")," Depth ",r),l.has(a))if(!l.get(a).externalConnections&&e.children(a)&&e.children(a).length>0){o.warn("Cluster without external connections, without a parent and with children",a,r);let c=e.graph().rankdir==="TB"?"LR":"TB";(s=(d=l.get(a))==null?void 0:d.clusterData)!=null&&s.dir&&(c=l.get(a).clusterData.dir,o.warn("Fixing dir",l.get(a).clusterData.dir,c));const p=new k({multigraph:!0,compound:!0}).setGraph({rankdir:c,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});o.warn("Old graph before copy",X(e)),A(a,e,p,a),e.setNode(a,{clusterNode:!0,id:a,clusterData:l.get(a).clusterData,label:l.get(a).label,graph:p}),o.warn("New graph after copy node: (",a,")",X(p)),o.debug("Old graph after copy",X(e))}else o.warn("Cluster ** ",a," **not meeting the criteria !externalConnections:",!l.get(a).externalConnections," no parent: ",!e.parent(a)," children ",e.children(a)&&e.children(a).length>0,e.children("D"),r),o.debug(l);else o.debug("Not a cluster",a,r);n=e.nodes(),o.warn("New list of nodes",n);for(const a of n){const c=e.node(a);o.warn(" Now next level",a,c),c!=null&&c.clusterNode&&R(c.graph,r+1)}}else o.debug("Done, no node has children",e.nodes())},"extractor"),T=m((e,r)=>{if(r.length===0)return[];let n=Object.assign([],r);return r.forEach(i=>{const d=e.children(i),s=T(e,d);n=[...n,...s]}),n},"sorter"),ie=m(e=>T(e,e.children()),"sortNodesByHierarchy"),F=m(async(e,r,n,i,d,s)=>{o.warn("Graph in recursive render:XAX",X(r),d);const a=r.graph().rankdir;o.trace("Dir in recursive render - dir:",a);const c=e.insert("g").attr("class","root");r.nodes()?o.info("Recursive render XXX",r.nodes()):o.info("No nodes found for",r),r.edges().length>0&&o.info("Recursive edges",r.edge(r.edges()[0]));const p=c.insert("g").attr("class","clusters"),E=c.insert("g").attr("class","edgePaths"),C=c.insert("g").attr("class","edgeLabels"),f=c.insert("g").attr("class","nodes");await Promise.all(r.nodes().map(async function(g){const t=r.node(g);if(d!==void 0){const u=JSON.parse(JSON.stringify(d.clusterData));o.trace(`Setting data for parent cluster XXX
 Node.id = `,g,`
 data=`,u.height,`
Parent cluster`,d.height),r.setNode(d.id,u),r.parent(g)||(o.trace("Setting parent",g,d.id),r.setParent(g,d.id,u))}if(o.info("(Insert) Node XXX"+g+": "+JSON.stringify(r.node(g))),t==null?void 0:t.clusterNode){o.info("Cluster identified XBX",g,t.width,r.node(g));const{ranksep:u,nodesep:w}=r.graph();t.graph.setGraph({...t.graph.graph(),ranksep:u+25,nodesep:w});const b=await F(f,t.graph,n,i,r.node(g),s),x=b.elem;z(t,x),t.diff=b.diff||0,o.info("New compound node after recursive render XAX",g,"width",t.width,"height",t.height),_(x,t)}else r.children(g).length>0?(o.trace("Cluster - the non recursive path XBX",g,t.id,t,t.width,"Graph:",r),o.trace(I(t.id,r)),l.set(t.id,{id:I(t.id,r),node:t})):(o.trace("Node - the non recursive path XAX",g,f,r.node(g),a),await q(f,r.node(g),{config:s,dir:a}))})),await m(async()=>{const g=r.edges().map(async function(t){const u=r.edge(t.v,t.w,t.name);o.info("Edge "+t.v+" -> "+t.w+": "+JSON.stringify(t)),o.info("Edge "+t.v+" -> "+t.w+": ",t," ",JSON.stringify(r.edge(t))),o.info("Fix",l,"ids:",t.v,t.w,"Translating: ",l.get(t.v),l.get(t.w)),await W(C,u)});await Promise.all(g)},"processEdges")(),o.info("Graph before layout:",JSON.stringify(X(r))),o.info("############################################# XXX"),o.info("###                Layout                 ### XXX"),o.info("############################################# XXX"),Z(r),o.info("Graph after layout:",JSON.stringify(X(r)));let y=0,{subGraphTitleTotalMargin:v}=K(s);return await Promise.all(ie(r).map(async function(g){var u;const t=r.node(g);if(o.info("Position XBX => "+g+": ("+t.x,","+t.y,") width: ",t.width," height: ",t.height),t==null?void 0:t.clusterNode)t.y+=v,o.info("A tainted cluster node XBX1",g,t.id,t.width,t.height,t.x,t.y,r.parent(g)),l.get(t.id).node=t,O(t);else if(r.children(g).length>0){o.info("A pure cluster node XBX1",g,t.id,t.x,t.y,t.width,t.height,r.parent(g)),t.height+=v,r.node(t.parentId);const w=(t==null?void 0:t.padding)/2||0,b=((u=t==null?void 0:t.labelBBox)==null?void 0:u.height)||0,x=b-w||0;o.debug("OffsetY",x,"labelHeight",b,"halfPadding",w),await Q(p,t),l.get(t.id).node=t}else{const w=r.node(t.parentId);t.y+=v/2,o.info("A regular node XBX1 - using the padding",t.id,"parent",t.parentId,t.width,t.height,t.x,t.y,"offsetY",t.offsetY,"parent",w,w==null?void 0:w.offsetY,t),O(t)}})),r.edges().forEach(function(g){const t=r.edge(g);o.info("Edge "+g.v+" -> "+g.w+": "+JSON.stringify(t),t),t.points.forEach(x=>x.y+=v/2);const u=r.node(g.v);var w=r.node(g.w);const b=U(E,t,l,n,u,w,i);V(t,b)}),r.nodes().forEach(function(g){const t=r.node(g);o.info(g,t.type,t.diff),t.isGroup&&(y=t.diff)}),o.warn("Returning from recursive render XAX",c,y),{elem:c,diff:y}},"recursiveRender"),Ve=m(async(e,r)=>{var s,a,c,p,E,C;const n=new k({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:((s=e.config)==null?void 0:s.nodeSpacing)||((c=(a=e.config)==null?void 0:a.flowchart)==null?void 0:c.nodeSpacing)||e.nodeSpacing,ranksep:((p=e.config)==null?void 0:p.rankSpacing)||((C=(E=e.config)==null?void 0:E.flowchart)==null?void 0:C.rankSpacing)||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),i=r.select("g");M(i,e.markers,e.type,e.diagramId),L(),j(),Y(),re(),e.nodes.forEach(f=>{n.setNode(f.id,{...f}),f.parentId&&n.setParent(f.id,f.parentId)}),o.debug("Edges:",e.edges),e.edges.forEach(f=>{if(f.start===f.end){const h=f.start,y=h+"---"+h+"---1",v=h+"---"+h+"---2",g=n.node(h);n.setNode(y,{domId:y,id:y,parentId:g.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),n.setParent(y,g.parentId),n.setNode(v,{domId:v,id:v,parentId:g.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),n.setParent(v,g.parentId);const t=structuredClone(f),u=structuredClone(f),w=structuredClone(f);t.label="",t.arrowTypeEnd="none",t.id=h+"-cyclic-special-1",u.arrowTypeEnd="none",u.id=h+"-cyclic-special-mid",w.label="",g.isGroup&&(t.fromCluster=h,w.toCluster=h),w.id=h+"-cyclic-special-2",n.setEdge(h,y,t,h+"-cyclic-special-0"),n.setEdge(y,v,u,h+"-cyclic-special-1"),n.setEdge(v,h,w,h+"-cyc<lic-special-2")}else n.setEdge(f.start,f.end,{...f},f.id)}),o.warn("Graph at first:",JSON.stringify(X(n))),ae(n),o.warn("Graph after XAX:",JSON.stringify(X(n)));const d=H();await F(i,n,e.type,e.diagramId,void 0,d)},"render");export{Ve as render};

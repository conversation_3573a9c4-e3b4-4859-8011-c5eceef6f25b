<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-BPNdLKiC.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-CL9SZpf8.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-SyQ8NwYv.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-APxr5XPC.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BIfzX7Ug.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C4xMcLhX.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-CtwQrvzD.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-SEbJxN6J.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-D2Ut0gK2.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-BQz1eaw5.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BskWw2a8.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-iuo-Ho0S.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-CRbaKECg.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-Ba7Np-LE.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-BxoMn_1r.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-bwPj7Y67.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-iwbEjzvh.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-Bh8QWD0w.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BPg3etNg.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-DUTpCop3.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-5FhabZKw.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Th-X2FgN.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-C3pDA31_.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-DoMcZLcQ.js" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-BrD4yHQ_.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-B7KScrDw.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-vCST1yxq.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DTcQ2vsq.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-CLLTFP8m.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BUhwqDnM.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-CbFzDG5Z.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-uhpSTama.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-C1h8SdqW.css" nonce="nonce-o7/wNbG2uwAVmBORayIVwQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>

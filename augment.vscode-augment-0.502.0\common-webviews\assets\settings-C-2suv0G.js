var bo=Object.defineProperty;var ko=(r,t,e)=>t in r?bo(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var Q=(r,t,e)=>ko(r,typeof t!="symbol"?t+"":t,e);import{A as At,am as Mo,an as ce,S as ct,i as dt,s as at,D as k,V as T,E as x,Y as L,c as w,e as y,f as b,F as S,Q as zt,af as Ce,q as U,r as V,u as p,t as m,h as v,G as _,T as Jn,a2 as $t,Z as ot,X as K,av as rs,N as gt,n as B,a4 as ze,b as Ot,J as It,a as wt,a1 as $n,K as Pt,L as Rt,M as Lt,g as se,a3 as gn,I as Jr,j as Dt,W as Gr,at as pe,aj as Zt,a8 as ve,ac as Ao,ad as os,H as nn,w as sn,x as rn,y as on,d as Gt,z as an,ak as Hr,a5 as _t,a6 as Ct,a7 as bt,aA as To,ai as Wr,O as Gn,aa as Kr,az as as,ae as Yr,al as is}from"./SpinnerAugment-CL9SZpf8.js";import{D as Pn,s as Rn,S as Eo,a as le,P as ln,C as No,B as Xr,T as Qr,b as Hn,c as to,d as Io,U as Po}from"./trash-can-UidoMBR_.js";import"./design-system-init-SyQ8NwYv.js";import{W as ft,a as Ut,e as mt,u as ye,o as we,I as cn,h as Mt,i as Ro,b as Lo,H as ls}from"./IconButtonAugment-C4xMcLhX.js";import{M as eo,R as Oo}from"./message-broker-SEbJxN6J.js";import{G as zo,S as Fo,a as Zo,C as jo,N as Do,J as Uo,L as Vo,b as qo,F as oe,c as Tt,M as Ee,D as Bo,d as Jo,e as Go,R as Ho,f as Wo,g as Ko,h as Yo,i as Xo,j as Qo,T as ta,k as ea,l as na}from"./mcp-logo-BBF9ZFwB.js";import{G as Ln,H as Yt,L as ht,o as ee,z as Zn,T as me,D as yt,d as On,J as mn,A as cs,b as sa,c as ra}from"./index-BAWb-tvr.js";import{V as no}from"./VSCodeCodicon-BxoMn_1r.js";import{o as oa}from"./keypress-DD1aQVr0.js";import{A as aa}from"./async-messaging-CtwQrvzD.js";import{D as ia}from"./Drawer-DIQAznt-.js";import{B as xt}from"./ButtonAugment-iwbEjzvh.js";import{T as de,a as so}from"./CardAugment-bwPj7Y67.js";import{C as kn}from"./CalloutAugment-C-hloZHD.js";import{E as la}from"./ellipsis-B3ZqaMmA.js";import{P as ca}from"./pen-to-square-DY0HDzb8.js";import{T as ro}from"./TextAreaAugment-CiMTZgUO.js";import{C as da,S as ua}from"./copy-BFy87Ryv.js";import{C as oo,a as pa,T as Wn}from"./CollapseButtonAugment-Ba7Np-LE.js";import{C as Kn,A as ma,E as fa}from"./arrow-up-right-from-square-DUrpll74.js";import{M as $a}from"./index-iuo-Ho0S.js";import{M as ga,R as ha}from"./rules-model-CkXHaFKK.js";import{C as va,E as ya}from"./chat-flags-model-LXed7yM_.js";import{R as wa}from"./RulesModeSelector-Cw1z9neV.js";import{M as ao}from"./ModalAugment-DoMcZLcQ.js";import"./BaseTextInput-BAWt2_LS.js";import"./index-D2Ut0gK2.js";import"./types-CGlLNakm.js";import"./file-paths-BPg3etNg.js";import"./index-BskWw2a8.js";const Ze={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class xa{constructor(t,e=Ze){Q(this,"timerId",null);Q(this,"currentMS");Q(this,"step",0);Q(this,"params");this.callback=t;const n={...e};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=Ze.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=Ze.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=Ze.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=Ze.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const t=this.callback();t instanceof Promise&&t.catch(e=>console.error("Error in polling callback:",e))}catch(t){console.error("Error in polling callback:",t)}}}class Sa{constructor(t){Q(this,"configs",At([]));Q(this,"pollingManager");Q(this,"_enableDebugFeatures",At(!1));Q(this,"_settingsComponentSupported",At({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));Q(this,"_enableAgentMode",At(!1));Q(this,"_enableAgentSwarmMode",At(!1));Q(this,"_hasEverUsedRemoteAgent",At(!1));Q(this,"_enableInitialOrientation",At(!1));Q(this,"_userTier",At("unknown"));Q(this,"_guidelines",At({}));this._host=t,this.pollingManager=new xa(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(t){const e=!t.isConfigured,n=t.oauthUrl;if(t.identifier.hostName===Ln.remoteToolHost){let s=t.identifier.toolId;switch(typeof s=="string"&&/^\d+$/.test(s)&&(s=Number(s)),s){case Yt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:qo,requiresAuthentication:e,authUrl:n};case Yt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Vo,requiresAuthentication:e,authUrl:n};case Yt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:Uo,requiresAuthentication:e,authUrl:n};case Yt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Do,requiresAuthentication:e,authUrl:n};case Yt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:jo,requiresAuthentication:e,authUrl:n};case Yt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Zo,requiresAuthentication:e,authUrl:n};case Yt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Fo,requiresAuthentication:e,authUrl:n};case Yt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:zo,requiresAuthentication:e,authUrl:n};case Yt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:e,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${s}`)}}else if(t.identifier.hostName===Ln.localToolHost){const s=t.identifier.toolId;switch(s){case ht.readFile:case ht.editFile:case ht.saveFile:case ht.launchProcess:case ht.killProcess:case ht.readProcess:case ht.writeProcess:case ht.listProcesses:case ht.waitProcess:case ht.openBrowser:case ht.clarify:case ht.onboardingSubAgent:case ht.strReplaceEditor:case ht.remember:case ht.diagnostics:case ht.setupScript:case ht.readTerminal:case ht.gitCommitRetrieval:case ht.memoryRetrieval:case ht.startWorkerAgent:case ht.readWorkerState:case ht.waitForWorkerAgent:case ht.sendInstructionToWorkerAgent:case ht.stopWorkerAgent:case ht.deleteWorkerAgent:case ht.readWorkerAgentEdits:case ht.beachheadSubAgent:return{displayName:t.definition.name.toString(),description:"Local tool",icon:oe,requiresAuthentication:e,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${s}`)}}else if(t.identifier.hostName===Ln.sidecarToolHost){const s=t.identifier.toolId;switch(s){case Tt.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Ee,requiresAuthentication:e,authUrl:n};case Tt.shell:return{displayName:"Shell",description:"Shell",icon:Ee,requiresAuthentication:e,authUrl:n};case Tt.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Ee,requiresAuthentication:e,authUrl:n};case Tt.view:return{displayName:"File View",description:"File Viewer",icon:Ee,requiresAuthentication:e,authUrl:n};case Tt.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Ee,requiresAuthentication:e,authUrl:n};case Tt.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Go,requiresAuthentication:e,authUrl:n};case Tt.remember:return{displayName:t.definition.name.toString(),description:"Remember",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Jo,requiresAuthentication:e,authUrl:n};case Tt.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.viewRangeUntruncated:return{displayName:t.definition.name.toString(),description:"View Range",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.searchUntruncated:return{displayName:t.definition.name.toString(),description:"Search Untruncated",icon:oe,requiresAuthentication:e,authUrl:n};case Tt.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Bo,requiresAuthentication:e,authUrl:n};case Tt.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Ee,requiresAuthentication:e,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${s}`)}}return{displayName:t.definition.name.toString(),description:t.definition.description||"",requiresAuthentication:e,authUrl:n}}handleMessageFromExtension(t){const e=t.data;switch(e.type){case ft.toolConfigInitialize:return this.createConfigsFromHostTools(e.data.hostTools,e.data.toolConfigs),e.data&&e.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(e.data.enableDebugFeatures),e.data&&e.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(e.data.settingsComponentSupported),e.data.enableAgentMode!==void 0&&this._enableAgentMode.set(e.data.enableAgentMode),e.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(e.data.enableAgentSwarmMode),e.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(e.data.hasEverUsedRemoteAgent),e.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(e.data.enableInitialOrientation),e.data.userTier!==void 0&&this._userTier.set(e.data.userTier),e.data.guidelines!==void 0&&this._guidelines.set(e.data.guidelines),!0;case ft.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(e.data.hostTools,[]).map(s=>{const o=n.find(a=>a.name===s.name);return o?{...o,displayName:s.displayName,description:s.description,icon:s.icon,requiresAuthentication:s.requiresAuthentication,authUrl:s.authUrl,isConfigured:s.isConfigured,toolApprovalConfig:s.toolApprovalConfig}:s})),!0}return!1}createConfigsFromHostTools(t,e){return t.map(n=>{const s=this.transformToolDisplay(n),o=e.find(l=>l.name===n.definition.name),a=(o==null?void 0:o.isConfigured)??!s.requiresAuthentication;return{config:(o==null?void 0:o.config)??{},configString:JSON.stringify((o==null?void 0:o.config)??{},null,2),isConfigured:a,name:n.definition.name.toString(),displayName:s.displayName,description:s.description,identifier:n.identifier,icon:s.icon,requiresAuthentication:s.requiresAuthentication,authUrl:s.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:n.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(t){return["github","linear","notion","jira","confluence","supabase"].includes(t.displayName.toLowerCase())}getDisplayableTools(){return Mo(this.configs,t=>{const e=t.filter(s=>this.isDisplayableTool(s)),n=new Map;for(const s of e)n.set(s.displayName,s);return Array.from(n.values()).sort((s,o)=>{const a={GitHub:1,Linear:2,Notion:3},l=Number.MAX_SAFE_INTEGER,i=a[s.displayName]||l,c=a[o.displayName]||l;return i<l&&c<l||i===l&&c===l?i!==c?i-c:s.displayName.localeCompare(o.displayName):i-c})})}saveConfig(t){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ft.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(t=!0){this._host.postMessage({type:ft.toolConfigGetDefinitions,data:{useCache:t}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(t){this._guidelines.update(e=>e.userGuidelines?{...e,userGuidelines:{...e.userGuidelines,contents:t,enabled:t.length>0}}:e)}updateToolApprovalConfig(t,e){this.configs.update(n=>n.map(s=>s.identifier.toolId===t.toolId&&s.identifier.hostName===t.hostName?{...s,toolApprovalConfig:e}:s))}getSettingsComponentSupported(){return this._settingsComponentSupported}}var lt,jn;(function(r){r.assertEqual=t=>t,r.assertIs=function(t){},r.assertNever=function(t){throw new Error},r.arrayToEnum=t=>{const e={};for(const n of t)e[n]=n;return e},r.getValidEnumValues=t=>{const e=r.objectKeys(t).filter(s=>typeof t[t[s]]!="number"),n={};for(const s of e)n[s]=t[s];return r.objectValues(n)},r.objectValues=t=>r.objectKeys(t).map(function(e){return t[e]}),r.objectKeys=typeof Object.keys=="function"?t=>Object.keys(t):t=>{const e=[];for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.push(n);return e},r.find=(t,e)=>{for(const n of t)if(e(n))return n},r.isInteger=typeof Number.isInteger=="function"?t=>Number.isInteger(t):t=>typeof t=="number"&&isFinite(t)&&Math.floor(t)===t,r.joinValues=function(t,e=" | "){return t.map(n=>typeof n=="string"?`'${n}'`:n).join(e)},r.jsonStringifyReplacer=(t,e)=>typeof e=="bigint"?e.toString():e})(lt||(lt={})),function(r){r.mergeShapes=(t,e)=>({...t,...e})}(jn||(jn={}));const q=lt.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Xt=r=>{switch(typeof r){case"undefined":return q.undefined;case"string":return q.string;case"number":return isNaN(r)?q.nan:q.number;case"boolean":return q.boolean;case"function":return q.function;case"bigint":return q.bigint;case"symbol":return q.symbol;case"object":return Array.isArray(r)?q.array:r===null?q.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?q.promise:typeof Map<"u"&&r instanceof Map?q.map:typeof Set<"u"&&r instanceof Set?q.set:typeof Date<"u"&&r instanceof Date?q.date:q.object;default:return q.unknown}},O=lt.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class Ft extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const e=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,e):this.__proto__=e,this.name="ZodError",this.issues=t}format(t){const e=t||function(o){return o.message},n={_errors:[]},s=o=>{for(const a of o.issues)if(a.code==="invalid_union")a.unionErrors.map(s);else if(a.code==="invalid_return_type")s(a.returnTypeError);else if(a.code==="invalid_arguments")s(a.argumentsError);else if(a.path.length===0)n._errors.push(e(a));else{let l=n,i=0;for(;i<a.path.length;){const c=a.path[i];i===a.path.length-1?(l[c]=l[c]||{_errors:[]},l[c]._errors.push(e(a))):l[c]=l[c]||{_errors:[]},l=l[c],i++}}};return s(this),n}static assert(t){if(!(t instanceof Ft))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,lt.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=e=>e.message){const e={},n=[];for(const s of this.issues)s.path.length>0?(e[s.path[0]]=e[s.path[0]]||[],e[s.path[0]].push(t(s))):n.push(t(s));return{formErrors:n,fieldErrors:e}}get formErrors(){return this.flatten()}}Ft.create=r=>new Ft(r);const Re=(r,t)=>{let e;switch(r.code){case O.invalid_type:e=r.received===q.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case O.invalid_literal:e=`Invalid literal value, expected ${JSON.stringify(r.expected,lt.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:e=`Unrecognized key(s) in object: ${lt.joinValues(r.keys,", ")}`;break;case O.invalid_union:e="Invalid input";break;case O.invalid_union_discriminator:e=`Invalid discriminator value. Expected ${lt.joinValues(r.options)}`;break;case O.invalid_enum_value:e=`Invalid enum value. Expected ${lt.joinValues(r.options)}, received '${r.received}'`;break;case O.invalid_arguments:e="Invalid function arguments";break;case O.invalid_return_type:e="Invalid function return type";break;case O.invalid_date:e="Invalid date";break;case O.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(e=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(e=`${e} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?e=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?e=`Invalid input: must end with "${r.validation.endsWith}"`:lt.assertNever(r.validation):e=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case O.too_small:e=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case O.too_big:e=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case O.custom:e="Invalid input";break;case O.invalid_intersection_types:e="Intersection results could not be merged";break;case O.not_multiple_of:e=`Number must be a multiple of ${r.multipleOf}`;break;case O.not_finite:e="Number must be finite";break;default:e=t.defaultError,lt.assertNever(r)}return{message:e}};let io=Re;function hn(){return io}const vn=r=>{const{data:t,path:e,errorMaps:n,issueData:s}=r,o=[...e,...s.path||[]],a={...s,path:o};if(s.message!==void 0)return{...s,path:o,message:s.message};let l="";const i=n.filter(c=>!!c).slice().reverse();for(const c of i)l=c(a,{data:t,defaultError:l}).message;return{...s,path:o,message:l}};function j(r,t){const e=hn(),n=vn({issueData:t,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,e,e===Re?void 0:Re].filter(s=>!!s)});r.common.issues.push(n)}class Et{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,e){const n=[];for(const s of e){if(s.status==="aborted")return X;s.status==="dirty"&&t.dirty(),n.push(s.value)}return{status:t.value,value:n}}static async mergeObjectAsync(t,e){const n=[];for(const s of e){const o=await s.key,a=await s.value;n.push({key:o,value:a})}return Et.mergeObjectSync(t,n)}static mergeObjectSync(t,e){const n={};for(const s of e){const{key:o,value:a}=s;if(o.status==="aborted"||a.status==="aborted")return X;o.status==="dirty"&&t.dirty(),a.status==="dirty"&&t.dirty(),o.value==="__proto__"||a.value===void 0&&!s.alwaysSet||(n[o.value]=a.value)}return{status:t.value,value:n}}}const X=Object.freeze({status:"aborted"}),yn=r=>({status:"dirty",value:r}),Nt=r=>({status:"valid",value:r}),Dn=r=>r.status==="aborted",Un=r=>r.status==="dirty",be=r=>r.status==="valid",Ve=r=>typeof Promise<"u"&&r instanceof Promise;function wn(r,t,e,n){if(typeof t=="function"?r!==t||!n:!t.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(r)}function lo(r,t,e,n,s){if(typeof t=="function"?r!==t||!s:!t.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(r,e),e}var H,je,De;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=t=>typeof t=="string"?{message:t}:t||{},r.toString=t=>typeof t=="string"?t:t==null?void 0:t.message}(H||(H={}));class Ht{constructor(t,e,n,s){this._cachedPath=[],this.parent=t,this.data=e,this._path=n,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ds=(r,t)=>{if(be(t))return{success:!0,data:t.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const e=new Ft(r.common.issues);return this._error=e,this._error}}};function nt(r){if(!r)return{};const{errorMap:t,invalid_type_error:e,required_error:n,description:s}=r;if(t&&(e||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(o,a)=>{var l,i;const{message:c}=r;return o.code==="invalid_enum_value"?{message:c??a.defaultError}:a.data===void 0?{message:(l=c??n)!==null&&l!==void 0?l:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(i=c??e)!==null&&i!==void 0?i:a.defaultError}},description:s}}class st{get description(){return this._def.description}_getType(t){return Xt(t.data)}_getOrReturnCtx(t,e){return e||{common:t.parent.common,data:t.data,parsedType:Xt(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new Et,ctx:{common:t.parent.common,data:t.data,parsedType:Xt(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const e=this._parse(t);if(Ve(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(t){const e=this._parse(t);return Promise.resolve(e)}parse(t,e){const n=this.safeParse(t,e);if(n.success)return n.data;throw n.error}safeParse(t,e){var n;const s={common:{issues:[],async:(n=e==null?void 0:e.async)!==null&&n!==void 0&&n,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)},o=this._parseSync({data:t,path:s.path,parent:s});return ds(s,o)}"~validate"(t){var e,n;const s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)};if(!this["~standard"].async)try{const o=this._parseSync({data:t,path:[],parent:s});return be(o)?{value:o.value}:{issues:s.common.issues}}catch(o){!((n=(e=o==null?void 0:o.message)===null||e===void 0?void 0:e.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:s}).then(o=>be(o)?{value:o.value}:{issues:s.common.issues})}async parseAsync(t,e){const n=await this.safeParseAsync(t,e);if(n.success)return n.data;throw n.error}async safeParseAsync(t,e){const n={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)},s=this._parse({data:t,path:n.path,parent:n}),o=await(Ve(s)?s:Promise.resolve(s));return ds(n,o)}refine(t,e){const n=s=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(s):e;return this._refinement((s,o)=>{const a=t(s),l=()=>o.addIssue({code:O.custom,...n(s)});return typeof Promise<"u"&&a instanceof Promise?a.then(i=>!!i||(l(),!1)):!!a||(l(),!1)})}refinement(t,e){return this._refinement((n,s)=>!!t(n)||(s.addIssue(typeof e=="function"?e(n,s):e),!1))}_refinement(t){return new qt({schema:this,typeName:Y.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Jt.create(this,this._def)}nullable(){return he.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Bt.create(this)}promise(){return Oe.create(this,this._def)}or(t){return Ge.create([this,t],this._def)}and(t){return He.create(this,t,this._def)}transform(t){return new qt({...nt(this._def),schema:this,typeName:Y.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const e=typeof t=="function"?t:()=>t;return new Xe({...nt(this._def),innerType:this,defaultValue:e,typeName:Y.ZodDefault})}brand(){return new Yn({typeName:Y.ZodBranded,type:this,...nt(this._def)})}catch(t){const e=typeof t=="function"?t:()=>t;return new Qe({...nt(this._def),innerType:this,catchValue:e,typeName:Y.ZodCatch})}describe(t){return new this.constructor({...this._def,description:t})}pipe(t){return dn.create(this,t)}readonly(){return tn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const _a=/^c[^\s-]{8,}$/i,Ca=/^[0-9a-z]+$/,ba=/^[0-9A-HJKMNP-TV-Z]{26}$/i,ka=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Ma=/^[a-z0-9_-]{21}$/i,Aa=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ta=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ea=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let zn;const Na=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ia=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Pa=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ra=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,La=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Oa=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,co="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",za=new RegExp(`^${co}$`);function uo(r){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?t=`${t}\\.\\d{${r.precision}}`:r.precision==null&&(t=`${t}(\\.\\d+)?`),t}function po(r){let t=`${co}T${uo(r)}`;const e=[];return e.push(r.local?"Z?":"Z"),r.offset&&e.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${e.join("|")})`,new RegExp(`^${t}$`)}function Fa(r,t){if(!Aa.test(r))return!1;try{const[e]=r.split("."),n=e.replace(/-/g,"+").replace(/_/g,"/").padEnd(e.length+(4-e.length%4)%4,"="),s=JSON.parse(atob(n));return typeof s=="object"&&s!==null&&!(!s.typ||!s.alg)&&(!t||s.alg===t)}catch{return!1}}function Za(r,t){return!(t!=="v4"&&t||!Ia.test(r))||!(t!=="v6"&&t||!Ra.test(r))}class Vt extends st{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==q.string){const a=this._getOrReturnCtx(t);return j(a,{code:O.invalid_type,expected:q.string,received:a.parsedType}),X}const e=new Et;let n;for(const a of this._def.checks)if(a.kind==="min")t.data.length<a.value&&(n=this._getOrReturnCtx(t,n),j(n,{code:O.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),e.dirty());else if(a.kind==="max")t.data.length>a.value&&(n=this._getOrReturnCtx(t,n),j(n,{code:O.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),e.dirty());else if(a.kind==="length"){const l=t.data.length>a.value,i=t.data.length<a.value;(l||i)&&(n=this._getOrReturnCtx(t,n),l?j(n,{code:O.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):i&&j(n,{code:O.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),e.dirty())}else if(a.kind==="email")Ea.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"email",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="emoji")zn||(zn=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),zn.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"emoji",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="uuid")ka.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"uuid",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="nanoid")Ma.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"nanoid",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="cuid")_a.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"cuid",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="cuid2")Ca.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"cuid2",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="ulid")ba.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"ulid",code:O.invalid_string,message:a.message}),e.dirty());else if(a.kind==="url")try{new URL(t.data)}catch{n=this._getOrReturnCtx(t,n),j(n,{validation:"url",code:O.invalid_string,message:a.message}),e.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"regex",code:O.invalid_string,message:a.message}),e.dirty())):a.kind==="trim"?t.data=t.data.trim():a.kind==="includes"?t.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(t,n),j(n,{code:O.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),e.dirty()):a.kind==="toLowerCase"?t.data=t.data.toLowerCase():a.kind==="toUpperCase"?t.data=t.data.toUpperCase():a.kind==="startsWith"?t.data.startsWith(a.value)||(n=this._getOrReturnCtx(t,n),j(n,{code:O.invalid_string,validation:{startsWith:a.value},message:a.message}),e.dirty()):a.kind==="endsWith"?t.data.endsWith(a.value)||(n=this._getOrReturnCtx(t,n),j(n,{code:O.invalid_string,validation:{endsWith:a.value},message:a.message}),e.dirty()):a.kind==="datetime"?po(a).test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{code:O.invalid_string,validation:"datetime",message:a.message}),e.dirty()):a.kind==="date"?za.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{code:O.invalid_string,validation:"date",message:a.message}),e.dirty()):a.kind==="time"?new RegExp(`^${uo(a)}$`).test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{code:O.invalid_string,validation:"time",message:a.message}),e.dirty()):a.kind==="duration"?Ta.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"duration",code:O.invalid_string,message:a.message}),e.dirty()):a.kind==="ip"?(s=t.data,((o=a.version)!=="v4"&&o||!Na.test(s))&&(o!=="v6"&&o||!Pa.test(s))&&(n=this._getOrReturnCtx(t,n),j(n,{validation:"ip",code:O.invalid_string,message:a.message}),e.dirty())):a.kind==="jwt"?Fa(t.data,a.alg)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"jwt",code:O.invalid_string,message:a.message}),e.dirty()):a.kind==="cidr"?Za(t.data,a.version)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"cidr",code:O.invalid_string,message:a.message}),e.dirty()):a.kind==="base64"?La.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"base64",code:O.invalid_string,message:a.message}),e.dirty()):a.kind==="base64url"?Oa.test(t.data)||(n=this._getOrReturnCtx(t,n),j(n,{validation:"base64url",code:O.invalid_string,message:a.message}),e.dirty()):lt.assertNever(a);var s,o;return{status:e.value,value:t.data}}_regex(t,e,n){return this.refinement(s=>t.test(s),{validation:e,code:O.invalid_string,...H.errToObj(n)})}_addCheck(t){return new Vt({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...H.errToObj(t)})}url(t){return this._addCheck({kind:"url",...H.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...H.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...H.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...H.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...H.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...H.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...H.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...H.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...H.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...H.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...H.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...H.errToObj(t)})}datetime(t){var e,n;return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,offset:(e=t==null?void 0:t.offset)!==null&&e!==void 0&&e,local:(n=t==null?void 0:t.local)!==null&&n!==void 0&&n,...H.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,...H.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...H.errToObj(t)})}regex(t,e){return this._addCheck({kind:"regex",regex:t,...H.errToObj(e)})}includes(t,e){return this._addCheck({kind:"includes",value:t,position:e==null?void 0:e.position,...H.errToObj(e==null?void 0:e.message)})}startsWith(t,e){return this._addCheck({kind:"startsWith",value:t,...H.errToObj(e)})}endsWith(t,e){return this._addCheck({kind:"endsWith",value:t,...H.errToObj(e)})}min(t,e){return this._addCheck({kind:"min",value:t,...H.errToObj(e)})}max(t,e){return this._addCheck({kind:"max",value:t,...H.errToObj(e)})}length(t,e){return this._addCheck({kind:"length",value:t,...H.errToObj(e)})}nonempty(t){return this.min(1,H.errToObj(t))}trim(){return new Vt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Vt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Vt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t}get maxLength(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t}}function ja(r,t){const e=(r.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,s=e>n?e:n;return parseInt(r.toFixed(s).replace(".",""))%parseInt(t.toFixed(s).replace(".",""))/Math.pow(10,s)}Vt.create=r=>{var t;return new Vt({checks:[],typeName:Y.ZodString,coerce:(t=r==null?void 0:r.coerce)!==null&&t!==void 0&&t,...nt(r)})};class fe extends st{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==q.number){const s=this._getOrReturnCtx(t);return j(s,{code:O.invalid_type,expected:q.number,received:s.parsedType}),X}let e;const n=new Et;for(const s of this._def.checks)s.kind==="int"?lt.isInteger(t.data)||(e=this._getOrReturnCtx(t,e),j(e,{code:O.invalid_type,expected:"integer",received:"float",message:s.message}),n.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(e=this._getOrReturnCtx(t,e),j(e,{code:O.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),n.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(e=this._getOrReturnCtx(t,e),j(e,{code:O.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),n.dirty()):s.kind==="multipleOf"?ja(t.data,s.value)!==0&&(e=this._getOrReturnCtx(t,e),j(e,{code:O.not_multiple_of,multipleOf:s.value,message:s.message}),n.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(e=this._getOrReturnCtx(t,e),j(e,{code:O.not_finite,message:s.message}),n.dirty()):lt.assertNever(s);return{status:n.value,value:t.data}}gte(t,e){return this.setLimit("min",t,!0,H.toString(e))}gt(t,e){return this.setLimit("min",t,!1,H.toString(e))}lte(t,e){return this.setLimit("max",t,!0,H.toString(e))}lt(t,e){return this.setLimit("max",t,!1,H.toString(e))}setLimit(t,e,n,s){return new fe({...this._def,checks:[...this._def.checks,{kind:t,value:e,inclusive:n,message:H.toString(s)}]})}_addCheck(t){return new fe({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:H.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:H.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:H.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:H.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:H.toString(t)})}multipleOf(t,e){return this._addCheck({kind:"multipleOf",value:t,message:H.toString(e)})}finite(t){return this._addCheck({kind:"finite",message:H.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:H.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:H.toString(t)})}get minValue(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t}get maxValue(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&lt.isInteger(t.value))}get isFinite(){let t=null,e=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(e===null||n.value>e)&&(e=n.value):n.kind==="max"&&(t===null||n.value<t)&&(t=n.value)}return Number.isFinite(e)&&Number.isFinite(t)}}fe.create=r=>new fe({checks:[],typeName:Y.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...nt(r)});class $e extends st{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==q.bigint)return this._getInvalidInput(t);let e;const n=new Et;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(e=this._getOrReturnCtx(t,e),j(e,{code:O.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),n.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(e=this._getOrReturnCtx(t,e),j(e,{code:O.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),n.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(e=this._getOrReturnCtx(t,e),j(e,{code:O.not_multiple_of,multipleOf:s.value,message:s.message}),n.dirty()):lt.assertNever(s);return{status:n.value,value:t.data}}_getInvalidInput(t){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.bigint,received:e.parsedType}),X}gte(t,e){return this.setLimit("min",t,!0,H.toString(e))}gt(t,e){return this.setLimit("min",t,!1,H.toString(e))}lte(t,e){return this.setLimit("max",t,!0,H.toString(e))}lt(t,e){return this.setLimit("max",t,!1,H.toString(e))}setLimit(t,e,n,s){return new $e({...this._def,checks:[...this._def.checks,{kind:t,value:e,inclusive:n,message:H.toString(s)}]})}_addCheck(t){return new $e({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:H.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:H.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:H.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:H.toString(t)})}multipleOf(t,e){return this._addCheck({kind:"multipleOf",value:t,message:H.toString(e)})}get minValue(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t}get maxValue(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t}}$e.create=r=>{var t;return new $e({checks:[],typeName:Y.ZodBigInt,coerce:(t=r==null?void 0:r.coerce)!==null&&t!==void 0&&t,...nt(r)})};class qe extends st{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==q.boolean){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.boolean,received:e.parsedType}),X}return Nt(t.data)}}qe.create=r=>new qe({typeName:Y.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...nt(r)});class ke extends st{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==q.date){const s=this._getOrReturnCtx(t);return j(s,{code:O.invalid_type,expected:q.date,received:s.parsedType}),X}if(isNaN(t.data.getTime()))return j(this._getOrReturnCtx(t),{code:O.invalid_date}),X;const e=new Et;let n;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(n=this._getOrReturnCtx(t,n),j(n,{code:O.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),e.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(n=this._getOrReturnCtx(t,n),j(n,{code:O.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),e.dirty()):lt.assertNever(s);return{status:e.value,value:new Date(t.data.getTime())}}_addCheck(t){return new ke({...this._def,checks:[...this._def.checks,t]})}min(t,e){return this._addCheck({kind:"min",value:t.getTime(),message:H.toString(e)})}max(t,e){return this._addCheck({kind:"max",value:t.getTime(),message:H.toString(e)})}get minDate(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t!=null?new Date(t):null}}ke.create=r=>new ke({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:Y.ZodDate,...nt(r)});class xn extends st{_parse(t){if(this._getType(t)!==q.symbol){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.symbol,received:e.parsedType}),X}return Nt(t.data)}}xn.create=r=>new xn({typeName:Y.ZodSymbol,...nt(r)});class Be extends st{_parse(t){if(this._getType(t)!==q.undefined){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.undefined,received:e.parsedType}),X}return Nt(t.data)}}Be.create=r=>new Be({typeName:Y.ZodUndefined,...nt(r)});class Je extends st{_parse(t){if(this._getType(t)!==q.null){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.null,received:e.parsedType}),X}return Nt(t.data)}}Je.create=r=>new Je({typeName:Y.ZodNull,...nt(r)});class Le extends st{constructor(){super(...arguments),this._any=!0}_parse(t){return Nt(t.data)}}Le.create=r=>new Le({typeName:Y.ZodAny,...nt(r)});class _e extends st{constructor(){super(...arguments),this._unknown=!0}_parse(t){return Nt(t.data)}}_e.create=r=>new _e({typeName:Y.ZodUnknown,...nt(r)});class ne extends st{_parse(t){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.never,received:e.parsedType}),X}}ne.create=r=>new ne({typeName:Y.ZodNever,...nt(r)});class Sn extends st{_parse(t){if(this._getType(t)!==q.undefined){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.void,received:e.parsedType}),X}return Nt(t.data)}}Sn.create=r=>new Sn({typeName:Y.ZodVoid,...nt(r)});class Bt extends st{_parse(t){const{ctx:e,status:n}=this._processInputParams(t),s=this._def;if(e.parsedType!==q.array)return j(e,{code:O.invalid_type,expected:q.array,received:e.parsedType}),X;if(s.exactLength!==null){const a=e.data.length>s.exactLength.value,l=e.data.length<s.exactLength.value;(a||l)&&(j(e,{code:a?O.too_big:O.too_small,minimum:l?s.exactLength.value:void 0,maximum:a?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),n.dirty())}if(s.minLength!==null&&e.data.length<s.minLength.value&&(j(e,{code:O.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),n.dirty()),s.maxLength!==null&&e.data.length>s.maxLength.value&&(j(e,{code:O.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),n.dirty()),e.common.async)return Promise.all([...e.data].map((a,l)=>s.type._parseAsync(new Ht(e,a,e.path,l)))).then(a=>Et.mergeArray(n,a));const o=[...e.data].map((a,l)=>s.type._parseSync(new Ht(e,a,e.path,l)));return Et.mergeArray(n,o)}get element(){return this._def.type}min(t,e){return new Bt({...this._def,minLength:{value:t,message:H.toString(e)}})}max(t,e){return new Bt({...this._def,maxLength:{value:t,message:H.toString(e)}})}length(t,e){return new Bt({...this._def,exactLength:{value:t,message:H.toString(e)}})}nonempty(t){return this.min(1,t)}}function Ne(r){if(r instanceof vt){const t={};for(const e in r.shape){const n=r.shape[e];t[e]=Jt.create(Ne(n))}return new vt({...r._def,shape:()=>t})}return r instanceof Bt?new Bt({...r._def,type:Ne(r.element)}):r instanceof Jt?Jt.create(Ne(r.unwrap())):r instanceof he?he.create(Ne(r.unwrap())):r instanceof Wt?Wt.create(r.items.map(t=>Ne(t))):r}Bt.create=(r,t)=>new Bt({type:r,minLength:null,maxLength:null,exactLength:null,typeName:Y.ZodArray,...nt(t)});class vt extends st{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),e=lt.objectKeys(t);return this._cached={shape:t,keys:e}}_parse(t){if(this._getType(t)!==q.object){const i=this._getOrReturnCtx(t);return j(i,{code:O.invalid_type,expected:q.object,received:i.parsedType}),X}const{status:e,ctx:n}=this._processInputParams(t),{shape:s,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof ne&&this._def.unknownKeys==="strip"))for(const i in n.data)o.includes(i)||a.push(i);const l=[];for(const i of o){const c=s[i],d=n.data[i];l.push({key:{status:"valid",value:i},value:c._parse(new Ht(n,d,n.path,i)),alwaysSet:i in n.data})}if(this._def.catchall instanceof ne){const i=this._def.unknownKeys;if(i==="passthrough")for(const c of a)l.push({key:{status:"valid",value:c},value:{status:"valid",value:n.data[c]}});else if(i==="strict")a.length>0&&(j(n,{code:O.unrecognized_keys,keys:a}),e.dirty());else if(i!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const i=this._def.catchall;for(const c of a){const d=n.data[c];l.push({key:{status:"valid",value:c},value:i._parse(new Ht(n,d,n.path,c)),alwaysSet:c in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const i=[];for(const c of l){const d=await c.key,u=await c.value;i.push({key:d,value:u,alwaysSet:c.alwaysSet})}return i}).then(i=>Et.mergeObjectSync(e,i)):Et.mergeObjectSync(e,l)}get shape(){return this._def.shape()}strict(t){return H.errToObj,new vt({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(e,n)=>{var s,o,a,l;const i=(a=(o=(s=this._def).errorMap)===null||o===void 0?void 0:o.call(s,e,n).message)!==null&&a!==void 0?a:n.defaultError;return e.code==="unrecognized_keys"?{message:(l=H.errToObj(t).message)!==null&&l!==void 0?l:i}:{message:i}}}:{}})}strip(){return new vt({...this._def,unknownKeys:"strip"})}passthrough(){return new vt({...this._def,unknownKeys:"passthrough"})}extend(t){return new vt({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new vt({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:Y.ZodObject})}setKey(t,e){return this.augment({[t]:e})}catchall(t){return new vt({...this._def,catchall:t})}pick(t){const e={};return lt.objectKeys(t).forEach(n=>{t[n]&&this.shape[n]&&(e[n]=this.shape[n])}),new vt({...this._def,shape:()=>e})}omit(t){const e={};return lt.objectKeys(this.shape).forEach(n=>{t[n]||(e[n]=this.shape[n])}),new vt({...this._def,shape:()=>e})}deepPartial(){return Ne(this)}partial(t){const e={};return lt.objectKeys(this.shape).forEach(n=>{const s=this.shape[n];t&&!t[n]?e[n]=s:e[n]=s.optional()}),new vt({...this._def,shape:()=>e})}required(t){const e={};return lt.objectKeys(this.shape).forEach(n=>{if(t&&!t[n])e[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof Jt;)s=s._def.innerType;e[n]=s}}),new vt({...this._def,shape:()=>e})}keyof(){return mo(lt.objectKeys(this.shape))}}vt.create=(r,t)=>new vt({shape:()=>r,unknownKeys:"strip",catchall:ne.create(),typeName:Y.ZodObject,...nt(t)}),vt.strictCreate=(r,t)=>new vt({shape:()=>r,unknownKeys:"strict",catchall:ne.create(),typeName:Y.ZodObject,...nt(t)}),vt.lazycreate=(r,t)=>new vt({shape:r,unknownKeys:"strip",catchall:ne.create(),typeName:Y.ZodObject,...nt(t)});class Ge extends st{_parse(t){const{ctx:e}=this._processInputParams(t),n=this._def.options;if(e.common.async)return Promise.all(n.map(async s=>{const o={...e,common:{...e.common,issues:[]},parent:null};return{result:await s._parseAsync({data:e.data,path:e.path,parent:o}),ctx:o}})).then(function(s){for(const a of s)if(a.result.status==="valid")return a.result;for(const a of s)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const o=s.map(a=>new Ft(a.ctx.common.issues));return j(e,{code:O.invalid_union,unionErrors:o}),X});{let s;const o=[];for(const l of n){const i={...e,common:{...e.common,issues:[]},parent:null},c=l._parseSync({data:e.data,path:e.path,parent:i});if(c.status==="valid")return c;c.status!=="dirty"||s||(s={result:c,ctx:i}),i.common.issues.length&&o.push(i.common.issues)}if(s)return e.common.issues.push(...s.ctx.common.issues),s.result;const a=o.map(l=>new Ft(l));return j(e,{code:O.invalid_union,unionErrors:a}),X}}get options(){return this._def.options}}Ge.create=(r,t)=>new Ge({options:r,typeName:Y.ZodUnion,...nt(t)});const ae=r=>r instanceof We?ae(r.schema):r instanceof qt?ae(r.innerType()):r instanceof Ke?[r.value]:r instanceof ge?r.options:r instanceof Ye?lt.objectValues(r.enum):r instanceof Xe?ae(r._def.innerType):r instanceof Be?[void 0]:r instanceof Je?[null]:r instanceof Jt?[void 0,...ae(r.unwrap())]:r instanceof he?[null,...ae(r.unwrap())]:r instanceof Yn||r instanceof tn?ae(r.unwrap()):r instanceof Qe?ae(r._def.innerType):[];class Mn extends st{_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==q.object)return j(e,{code:O.invalid_type,expected:q.object,received:e.parsedType}),X;const n=this.discriminator,s=e.data[n],o=this.optionsMap.get(s);return o?e.common.async?o._parseAsync({data:e.data,path:e.path,parent:e}):o._parseSync({data:e.data,path:e.path,parent:e}):(j(e,{code:O.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),X)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,e,n){const s=new Map;for(const o of e){const a=ae(o.shape[t]);if(!a.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const l of a){if(s.has(l))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(l)}`);s.set(l,o)}}return new Mn({typeName:Y.ZodDiscriminatedUnion,discriminator:t,options:e,optionsMap:s,...nt(n)})}}function Vn(r,t){const e=Xt(r),n=Xt(t);if(r===t)return{valid:!0,data:r};if(e===q.object&&n===q.object){const s=lt.objectKeys(t),o=lt.objectKeys(r).filter(l=>s.indexOf(l)!==-1),a={...r,...t};for(const l of o){const i=Vn(r[l],t[l]);if(!i.valid)return{valid:!1};a[l]=i.data}return{valid:!0,data:a}}if(e===q.array&&n===q.array){if(r.length!==t.length)return{valid:!1};const s=[];for(let o=0;o<r.length;o++){const a=Vn(r[o],t[o]);if(!a.valid)return{valid:!1};s.push(a.data)}return{valid:!0,data:s}}return e===q.date&&n===q.date&&+r==+t?{valid:!0,data:r}:{valid:!1}}class He extends st{_parse(t){const{status:e,ctx:n}=this._processInputParams(t),s=(o,a)=>{if(Dn(o)||Dn(a))return X;const l=Vn(o.value,a.value);return l.valid?((Un(o)||Un(a))&&e.dirty(),{status:e.value,value:l.data}):(j(n,{code:O.invalid_intersection_types}),X)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([o,a])=>s(o,a)):s(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}He.create=(r,t,e)=>new He({left:r,right:t,typeName:Y.ZodIntersection,...nt(e)});class Wt extends st{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==q.array)return j(n,{code:O.invalid_type,expected:q.array,received:n.parsedType}),X;if(n.data.length<this._def.items.length)return j(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),X;!this._def.rest&&n.data.length>this._def.items.length&&(j(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),e.dirty());const s=[...n.data].map((o,a)=>{const l=this._def.items[a]||this._def.rest;return l?l._parse(new Ht(n,o,n.path,a)):null}).filter(o=>!!o);return n.common.async?Promise.all(s).then(o=>Et.mergeArray(e,o)):Et.mergeArray(e,s)}get items(){return this._def.items}rest(t){return new Wt({...this._def,rest:t})}}Wt.create=(r,t)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Wt({items:r,typeName:Y.ZodTuple,rest:null,...nt(t)})};class An extends st{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==q.object)return j(n,{code:O.invalid_type,expected:q.object,received:n.parsedType}),X;const s=[],o=this._def.keyType,a=this._def.valueType;for(const l in n.data)s.push({key:o._parse(new Ht(n,l,n.path,l)),value:a._parse(new Ht(n,n.data[l],n.path,l)),alwaysSet:l in n.data});return n.common.async?Et.mergeObjectAsync(e,s):Et.mergeObjectSync(e,s)}get element(){return this._def.valueType}static create(t,e,n){return new An(e instanceof st?{keyType:t,valueType:e,typeName:Y.ZodRecord,...nt(n)}:{keyType:Vt.create(),valueType:t,typeName:Y.ZodRecord,...nt(e)})}}class _n extends st{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==q.map)return j(n,{code:O.invalid_type,expected:q.map,received:n.parsedType}),X;const s=this._def.keyType,o=this._def.valueType,a=[...n.data.entries()].map(([l,i],c)=>({key:s._parse(new Ht(n,l,n.path,[c,"key"])),value:o._parse(new Ht(n,i,n.path,[c,"value"]))}));if(n.common.async){const l=new Map;return Promise.resolve().then(async()=>{for(const i of a){const c=await i.key,d=await i.value;if(c.status==="aborted"||d.status==="aborted")return X;c.status!=="dirty"&&d.status!=="dirty"||e.dirty(),l.set(c.value,d.value)}return{status:e.value,value:l}})}{const l=new Map;for(const i of a){const c=i.key,d=i.value;if(c.status==="aborted"||d.status==="aborted")return X;c.status!=="dirty"&&d.status!=="dirty"||e.dirty(),l.set(c.value,d.value)}return{status:e.value,value:l}}}}_n.create=(r,t,e)=>new _n({valueType:t,keyType:r,typeName:Y.ZodMap,...nt(e)});class Me extends st{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==q.set)return j(n,{code:O.invalid_type,expected:q.set,received:n.parsedType}),X;const s=this._def;s.minSize!==null&&n.data.size<s.minSize.value&&(j(n,{code:O.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),e.dirty()),s.maxSize!==null&&n.data.size>s.maxSize.value&&(j(n,{code:O.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),e.dirty());const o=this._def.valueType;function a(i){const c=new Set;for(const d of i){if(d.status==="aborted")return X;d.status==="dirty"&&e.dirty(),c.add(d.value)}return{status:e.value,value:c}}const l=[...n.data.values()].map((i,c)=>o._parse(new Ht(n,i,n.path,c)));return n.common.async?Promise.all(l).then(i=>a(i)):a(l)}min(t,e){return new Me({...this._def,minSize:{value:t,message:H.toString(e)}})}max(t,e){return new Me({...this._def,maxSize:{value:t,message:H.toString(e)}})}size(t,e){return this.min(t,e).max(t,e)}nonempty(t){return this.min(1,t)}}Me.create=(r,t)=>new Me({valueType:r,minSize:null,maxSize:null,typeName:Y.ZodSet,...nt(t)});class Pe extends st{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==q.function)return j(e,{code:O.invalid_type,expected:q.function,received:e.parsedType}),X;function n(l,i){return vn({data:l,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,hn(),Re].filter(c=>!!c),issueData:{code:O.invalid_arguments,argumentsError:i}})}function s(l,i){return vn({data:l,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,hn(),Re].filter(c=>!!c),issueData:{code:O.invalid_return_type,returnTypeError:i}})}const o={errorMap:e.common.contextualErrorMap},a=e.data;if(this._def.returns instanceof Oe){const l=this;return Nt(async function(...i){const c=new Ft([]),d=await l._def.args.parseAsync(i,o).catch(f=>{throw c.addIssue(n(i,f)),c}),u=await Reflect.apply(a,this,d);return await l._def.returns._def.type.parseAsync(u,o).catch(f=>{throw c.addIssue(s(u,f)),c})})}{const l=this;return Nt(function(...i){const c=l._def.args.safeParse(i,o);if(!c.success)throw new Ft([n(i,c.error)]);const d=Reflect.apply(a,this,c.data),u=l._def.returns.safeParse(d,o);if(!u.success)throw new Ft([s(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new Pe({...this._def,args:Wt.create(t).rest(_e.create())})}returns(t){return new Pe({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,e,n){return new Pe({args:t||Wt.create([]).rest(_e.create()),returns:e||_e.create(),typeName:Y.ZodFunction,...nt(n)})}}class We extends st{get schema(){return this._def.getter()}_parse(t){const{ctx:e}=this._processInputParams(t);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}}We.create=(r,t)=>new We({getter:r,typeName:Y.ZodLazy,...nt(t)});class Ke extends st{_parse(t){if(t.data!==this._def.value){const e=this._getOrReturnCtx(t);return j(e,{received:e.data,code:O.invalid_literal,expected:this._def.value}),X}return{status:"valid",value:t.data}}get value(){return this._def.value}}function mo(r,t){return new ge({values:r,typeName:Y.ZodEnum,...nt(t)})}Ke.create=(r,t)=>new Ke({value:r,typeName:Y.ZodLiteral,...nt(t)});class ge extends st{constructor(){super(...arguments),je.set(this,void 0)}_parse(t){if(typeof t.data!="string"){const e=this._getOrReturnCtx(t),n=this._def.values;return j(e,{expected:lt.joinValues(n),received:e.parsedType,code:O.invalid_type}),X}if(wn(this,je)||lo(this,je,new Set(this._def.values)),!wn(this,je).has(t.data)){const e=this._getOrReturnCtx(t),n=this._def.values;return j(e,{received:e.data,code:O.invalid_enum_value,options:n}),X}return Nt(t.data)}get options(){return this._def.values}get enum(){const t={};for(const e of this._def.values)t[e]=e;return t}get Values(){const t={};for(const e of this._def.values)t[e]=e;return t}get Enum(){const t={};for(const e of this._def.values)t[e]=e;return t}extract(t,e=this._def){return ge.create(t,{...this._def,...e})}exclude(t,e=this._def){return ge.create(this.options.filter(n=>!t.includes(n)),{...this._def,...e})}}je=new WeakMap,ge.create=mo;class Ye extends st{constructor(){super(...arguments),De.set(this,void 0)}_parse(t){const e=lt.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(t);if(n.parsedType!==q.string&&n.parsedType!==q.number){const s=lt.objectValues(e);return j(n,{expected:lt.joinValues(s),received:n.parsedType,code:O.invalid_type}),X}if(wn(this,De)||lo(this,De,new Set(lt.getValidEnumValues(this._def.values))),!wn(this,De).has(t.data)){const s=lt.objectValues(e);return j(n,{received:n.data,code:O.invalid_enum_value,options:s}),X}return Nt(t.data)}get enum(){return this._def.values}}De=new WeakMap,Ye.create=(r,t)=>new Ye({values:r,typeName:Y.ZodNativeEnum,...nt(t)});class Oe extends st{unwrap(){return this._def.type}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==q.promise&&e.common.async===!1)return j(e,{code:O.invalid_type,expected:q.promise,received:e.parsedType}),X;const n=e.parsedType===q.promise?e.data:Promise.resolve(e.data);return Nt(n.then(s=>this._def.type.parseAsync(s,{path:e.path,errorMap:e.common.contextualErrorMap})))}}Oe.create=(r,t)=>new Oe({type:r,typeName:Y.ZodPromise,...nt(t)});class qt extends st{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Y.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:e,ctx:n}=this._processInputParams(t),s=this._def.effect||null,o={addIssue:a=>{j(n,a),a.fatal?e.abort():e.dirty()},get path(){return n.path}};if(o.addIssue=o.addIssue.bind(o),s.type==="preprocess"){const a=s.transform(n.data,o);if(n.common.async)return Promise.resolve(a).then(async l=>{if(e.value==="aborted")return X;const i=await this._def.schema._parseAsync({data:l,path:n.path,parent:n});return i.status==="aborted"?X:i.status==="dirty"||e.value==="dirty"?yn(i.value):i});{if(e.value==="aborted")return X;const l=this._def.schema._parseSync({data:a,path:n.path,parent:n});return l.status==="aborted"?X:l.status==="dirty"||e.value==="dirty"?yn(l.value):l}}if(s.type==="refinement"){const a=l=>{const i=s.refinement(l,o);if(n.common.async)return Promise.resolve(i);if(i instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return l};if(n.common.async===!1){const l=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return l.status==="aborted"?X:(l.status==="dirty"&&e.dirty(),a(l.value),{status:e.value,value:l.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(l=>l.status==="aborted"?X:(l.status==="dirty"&&e.dirty(),a(l.value).then(()=>({status:e.value,value:l.value}))))}if(s.type==="transform"){if(n.common.async===!1){const a=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!be(a))return a;const l=s.transform(a.value,o);if(l instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:l}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(a=>be(a)?Promise.resolve(s.transform(a.value,o)).then(l=>({status:e.value,value:l})):a)}lt.assertNever(s)}}qt.create=(r,t,e)=>new qt({schema:r,typeName:Y.ZodEffects,effect:t,...nt(e)}),qt.createWithPreprocess=(r,t,e)=>new qt({schema:t,effect:{type:"preprocess",transform:r},typeName:Y.ZodEffects,...nt(e)});class Jt extends st{_parse(t){return this._getType(t)===q.undefined?Nt(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Jt.create=(r,t)=>new Jt({innerType:r,typeName:Y.ZodOptional,...nt(t)});class he extends st{_parse(t){return this._getType(t)===q.null?Nt(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}he.create=(r,t)=>new he({innerType:r,typeName:Y.ZodNullable,...nt(t)});class Xe extends st{_parse(t){const{ctx:e}=this._processInputParams(t);let n=e.data;return e.parsedType===q.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:e.path,parent:e})}removeDefault(){return this._def.innerType}}Xe.create=(r,t)=>new Xe({innerType:r,typeName:Y.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...nt(t)});class Qe extends st{_parse(t){const{ctx:e}=this._processInputParams(t),n={...e,common:{...e.common,issues:[]}},s=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Ve(s)?s.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new Ft(n.common.issues)},input:n.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new Ft(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Qe.create=(r,t)=>new Qe({innerType:r,typeName:Y.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...nt(t)});class Cn extends st{_parse(t){if(this._getType(t)!==q.nan){const e=this._getOrReturnCtx(t);return j(e,{code:O.invalid_type,expected:q.nan,received:e.parsedType}),X}return{status:"valid",value:t.data}}}Cn.create=r=>new Cn({typeName:Y.ZodNaN,...nt(r)});const Da=Symbol("zod_brand");class Yn extends st{_parse(t){const{ctx:e}=this._processInputParams(t),n=e.data;return this._def.type._parse({data:n,path:e.path,parent:e})}unwrap(){return this._def.type}}class dn extends st{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?X:s.status==="dirty"?(e.dirty(),yn(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const s=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?X:s.status==="dirty"?(e.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:n.path,parent:n})}}static create(t,e){return new dn({in:t,out:e,typeName:Y.ZodPipeline})}}class tn extends st{_parse(t){const e=this._def.innerType._parse(t),n=s=>(be(s)&&(s.value=Object.freeze(s.value)),s);return Ve(e)?e.then(s=>n(s)):n(e)}unwrap(){return this._def.innerType}}function us(r,t={},e){return r?Le.create().superRefine((n,s)=>{var o,a;if(!r(n)){const l=typeof t=="function"?t(n):typeof t=="string"?{message:t}:t,i=(a=(o=l.fatal)!==null&&o!==void 0?o:e)===null||a===void 0||a,c=typeof l=="string"?{message:l}:l;s.addIssue({code:"custom",...c,fatal:i})}}):Le.create()}tn.create=(r,t)=>new tn({innerType:r,typeName:Y.ZodReadonly,...nt(t)});const Ua={object:vt.lazycreate};var Y;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(Y||(Y={}));const ps=Vt.create,ms=fe.create,Va=Cn.create,qa=$e.create,fs=qe.create,Ba=ke.create,Ja=xn.create,Ga=Be.create,Ha=Je.create,Wa=Le.create,Ka=_e.create,Ya=ne.create,Xa=Sn.create,Qa=Bt.create,ti=vt.create,ei=vt.strictCreate,ni=Ge.create,si=Mn.create,ri=He.create,oi=Wt.create,ai=An.create,ii=_n.create,li=Me.create,ci=Pe.create,di=We.create,ui=Ke.create,pi=ge.create,mi=Ye.create,fi=Oe.create,$s=qt.create,$i=Jt.create,gi=he.create,hi=qt.createWithPreprocess,vi=dn.create,yi={string:r=>Vt.create({...r,coerce:!0}),number:r=>fe.create({...r,coerce:!0}),boolean:r=>qe.create({...r,coerce:!0}),bigint:r=>$e.create({...r,coerce:!0}),date:r=>ke.create({...r,coerce:!0})},wi=X;var pt=Object.freeze({__proto__:null,defaultErrorMap:Re,setErrorMap:function(r){io=r},getErrorMap:hn,makeIssue:vn,EMPTY_PATH:[],addIssueToContext:j,ParseStatus:Et,INVALID:X,DIRTY:yn,OK:Nt,isAborted:Dn,isDirty:Un,isValid:be,isAsync:Ve,get util(){return lt},get objectUtil(){return jn},ZodParsedType:q,getParsedType:Xt,ZodType:st,datetimeRegex:po,ZodString:Vt,ZodNumber:fe,ZodBigInt:$e,ZodBoolean:qe,ZodDate:ke,ZodSymbol:xn,ZodUndefined:Be,ZodNull:Je,ZodAny:Le,ZodUnknown:_e,ZodNever:ne,ZodVoid:Sn,ZodArray:Bt,ZodObject:vt,ZodUnion:Ge,ZodDiscriminatedUnion:Mn,ZodIntersection:He,ZodTuple:Wt,ZodRecord:An,ZodMap:_n,ZodSet:Me,ZodFunction:Pe,ZodLazy:We,ZodLiteral:Ke,ZodEnum:ge,ZodNativeEnum:Ye,ZodPromise:Oe,ZodEffects:qt,ZodTransformer:qt,ZodOptional:Jt,ZodNullable:he,ZodDefault:Xe,ZodCatch:Qe,ZodNaN:Cn,BRAND:Da,ZodBranded:Yn,ZodPipeline:dn,ZodReadonly:tn,custom:us,Schema:st,ZodSchema:st,late:Ua,get ZodFirstPartyTypeKind(){return Y},coerce:yi,any:Wa,array:Qa,bigint:qa,boolean:fs,date:Ba,discriminatedUnion:si,effect:$s,enum:pi,function:ci,instanceof:(r,t={message:`Input not instance of ${r.name}`})=>us(e=>e instanceof r,t),intersection:ri,lazy:di,literal:ui,map:ii,nan:Va,nativeEnum:mi,never:Ya,null:Ha,nullable:gi,number:ms,object:ti,oboolean:()=>fs().optional(),onumber:()=>ms().optional(),optional:$i,ostring:()=>ps().optional(),pipeline:vi,preprocess:hi,promise:fi,record:ai,set:li,strictObject:ei,string:ps,symbol:Ja,transformer:$s,tuple:oi,undefined:Ga,union:ni,unknown:Ka,void:Xa,NEVER:wi,ZodIssueCode:O,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Ft});class St extends Error{constructor(t){super(t),this.name="MCPServerError",Object.setPrototypeOf(this,St.prototype)}}const Qt=pt.object({name:pt.string().optional(),title:pt.string().optional(),type:pt.enum(["stdio","http","sse"]).optional(),command:pt.string().optional(),args:pt.array(pt.union([pt.string(),pt.number(),pt.boolean()])).optional(),env:pt.record(pt.union([pt.string(),pt.number(),pt.boolean(),pt.null(),pt.undefined()])).optional(),url:pt.string().optional()}).passthrough();function jt(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function Ie(r){return(r==null?void 0:r.type)==="stdio"}function bn(r){return jt(r)?r.url:Ie(r)?r.command:""}const xi=pt.array(Qt),Si=pt.object({servers:pt.array(Qt)}),_i=pt.object({mcpServers:pt.array(Qt)}),Ci=pt.object({servers:pt.record(pt.unknown())}),bi=pt.object({mcpServers:pt.record(pt.unknown())}),ki=pt.record(pt.unknown()),Mi=Qt.refine(r=>{const t=r.command!==void 0,e=r.url!==void 0;if(!t&&!e)return!1;const n=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(s=>n.has(s))},{message:"Single server object must have valid server properties"});function Se(r){try{const t=Qt.transform(e=>{let n;if(e.type)n=e.type;else if(e.url)n="http";else{if(!e.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");n="stdio"}if(n==="http"||n==="sse"){if(!e.url)throw new Error(`${n.toUpperCase()} server must have a 'url' property`);return{type:n,name:e.name||e.title||e.url,url:e.url}}{const s=e.command||"",o=e.args?e.args.map(c=>String(c)):[];if(!s)throw new Error("Stdio server must have a 'command' property");const a=o.length>0?`${s} ${o.join(" ")}`:s,l=e.name||e.title||(s?s.split(" ")[0]:""),i=e.env?Object.fromEntries(Object.entries(e.env).filter(([c,d])=>d!=null).map(([c,d])=>[c,String(d)])):void 0;return{type:"stdio",name:l,command:a,arguments:"",useShellInterpolation:!0,env:Object.keys(i||{}).length>0?i:void 0}}}).refine(e=>!!e.name,{message:"Server must have a name",path:["name"]}).refine(e=>e.type==="http"||e.type==="sse"?!!e.url:!!e.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!t.success)throw new St(t.error.message);return t.data}catch(t){throw t instanceof Error?new St(`Invalid server configuration: ${t.message}`):new St("Invalid server configuration")}}function fo(r){try{const t=JSON.parse(r),e=pt.union([xi.transform(n=>n.map(s=>Se(s))),Si.transform(n=>n.servers.map(s=>Se(s))),_i.transform(n=>n.mcpServers.map(s=>Se(s))),Ci.transform(n=>Object.entries(n.servers).map(([s,o])=>{const a=Qt.parse(o);return Se({...a,name:a.name||s})})),bi.transform(n=>Object.entries(n.mcpServers).map(([s,o])=>{const a=Qt.parse(o);return Se({...a,name:a.name||s})})),Mi.transform(n=>[Se(n)]),ki.transform(n=>{if(!Object.values(n).some(s=>{const o=Qt.safeParse(s);return o.success&&(o.data.command!==void 0||o.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(n).map(([s,o])=>{const a=Qt.parse(o);return Se({...a,name:a.name||s})})})]).safeParse(t);if(e.success)return e.data;throw new St("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(t){throw t instanceof St?t:new St("Failed to parse MCP servers from JSON. Please check the format.")}}let Ai=class $o{constructor(t){Q(this,"servers",At([]));this.host=t,this.loadServersFromStorage()}handleMessageFromExtension(t){const e=t.data;if(e.type===ft.getStoredMCPServersResponse){const n=e.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(t){return this.importFromJSON(t)}loadServersFromStorage(){try{this.host.postMessage({type:ft.getStoredMCPServers})}catch(t){console.error("Failed to load MCP servers:",t),this.servers.set([])}}saveServers(t){try{this.host.postMessage({type:ft.setStoredMCPServers,data:t})}catch(e){throw console.error("Failed to save MCP servers:",e),new St("Failed to save MCP servers")}}getServers(){return this.servers}addServer(t){this.checkExistingServerName(t.name),this.servers.update(e=>{const n=[...e,{...t,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(t){for(const e of t)this.checkExistingServerName(e.name);this.servers.update(e=>{const n=[...e,...t.map(s=>({...s,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(t,e){const n=ce(this.servers).find(s=>s.name===t);if(n&&(n==null?void 0:n.id)!==e)throw new St(`Server name '${t}' already exists`)}updateServer(t){this.checkExistingServerName(t.name,t.id),this.servers.update(e=>{const n=e.map(s=>s.id===t.id?t:s);return this.saveServers(n),n})}deleteServer(t){this.servers.update(e=>{const n=e.filter(s=>s.id!==t);return this.saveServers(n),n})}toggleDisabledServer(t){this.servers.update(e=>{const n=e.map(s=>s.id===t?{...s,disabled:!s.disabled}:s);return this.saveServers(n),n})}static convertServerToJSON(t){if(jt(t))return JSON.stringify({mcpServers:{[t.name]:{url:t.url,type:t.type}}},null,2);{const e=t;return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}}static parseServerValidationMessages(t){const e=new Map,n=new Map;t.forEach(o=>{var l,i;const a=(l=o.tools)==null?void 0:l.filter(c=>!c.enabled).map(c=>c.definition.mcp_tool_name);o.disabled?e.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?e.set(o.id,"No tools are available for this MCP server"):a&&a.length===((i=o.tools)==null?void 0:i.length)?e.set(o.id,"All tools for this MCP server have validation errors: "+a.join(", ")):a&&a.length>0&&n.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+a.join(", "))});const s=this.parseDuplicateServerIds(t);return{errors:new Map([...e,...s]),warnings:n}}static parseDuplicateServerIds(t){const e=new Map;for(const s of t)e.has(s.name)||e.set(s.name,[]),e.get(s.name).push(s.id);const n=new Map;for(const[,s]of e)if(s.length>1)for(let o=1;o<s.length;o++)n.set(s[o],"MCP server is disabled due to duplicate server names");return n}static convertParsedServerToWebview(t){const{tools:e,...n}=t;return{...n,tools:void 0}}static parseServerConfigFromJSON(t){return fo(t).map(e=>this.convertParsedServerToWebview(e))}importFromJSON(t){try{const e=$o.parseServerConfigFromJSON(t),n=ce(this.servers),s=new Set(n.map(o=>o.name));for(const o of e){if(!o.name)throw new St("All servers must have a name.");if(s.has(o.name))throw new St(`A server with the name '${o.name}' already exists.`);s.add(o.name)}return this.servers.update(o=>{const a=[...o,...e.map(l=>({...l,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof St?e:new St("Failed to import MCP servers from JSON. Please check the format.")}}};class Ti{constructor(t){Q(this,"_terminalSettings",At({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=t,this.requestTerminalSettings()}handleMessageFromExtension(t){const e=t.data;return e.type===ft.terminalSettingsResponse&&(this._terminalSettings.set(e.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:ft.getTerminalSettings})}updateSelectedShell(t){this._terminalSettings.update(e=>({...e,selectedShell:t})),this._host.postMessage({type:ft.updateTerminalSettings,data:{selectedShell:t}})}updateStartupScript(t){this._terminalSettings.update(e=>({...e,startupScript:t})),this._host.postMessage({type:ft.updateTerminalSettings,data:{startupScript:t}})}}class qn{constructor(t){Q(this,"_soundSettings",At(Pn));Q(this,"_isLoaded",!1);Q(this,"dispose",()=>{Rn.disposeSounds()});this._msgBroker=t,this.initialize()}async refreshSettings(){try{const t=await this._msgBroker.sendToSidecar({type:ee.getSoundSettings});t.data&&this._soundSettings.set(t.data)}catch(t){console.warn("Failed to refresh sound settings:",t)}}async unlockSound(){ce(this._soundSettings).enabled&&Rn.unlockSoundForConfig(ce(this._soundSettings))}async playAgentComplete(){const t=ce(this._soundSettings);await Rn.playSound(Eo.AGENT_COMPLETE,t)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const t=await this._msgBroker.sendToSidecar({type:ee.getSoundSettings});t.data&&this._soundSettings.set(t.data),this._isLoaded=!0}catch(t){console.warn("Failed to load sound settings, using defaults:",t),this._soundSettings.set(Pn),this._isLoaded=!0}}async updateSettings(t){try{await this._msgBroker.sendToSidecar({type:ee.updateSoundSettings,data:t}),this._soundSettings.update(e=>({...e,...t}))}catch(e){throw console.error("Failed to update sound settings:",e),e}}async setEnabled(t){await this.updateSettings({enabled:t})}async setVolume(t){const e=Math.max(0,Math.min(1,t));await this.updateSettings({volume:e})}async resetToDefaults(){await this.updateSettings(Pn)}updateEnabled(t){this.setEnabled(t).catch(e=>{console.error("Failed to update enabled setting:",e)})}updateVolume(t){this.setVolume(t).catch(e=>{console.error("Failed to update volume setting:",e)})}}Q(qn,"key","soundModel");var ie;let gs=(ie=class{constructor(t){Q(this,"_swarmModeSettings",At(le));Q(this,"_isLoaded",!1);Q(this,"_pollInterval",null);Q(this,"_lastKnownSettingsHash","");Q(this,"dispose",()=>{this.stopPolling()});this._msgBroker=t,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const t=await this._msgBroker.sendToSidecar({type:ee.getSwarmModeSettings});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data)),this._isLoaded=!0}catch(t){console.warn("Failed to load swarm mode settings, using defaults:",t),this._swarmModeSettings.set(le),this._lastKnownSettingsHash=JSON.stringify(le),this._isLoaded=!0}}async updateSettings(t){try{const e=await this._msgBroker.sendToSidecar({type:ee.updateSwarmModeSettings,data:t});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data))}catch(e){throw console.error("Failed to update swarm mode settings:",e),e}}async setEnabled(t){await this.updateSettings({enabled:t})}async resetToDefaults(){await this.updateSettings(le)}updateEnabled(t){this.setEnabled(t).catch(e=>{console.error("Failed to update enabled setting:",e)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},ie.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const t=await this._msgBroker.sendToSidecar({type:ee.getSwarmModeSettings}),e=JSON.stringify(t.data);this._lastKnownSettingsHash&&e!==this._lastKnownSettingsHash&&t.data&&this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=e}catch(t){console.warn("Failed to check for swarm mode settings updates:",t)}}},Q(ie,"key","swarmModeModel"),Q(ie,"POLLING_INTERVAL_MS",5e3),ie);function en(r,t){return e=>!e.shiftKey&&e.key===r&&(t(e),!0)}var te=(r=>(r.file="file",r.folder="folder",r))(te||{});class ue{constructor(t,e){Q(this,"subscribe");Q(this,"set");Q(this,"update");Q(this,"handleMessageFromExtension",async t=>{const e=t.data;switch(e.type){case ft.wsContextSourceFoldersChanged:case ft.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case ft.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:e.data.status}))}});Q(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:ft.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);Q(this,"getChildren",async t=>(await this.asyncMsgSender.send({type:ft.wsContextGetChildrenRequest,data:{fileId:t}},1e4)).data.children.map(e=>e.type==="folder"?{...e,children:[],expanded:!1}:{...e}).sort((e,n)=>e.type===n.type?e.name.localeCompare(n.name):e.type==="folder"?-1:1));this.host=t,this.asyncMsgSender=e;const{subscribe:n,set:s,update:o}=At({sourceFolders:[],sourceTree:[],syncStatus:Zn.done});this.subscribe=n,this.set=s,this.update=o,this.getSourceFolders().then(a=>{this.update(l=>({...l,sourceFolders:a,sourceTree:ue.sourceFoldersToSourceNodes(a)}))})}async expandNode(t){t.children=await this.getChildren(t.fileId),t.expanded=!0,this.update(e=>e)}collapseNode(t){this.update(e=>(t.children=[],t.expanded=!1,e))}toggleNode(t){t.type==="folder"&&t.inclusionState!==Ut.excluded&&(t.expanded?this.collapseNode(t):this.expandNode(t))}addMoreSourceFolders(){this.host.postMessage({type:ft.wsContextAddMoreSourceFolders})}removeSourceFolder(t){this.host.postMessage({type:ft.wsContextRemoveSourceFolder,data:t})}requestRefresh(){this.host.postMessage({type:ft.wsContextUserRequestedRefresh})}async updateSourceFolders(t){let e=ce(this);const n=await this.getRefreshedSourceTree(e.sourceTree,t);this.update(s=>({...s,sourceFolders:t,sourceTree:n}))}async getRefreshedSourceTree(t,e){const n=ue.sourceFoldersToSourceNodes(e);return this.getRefreshedSourceTreeRecurse(t,n)}async getRefreshedSourceTreeRecurse(t,e){const n=new Map(t.map(s=>[JSON.stringify([s.fileId.folderRoot,s.fileId.relPath]),s]));for(let s of e){const o=ue.fileIdToString(s.fileId);if(s.type==="folder"){const a=n.get(o);a&&(s.expanded=a.type==="folder"&&a.expanded,s.expanded&&(s.children=await this.getChildren(s.fileId),s.children=await this.getRefreshedSourceTreeRecurse(a.children,s.children)))}}return e}static fileIdToString(t){return JSON.stringify([t.folderRoot,t.relPath])}static sourceFoldersToSourceNodes(t){return t.filter(e=>!e.isNestedFolder&&!e.isPending).sort((e,n)=>e.name.localeCompare(n.name)).map(e=>({name:e.name,fileId:e.fileId,children:[],expanded:!1,type:"folder",inclusionState:e.inclusionState,reason:"",trackedFileCount:e.trackedFileCount}))}}function hs(r,t,e){const n=r.slice();return n[6]=t[e],n}function vs(r){let t,e;function n(){return r[5](r[6])}return t=new cn({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[Ei]},$$scope:{ctx:r}}}),t.$on("click",function(){return r[4](r[6])}),t.$on("keyup",function(){Ce(en("Enter",n))&&en("Enter",n).apply(this,arguments)}),{c(){x(t.$$.fragment)},m(s,o){S(t,s,o),e=!0},p(s,o){r=s;const a={};512&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(s){e||(p(t.$$.fragment,s),e=!0)},o(s){m(t.$$.fragment,s),e=!1},d(s){_(t,s)}}}function Ei(r){let t,e;return t=new No({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function ys(r){let t,e;return t=new K({props:{size:1,class:"file-count",$$slots:{default:[Ni]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};513&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Ni(r){let t,e=r[6].trackedFileCount.toLocaleString()+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){1&s&&e!==(e=n[6].trackedFileCount.toLocaleString()+"")&&ot(t,e)},d(n){n&&v(t)}}}function ws(r,t){let e,n,s,o,a,l,i,c,d,u,f,$=t[6].name+"",g=(t[6].isPending?"(pending)":t[6].fileId.folderRoot)+"",h=!t[6].isWorkspaceFolder&&vs(t);s=new no({props:{class:"source-folder-v-adjust",icon:t[3](t[6])}});let C=t[6].trackedFileCount&&ys(t);return{key:r,first:null,c(){e=k("div"),h&&h.c(),n=T(),x(s.$$.fragment),o=T(),a=k("span"),l=L($),i=T(),c=k("span"),d=L(g),u=T(),C&&C.c(),w(c,"class","folderRoot svelte-1skknri"),w(a,"class","name svelte-1skknri"),w(e,"class","item svelte-1skknri"),$t(e,"workspace-folder",t[6].isWorkspaceFolder),this.first=e},m(N,P){y(N,e,P),h&&h.m(e,null),b(e,n),S(s,e,null),b(e,o),b(e,a),b(a,l),b(a,i),b(a,c),b(c,d),b(e,u),C&&C.m(e,null),f=!0},p(N,P){(t=N)[6].isWorkspaceFolder?h&&(U(),m(h,1,1,()=>{h=null}),V()):h?(h.p(t,P),1&P&&p(h,1)):(h=vs(t),h.c(),p(h,1),h.m(e,n));const A={};1&P&&(A.icon=t[3](t[6])),s.$set(A),(!f||1&P)&&$!==($=t[6].name+"")&&ot(l,$),(!f||1&P)&&g!==(g=(t[6].isPending?"(pending)":t[6].fileId.folderRoot)+"")&&ot(d,g),t[6].trackedFileCount?C?(C.p(t,P),1&P&&p(C,1)):(C=ys(t),C.c(),p(C,1),C.m(e,null)):C&&(U(),m(C,1,1,()=>{C=null}),V()),(!f||1&P)&&$t(e,"workspace-folder",t[6].isWorkspaceFolder)},i(N){f||(p(h),p(s.$$.fragment,N),p(C),f=!0)},o(N){m(h),m(s.$$.fragment,N),m(C),f=!1},d(N){N&&v(e),h&&h.d(),_(s),C&&C.d()}}}function Ii(r){let t,e,n,s,o,a,l,i,c=[],d=new Map,u=mt(r[0]);const f=$=>ue.fileIdToString($[6].fileId);for(let $=0;$<u.length;$+=1){let g=hs(r,u,$),h=f(g);d.set(h,c[$]=ws(h,g))}return s=new ln({}),{c(){t=k("div");for(let $=0;$<c.length;$+=1)c[$].c();e=T(),n=k("div"),x(s.$$.fragment),o=L(" Add more..."),w(n,"role","button"),w(n,"tabindex","0"),w(n,"class","add-more svelte-1skknri"),w(t,"class","source-folder svelte-1skknri")},m($,g){y($,t,g);for(let h=0;h<c.length;h+=1)c[h]&&c[h].m(t,null);b(t,e),b(t,n),S(s,n,null),b(n,o),a=!0,l||(i=[zt(n,"keyup",function(){Ce(en("Enter",r[1]))&&en("Enter",r[1]).apply(this,arguments)}),zt(n,"click",function(){Ce(r[1])&&r[1].apply(this,arguments)})],l=!0)},p($,[g]){r=$,13&g&&(u=mt(r[0]),U(),c=ye(c,g,f,1,r,u,d,t,we,ws,e,hs),V())},i($){if(!a){for(let g=0;g<u.length;g+=1)p(c[g]);p(s.$$.fragment,$),a=!0}},o($){for(let g=0;g<c.length;g+=1)m(c[g]);m(s.$$.fragment,$),a=!1},d($){$&&v(t);for(let g=0;g<c.length;g+=1)c[g].d();_(s),l=!1,Jn(i)}}}function Pi(r,t,e){let{folders:n=[]}=t,{onAddMore:s}=t,{onRemove:o}=t;return r.$$set=a=>{"folders"in a&&e(0,n=a.folders),"onAddMore"in a&&e(1,s=a.onAddMore),"onRemove"in a&&e(2,o=a.onRemove)},[n,s,o,a=>a.isWorkspaceFolder?"root-folder":"folder",a=>o(a.fileId.folderRoot),a=>o(a.fileId.folderRoot)]}class Ri extends ct{constructor(t){super(),dt(this,t,Pi,Ii,at,{folders:0,onAddMore:1,onRemove:2})}}function xs(r,t,e){const n=r.slice();return n[10]=t[e],n}function Ss(r){let t,e;return t=new K({props:{size:1,class:"file-count",$$slots:{default:[Li]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};8193&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Li(r){let t,e=r[0].trackedFileCount.toLocaleString()+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){1&s&&e!==(e=n[0].trackedFileCount.toLocaleString()+"")&&ot(t,e)},d(n){n&&v(t)}}}function _s(r){let t,e,n=[],s=new Map,o=mt(r[5].children);const a=l=>ue.fileIdToString(l[10].fileId);for(let l=0;l<o.length;l+=1){let i=xs(r,o,l),c=a(i);s.set(c,n[l]=Cs(c,i))}return{c(){t=k("div");for(let l=0;l<n.length;l+=1)n[l].c();w(t,"class","children-container")},m(l,i){y(l,t,i);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);e=!0},p(l,i){38&i&&(o=mt(l[5].children),U(),n=ye(n,i,a,1,l,o,s,t,we,Cs,null,xs),V())},i(l){if(!e){for(let i=0;i<o.length;i+=1)p(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)m(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d()}}}function Cs(r,t){let e,n,s;return n=new go({props:{data:t[10],wsContextModel:t[1],indentLevel:t[2]+1}}),{key:r,first:null,c(){e=gt(),x(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),S(n,o,a),s=!0},p(o,a){t=o;const l={};32&a&&(l.data=t[10]),2&a&&(l.wsContextModel=t[1]),4&a&&(l.indentLevel=t[2]+1),n.$set(l)},i(o){s||(p(n.$$.fragment,o),s=!0)},o(o){m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(n,o)}}}function Oi(r){let t,e,n,s,o,a,l,i,c,d,u,f,$,g,h,C,N,P,A=r[0].name+"";n=new no({props:{icon:r[4]}});let M=r[0].type===te.folder&&r[0].inclusionState!==Ut.excluded&&typeof r[0].trackedFileCount=="number"&&Ss(r),I=r[5]&&_s(r);return{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("span"),a=L(A),l=T(),M&&M.c(),i=T(),c=k("img"),h=T(),I&&I.c(),w(o,"class","name svelte-sympus"),rs(c.src,d=r[7][r[0].inclusionState])||w(c,"src",d),w(c,"alt",u=r[8][r[0].inclusionState]),w(e,"class","tree-item svelte-sympus"),w(e,"role","treeitem"),w(e,"aria-selected","false"),w(e,"tabindex","0"),w(e,"title",f=r[0].reason),w(e,"aria-expanded",$=r[0].type===te.folder&&r[0].expanded),w(e,"aria-level",r[2]),w(e,"style",g=`padding-left: ${10*r[2]+20}px;`),$t(e,"included-folder",r[3])},m(R,J){y(R,t,J),b(t,e),S(n,e,null),b(e,s),b(e,o),b(o,a),b(e,l),M&&M.m(e,null),b(e,i),b(e,c),b(t,h),I&&I.m(t,null),C=!0,N||(P=[zt(e,"click",r[6]),zt(e,"keyup",en("Enter",r[6]))],N=!0)},p(R,[J]){const tt={};16&J&&(tt.icon=R[4]),n.$set(tt),(!C||1&J)&&A!==(A=R[0].name+"")&&ot(a,A),R[0].type===te.folder&&R[0].inclusionState!==Ut.excluded&&typeof R[0].trackedFileCount=="number"?M?(M.p(R,J),1&J&&p(M,1)):(M=Ss(R),M.c(),p(M,1),M.m(e,i)):M&&(U(),m(M,1,1,()=>{M=null}),V()),(!C||1&J&&!rs(c.src,d=R[7][R[0].inclusionState]))&&w(c,"src",d),(!C||1&J&&u!==(u=R[8][R[0].inclusionState]))&&w(c,"alt",u),(!C||1&J&&f!==(f=R[0].reason))&&w(e,"title",f),(!C||1&J&&$!==($=R[0].type===te.folder&&R[0].expanded))&&w(e,"aria-expanded",$),(!C||4&J)&&w(e,"aria-level",R[2]),(!C||4&J&&g!==(g=`padding-left: ${10*R[2]+20}px;`))&&w(e,"style",g),(!C||8&J)&&$t(e,"included-folder",R[3]),R[5]?I?(I.p(R,J),32&J&&p(I,1)):(I=_s(R),I.c(),p(I,1),I.m(t,null)):I&&(U(),m(I,1,1,()=>{I=null}),V())},i(R){C||(p(n.$$.fragment,R),p(M),p(I),C=!0)},o(R){m(n.$$.fragment,R),m(M),m(I),C=!1},d(R){R&&v(t),_(n),M&&M.d(),I&&I.d(),N=!1,Jn(P)}}}function zi(r,t,e){let{data:n}=t,{wsContextModel:s}=t,{indentLevel:o}=t;const a={[Ut.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[Ut.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[Ut.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},l={[Ut.included]:"included",[Ut.excluded]:"excluded",[Ut.partial]:"partially included"};let i,c,d;return r.$$set=u=>{"data"in u&&e(0,n=u.data),"wsContextModel"in u&&e(1,s=u.wsContextModel),"indentLevel"in u&&e(2,o=u.indentLevel)},r.$$.update=()=>{var u;1&r.$$.dirty&&e(4,c=(u=n).type===te.folder&&u.inclusionState!==Ut.excluded?u.expanded?"chevron-down":"chevron-right":u.type===te.folder?"folder":"file"),1&r.$$.dirty&&e(3,i=n.type===te.folder&&n.inclusionState!==Ut.excluded),1&r.$$.dirty&&e(5,d=n.type===te.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,s,o,i,c,d,()=>{s.toggleNode(n)},a,l]}class go extends ct{constructor(t){super(),dt(this,t,zi,Oi,at,{data:0,wsContextModel:1,indentLevel:2})}}function bs(r,t,e){const n=r.slice();return n[3]=t[e],n}function ks(r,t){let e,n,s;return n=new go({props:{wsContextModel:t[0],data:t[3],indentLevel:0}}),{key:r,first:null,c(){e=gt(),x(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),S(n,o,a),s=!0},p(o,a){t=o;const l={};1&a&&(l.wsContextModel=t[0]),2&a&&(l.data=t[3]),n.$set(l)},i(o){s||(p(n.$$.fragment,o),s=!0)},o(o){m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(n,o)}}}function Fi(r){let t,e,n=[],s=new Map,o=mt(r[1]);const a=l=>ue.fileIdToString(l[3].fileId);for(let l=0;l<o.length;l+=1){let i=bs(r,o,l),c=a(i);s.set(c,n[l]=ks(c,i))}return{c(){t=k("div");for(let l=0;l<n.length;l+=1)n[l].c();w(t,"class","files-container svelte-8hfqhl")},m(l,i){y(l,t,i);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);e=!0},p(l,[i]){3&i&&(o=mt(l[1]),U(),n=ye(n,i,a,1,l,o,s,t,we,ks,null,bs),V())},i(l){if(!e){for(let i=0;i<o.length;i+=1)p(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)m(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d()}}}function Zi(r,t,e){let n,s=B,o=()=>(s(),s=ze(l,i=>e(2,n=i)),l);r.$$.on_destroy.push(()=>s());let a,{wsContextModel:l}=t;return o(),r.$$set=i=>{"wsContextModel"in i&&o(e(0,l=i.wsContextModel))},r.$$.update=()=>{4&r.$$.dirty&&e(1,a=n.sourceTree)},[l,a,n]}class ji extends ct{constructor(t){super(),dt(this,t,Zi,Fi,at,{wsContextModel:0})}}function Di(r){let t,e,n;return{c(){t=Ot("svg"),e=Ot("rect"),n=Ot("path"),w(e,"width","16"),w(e,"height","16"),w(e,"transform","matrix(-1 0 0 -1 16 16)"),w(e,"fill","currentColor"),w(e,"fill-opacity","0.01"),w(n,"fill-rule","evenodd"),w(n,"clip-rule","evenodd"),w(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),w(n,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 16 16"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){y(s,t,o),b(t,e),b(t,n)},p:B,i:B,o:B,d(s){s&&v(t)}}}class Ui extends ct{constructor(t){super(),dt(this,t,null,Di,at,{})}}const Vi=r=>({}),Ms=r=>({}),qi=r=>({}),As=r=>({});function Bi(r){let t;const e=r[8]["header-left"],n=It(e,r,r[10],As);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||1024&o)&&Pt(n,e,s,s[10],t?Lt(e,s[10],o,qi):Rt(s[10]),As)},i(s){t||(p(n,s),t=!0)},o(s){m(n,s),t=!1},d(s){n&&n.d(s)}}}function Ji(r){let t,e,n,s=r[0]&&Ts(r),o=r[1]&&Es(r);return{c(){s&&s.c(),t=T(),o&&o.c(),e=gt()},m(a,l){s&&s.m(a,l),y(a,t,l),o&&o.m(a,l),y(a,e,l),n=!0},p(a,l){a[0]?s?(s.p(a,l),1&l&&p(s,1)):(s=Ts(a),s.c(),p(s,1),s.m(t.parentNode,t)):s&&(U(),m(s,1,1,()=>{s=null}),V()),a[1]?o?(o.p(a,l),2&l&&p(o,1)):(o=Es(a),o.c(),p(o,1),o.m(e.parentNode,e)):o&&(U(),m(o,1,1,()=>{o=null}),V())},i(a){n||(p(s),p(o),n=!0)},o(a){m(s),m(o),n=!1},d(a){a&&(v(t),v(e)),s&&s.d(a),o&&o.d(a)}}}function Ts(r){let t,e,n;var s=r[0];return s&&(e=pe(s,{})),{c(){t=k("div"),e&&x(e.$$.fragment),w(t,"class","icon-wrapper svelte-13uht7n")},m(o,a){y(o,t,a),e&&S(e,t,null),n=!0},p(o,a){if(1&a&&s!==(s=o[0])){if(e){U();const l=e;m(l.$$.fragment,1,0,()=>{_(l,1)}),V()}s?(e=pe(s,{}),x(e.$$.fragment),p(e.$$.fragment,1),S(e,t,null)):e=null}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&v(t),e&&_(e)}}}function Es(r){let t,e;return t=new K({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Gi]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};1026&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Gi(r){let t;return{c(){t=L(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n&&ot(t,e[1])},d(e){e&&v(t)}}}function Ns(r){let t,e;const n=r[8].default,s=It(n,r,r[10],null);return{c(){t=k("div"),s&&s.c(),w(t,"class","settings-card-body")},m(o,a){y(o,t,a),s&&s.m(t,null),e=!0},p(o,a){s&&s.p&&(!e||1024&a)&&Pt(s,n,o,o[10],e?Lt(n,o[10],a,null):Rt(o[10]),null)},i(o){e||(p(s,o),e=!0)},o(o){m(s,o),e=!1},d(o){o&&v(t),s&&s.d(o)}}}function Hi(r){let t,e,n,s,o,a,l,i,c,d,u;const f=[Ji,Bi],$=[];function g(M,I){return M[0]||M[1]?0:1}s=g(r),o=$[s]=f[s](r);const h=r[8]["header-right"],C=It(h,r,r[10],Ms);let N=r[5].default&&Ns(r),P=[{role:"button"},{class:r[3]},r[4]],A={};for(let M=0;M<P.length;M+=1)A=wt(A,P[M]);return{c(){t=k("div"),e=k("div"),n=k("div"),o.c(),a=T(),l=k("div"),C&&C.c(),i=T(),N&&N.c(),w(n,"class","settings-card-left svelte-13uht7n"),w(l,"class","settings-card-right svelte-13uht7n"),w(e,"class","settings-card-content svelte-13uht7n"),$n(t,A),$t(t,"clickable",r[2]),$t(t,"svelte-13uht7n",!0)},m(M,I){y(M,t,I),b(t,e),b(e,n),$[s].m(n,null),b(e,a),b(e,l),C&&C.m(l,null),b(t,i),N&&N.m(t,null),c=!0,d||(u=zt(t,"click",r[9]),d=!0)},p(M,[I]){let R=s;s=g(M),s===R?$[s].p(M,I):(U(),m($[R],1,1,()=>{$[R]=null}),V(),o=$[s],o?o.p(M,I):(o=$[s]=f[s](M),o.c()),p(o,1),o.m(n,null)),C&&C.p&&(!c||1024&I)&&Pt(C,h,M,M[10],c?Lt(h,M[10],I,Vi):Rt(M[10]),Ms),M[5].default?N?(N.p(M,I),32&I&&p(N,1)):(N=Ns(M),N.c(),p(N,1),N.m(t,null)):N&&(U(),m(N,1,1,()=>{N=null}),V()),$n(t,A=se(P,[{role:"button"},(!c||8&I)&&{class:M[3]},16&I&&M[4]])),$t(t,"clickable",M[2]),$t(t,"svelte-13uht7n",!0)},i(M){c||(p(o),p(C,M),p(N),c=!0)},o(M){m(o),m(C,M),m(N),c=!1},d(M){M&&v(t),$[s].d(),C&&C.d(M),N&&N.d(),d=!1,u()}}}function Wi(r,t,e){let n,s,o;const a=["class","icon","title","isClickable"];let l=gn(t,a),{$$slots:i={},$$scope:c}=t;const d=Jr(i);let{class:u=""}=t,{icon:f}=t,{title:$}=t,{isClickable:g=!1}=t;return r.$$set=h=>{t=wt(wt({},t),Dt(h)),e(11,l=gn(t,a)),"class"in h&&e(6,u=h.class),"icon"in h&&e(0,f=h.icon),"title"in h&&e(1,$=h.title),"isClickable"in h&&e(2,g=h.isClickable),"$$scope"in h&&e(10,c=h.$$scope)},r.$$.update=()=>{e(7,{class:n,...s}=l,n,(e(4,s),e(11,l))),192&r.$$.dirty&&e(3,o=`settings-card ${u} ${n||""}`)},[f,$,g,o,s,d,u,n,i,function(h){Gr.call(this,r,h)},c]}let ho=class extends ct{constructor(r){super(),dt(this,r,Wi,Hi,at,{class:6,icon:0,title:1,isClickable:2})}};function Ki(r){let t;return{c(){t=L("SOURCE FOLDERS")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Yi(r){let t;return{c(){t=L("FILES")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Xi(r){let t,e=r[2].toLocaleString()+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){4&s&&e!==(e=n[2].toLocaleString()+"")&&ot(t,e)},d(n){n&&v(t)}}}function Qi(r){let t,e,n,s,o,a,l,i,c,d,u,f,$,g;return n=new K({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Ki]},$$scope:{ctx:r}}}),o=new Ri({props:{folders:r[0],onRemove:r[7],onAddMore:r[8]}}),c=new K({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Yi]},$$scope:{ctx:r}}}),u=new K({props:{size:1,class:"file-count",$$slots:{default:[Xi]},$$scope:{ctx:r}}}),$=new ji({props:{wsContextModel:r[3]}}),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),x(o.$$.fragment),a=T(),l=k("div"),i=k("div"),x(c.$$.fragment),d=T(),x(u.$$.fragment),f=T(),x($.$$.fragment),w(i,"class","files-header svelte-qsnirf"),w(t,"class","context-list svelte-qsnirf")},m(h,C){y(h,t,C),b(t,e),S(n,e,null),b(e,s),S(o,e,null),b(t,a),b(t,l),b(l,i),S(c,i,null),b(i,d),S(u,i,null),b(l,f),S($,l,null),g=!0},p(h,C){const N={};512&C&&(N.$$scope={dirty:C,ctx:h}),n.$set(N);const P={};1&C&&(P.folders=h[0]),o.$set(P);const A={};512&C&&(A.$$scope={dirty:C,ctx:h}),c.$set(A);const M={};516&C&&(M.$$scope={dirty:C,ctx:h}),u.$set(M)},i(h){g||(p(n.$$.fragment,h),p(o.$$.fragment,h),p(c.$$.fragment,h),p(u.$$.fragment,h),p($.$$.fragment,h),g=!0)},o(h){m(n.$$.fragment,h),m(o.$$.fragment,h),m(c.$$.fragment,h),m(u.$$.fragment,h),m($.$$.fragment,h),g=!1},d(h){h&&v(t),_(n),_(o),_(c),_(u),_($)}}}function Is(r){let t,e;return t=new cn({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[tl]},$$scope:{ctx:r}}}),t.$on("click",r[5]),t.$on("keyup",oa("Enter",r[6])),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};512&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function tl(r){let t,e;return t=new Ho({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function el(r){let t,e,n=r[1]===Zn.done&&Is(r);return{c(){t=k("div"),n&&n.c(),w(t,"slot","header-right")},m(s,o){y(s,t,o),n&&n.m(t,null),e=!0},p(s,o){s[1]===Zn.done?n?(n.p(s,o),2&o&&p(n,1)):(n=Is(s),n.c(),p(n,1),n.m(t,null)):n&&(U(),m(n,1,1,()=>{n=null}),V())},i(s){e||(p(n),e=!0)},o(s){m(n),e=!1},d(s){s&&v(t),n&&n.d()}}}function nl(r){let t,e,n,s;return t=new ho({props:{icon:Ui,title:"Context",$$slots:{"header-right":[el],default:[Qi]},$$scope:{ctx:r}}}),t.$on("contextmenu",sl),{c(){x(t.$$.fragment)},m(o,a){S(t,o,a),e=!0,n||(s=zt(window,"message",r[3].handleMessageFromExtension),n=!0)},p(o,[a]){const l={};519&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)},i(o){e||(p(t.$$.fragment,o),e=!0)},o(o){m(t.$$.fragment,o),e=!1},d(o){_(t,o),n=!1,s()}}}const sl=r=>r.preventDefault();function rl(r,t,e){let n,s,o,a,l=new ue(Mt,new aa(Mt.postMessage));return Zt(r,l,i=>e(4,s=i)),r.$$.update=()=>{16&r.$$.dirty&&e(0,o=s.sourceFolders.sort((i,c)=>i.isWorkspaceFolder!==c.isWorkspaceFolder?i.isWorkspaceFolder?-1:1:i.fileId.folderRoot.localeCompare(c.fileId.folderRoot))),16&r.$$.dirty&&e(1,a=s.syncStatus),1&r.$$.dirty&&e(2,n=o.reduce((i,c)=>i+(c.trackedFileCount??0),0))},[o,a,n,l,s,()=>l.requestRefresh(),()=>l.requestRefresh(),i=>l.removeSourceFolder(i),()=>l.addMoreSourceFolders()]}class ol extends ct{constructor(t){super(),dt(this,t,rl,nl,at,{})}}function vo(r){return function(t){switch(typeof t){case"object":return t!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function Ps(r){return vo(r)&&"component"in r}function al(r){let t,e;return{c(){t=Ot("svg"),e=Ot("path"),w(e,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),w(e,"fill","currentColor"),w(t,"width","16"),w(t,"height","15"),w(t,"viewBox","0 0 16 15"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:B,i:B,o:B,d(n){n&&v(t)}}}class yo extends ct{constructor(t){super(),dt(this,t,null,al,at,{})}}const il=r=>({item:1&r}),Rs=r=>({item:r[0]}),ll=r=>({}),Ls=r=>({});function Os(r){var c;let t,e,n,s,o;t=new K({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[cl]},$$scope:{ctx:r}}});let a=((c=r[0])==null?void 0:c.description)&&zs(r);const l=r[1].content,i=It(l,r,r[2],Rs);return{c(){x(t.$$.fragment),e=T(),a&&a.c(),n=T(),s=k("div"),i&&i.c(),w(s,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){S(t,d,u),y(d,e,u),a&&a.m(d,u),y(d,n,u),y(d,s,u),i&&i.m(s,null),o=!0},p(d,u){var $;const f={};5&u&&(f.$$scope={dirty:u,ctx:d}),t.$set(f),($=d[0])!=null&&$.description?a?(a.p(d,u),1&u&&p(a,1)):(a=zs(d),a.c(),p(a,1),a.m(n.parentNode,n)):a&&(U(),m(a,1,1,()=>{a=null}),V()),i&&i.p&&(!o||5&u)&&Pt(i,l,d,d[2],o?Lt(l,d[2],u,il):Rt(d[2]),Rs)},i(d){o||(p(t.$$.fragment,d),p(a),p(i,d),o=!0)},o(d){m(t.$$.fragment,d),m(a),m(i,d),o=!1},d(d){d&&(v(e),v(n),v(s)),_(t,d),a&&a.d(d),i&&i.d(d)}}}function cl(r){var s;let t,e,n=((s=r[0])==null?void 0:s.name)+"";return{c(){t=k("div"),e=L(n),w(t,"class","c-navigation__content-header svelte-z0ijuz")},m(o,a){y(o,t,a),b(t,e)},p(o,a){var l;1&a&&n!==(n=((l=o[0])==null?void 0:l.name)+"")&&ot(e,n)},d(o){o&&v(t)}}}function zs(r){let t,e;return t=new K({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[dl]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};5&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function dl(r){var s;let t,e,n=((s=r[0])==null?void 0:s.description)+"";return{c(){t=k("div"),e=L(n),w(t,"class","c-navigation__content-description svelte-z0ijuz")},m(o,a){y(o,t,a),b(t,e)},p(o,a){var l;1&a&&n!==(n=((l=o[0])==null?void 0:l.description)+"")&&ot(e,n)},d(o){o&&v(t)}}}function ul(r){let t,e,n,s,o;const a=r[1].header,l=It(a,r,r[2],Ls);let i=r[0]!=null&&Os(r);return{c(){var c;t=k("div"),l&&l.c(),e=T(),n=k("div"),i&&i.c(),w(t,"class","c-navigation__content svelte-z0ijuz"),w(t,"id",s=(c=r[0])==null?void 0:c.id)},m(c,d){y(c,t,d),l&&l.m(t,null),b(t,e),b(t,n),i&&i.m(n,null),o=!0},p(c,[d]){var u;l&&l.p&&(!o||4&d)&&Pt(l,a,c,c[2],o?Lt(a,c[2],d,ll):Rt(c[2]),Ls),c[0]!=null?i?(i.p(c,d),1&d&&p(i,1)):(i=Os(c),i.c(),p(i,1),i.m(n,null)):i&&(U(),m(i,1,1,()=>{i=null}),V()),(!o||1&d&&s!==(s=(u=c[0])==null?void 0:u.id))&&w(t,"id",s)},i(c){o||(p(l,c),p(i),o=!0)},o(c){m(l,c),m(i),o=!1},d(c){c&&v(t),l&&l.d(c),i&&i.d()}}}function pl(r,t,e){let{$$slots:n={},$$scope:s}=t,{item:o}=t;return r.$$set=a=>{"item"in a&&e(0,o=a.item),"$$scope"in a&&e(2,s=a.$$scope)},[o,n,s]}class wo extends ct{constructor(t){super(),dt(this,t,pl,ul,at,{item:0})}}function ml(r,t){let e;function n({scrollTo:s,delay:o,options:a}){clearTimeout(e),s&&(e=setTimeout(()=>{r.scrollIntoView(a)},o))}return n(t),{update:n,destroy(){clearTimeout(e)}}}function Fs(r,t,e){const n=r.slice();return n[13]=t[e][0],n[14]=t[e][1],n}function Zs(r,t,e){const n=r.slice();return n[22]=t[e],n}const fl=r=>({item:32&r}),js=r=>({slot:"content",item:r[22]}),$l=r=>({label:32&r,mode:4&r}),Ds=r=>({label:r[13],mode:r[2]}),gl=r=>({item:1&r}),Us=r=>({item:r[0]});function Vs(r,t,e){const n=r.slice();return n[13]=t[e][0],n[14]=t[e][1],n}function qs(r,t,e){const n=r.slice();return n[17]=t[e],n}const hl=r=>({label:32&r,mode:4&r}),Bs=r=>({label:r[13],mode:r[2]}),vl=r=>({item:1&r,selectedId:2&r}),Js=r=>({slot:"header",item:r[0],selectedId:r[1]}),yl=r=>({item:1&r,isSelected:3&r}),Gs=r=>{var t;return{slot:"content",item:r[0],isSelected:((t=r[0])==null?void 0:t.id)===r[1]}};function wl(r){let t,e,n;const s=r[10].header,o=It(s,r,r[12],Us);let a=mt(r[5]),l=[];for(let c=0;c<a.length;c+=1)l[c]=Ws(Fs(r,a,c));const i=c=>m(l[c],1,1,()=>{l[c]=null});return{c(){t=k("div"),o&&o.c(),e=T();for(let c=0;c<l.length;c+=1)l[c].c();w(t,"class","c-navigation__flat svelte-n5ccbo")},m(c,d){y(c,t,d),o&&o.m(t,null),b(t,e);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(t,null);n=!0},p(c,d){if(o&&o.p&&(!n||4097&d)&&Pt(o,s,c,c[12],n?Lt(s,c[12],d,gl):Rt(c[12]),Us),4134&d){let u;for(a=mt(c[5]),u=0;u<a.length;u+=1){const f=Fs(c,a,u);l[u]?(l[u].p(f,d),p(l[u],1)):(l[u]=Ws(f),l[u].c(),p(l[u],1),l[u].m(t,null))}for(U(),u=a.length;u<l.length;u+=1)i(u);V()}},i(c){if(!n){p(o,c);for(let d=0;d<a.length;d+=1)p(l[d]);n=!0}},o(c){m(o,c),l=l.filter(Boolean);for(let d=0;d<l.length;d+=1)m(l[d]);n=!1},d(c){c&&v(t),o&&o.d(c),ve(l,c)}}}function xl(r){let t,e;return t=new ia({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:r[3],minimized:!1,$$slots:{right:[Tl],left:[kl]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};8&s&&(o.showButton=n[3]),4135&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Sl(r){let t,e,n,s,o,a,l=r[13]+"";return e=new yo({}),{c(){t=k("span"),x(e.$$.fragment),n=T(),s=k("span"),o=L(l),w(t,"class","c-navigation__head-icon")},m(i,c){y(i,t,c),S(e,t,null),y(i,n,c),y(i,s,c),b(s,o),a=!0},p(i,c){(!a||32&c)&&l!==(l=i[13]+"")&&ot(o,l)},i(i){a||(p(e.$$.fragment,i),a=!0)},o(i){m(e.$$.fragment,i),a=!1},d(i){i&&(v(t),v(n),v(s)),_(e)}}}function _l(r){let t;const e=r[10].content,n=It(e,r,r[12],js);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||4128&o)&&Pt(n,e,s,s[12],t?Lt(e,s[12],o,fl):Rt(s[12]),js)},i(s){t||(p(n,s),t=!0)},o(s){m(n,s),t=!1},d(s){n&&n.d(s)}}}function Hs(r){let t,e,n,s,o,a,l;return e=new wo({props:{item:r[22],$$slots:{content:[_l]},$$scope:{ctx:r}}}),{c(){t=k("span"),x(e.$$.fragment),n=T()},m(i,c){y(i,t,c),S(e,t,null),b(t,n),o=!0,a||(l=Ao(s=ml.call(null,t,{scrollTo:r[2]==="flat"&&r[22].id===r[1],delay:300,options:{behavior:"smooth"}})),a=!0)},p(i,c){r=i;const d={};32&c&&(d.item=r[22]),4128&c&&(d.$$scope={dirty:c,ctx:r}),e.$set(d),s&&Ce(s.update)&&38&c&&s.update.call(null,{scrollTo:r[2]==="flat"&&r[22].id===r[1],delay:300,options:{behavior:"smooth"}})},i(i){o||(p(e.$$.fragment,i),o=!0)},o(i){m(e.$$.fragment,i),o=!1},d(i){i&&v(t),_(e),a=!1,l()}}}function Ws(r){let t,e,n,s;const o=r[10].group,a=It(o,r,r[12],Ds),l=a||function(u){let f,$;return f=new K({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[Sl]},$$scope:{ctx:u}}}),{c(){x(f.$$.fragment)},m(g,h){S(f,g,h),$=!0},p(g,h){const C={};4128&h&&(C.$$scope={dirty:h,ctx:g}),f.$set(C)},i(g){$||(p(f.$$.fragment,g),$=!0)},o(g){m(f.$$.fragment,g),$=!1},d(g){_(f,g)}}}(r);let i=mt(r[14]),c=[];for(let u=0;u<i.length;u+=1)c[u]=Hs(Zs(r,i,u));const d=u=>m(c[u],1,1,()=>{c[u]=null});return{c(){t=k("div"),l&&l.c(),e=T();for(let u=0;u<c.length;u+=1)c[u].c();n=gt(),w(t,"class","c-navigation__head svelte-n5ccbo")},m(u,f){y(u,t,f),l&&l.m(t,null),y(u,e,f);for(let $=0;$<c.length;$+=1)c[$]&&c[$].m(u,f);y(u,n,f),s=!0},p(u,f){if(a?a.p&&(!s||4132&f)&&Pt(a,o,u,u[12],s?Lt(o,u[12],f,$l):Rt(u[12]),Ds):l&&l.p&&(!s||32&f)&&l.p(u,s?f:-1),4134&f){let $;for(i=mt(u[14]),$=0;$<i.length;$+=1){const g=Zs(u,i,$);c[$]?(c[$].p(g,f),p(c[$],1)):(c[$]=Hs(g),c[$].c(),p(c[$],1),c[$].m(n.parentNode,n))}for(U(),$=i.length;$<c.length;$+=1)d($);V()}},i(u){if(!s){p(l,u);for(let f=0;f<i.length;f+=1)p(c[f]);s=!0}},o(u){m(l,u),c=c.filter(Boolean);for(let f=0;f<c.length;f+=1)m(c[f]);s=!1},d(u){u&&(v(t),v(e),v(n)),l&&l.d(u),ve(c,u)}}}function Cl(r){let t,e=r[13]+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){32&s&&e!==(e=n[13]+"")&&ot(t,e)},d(n){n&&v(t)}}}function bl(r){let t,e,n,s,o,a=r[17].name+"";var l=r[17].icon;return l&&(e=pe(l,{})),{c(){t=k("span"),e&&x(e.$$.fragment),n=T(),s=L(a),w(t,"class","c-navigation__head-icon")},m(i,c){y(i,t,c),e&&S(e,t,null),y(i,n,c),y(i,s,c),o=!0},p(i,c){if(32&c&&l!==(l=i[17].icon)){if(e){U();const d=e;m(d.$$.fragment,1,0,()=>{_(d,1)}),V()}l?(e=pe(l,{}),x(e.$$.fragment),p(e.$$.fragment,1),S(e,t,null)):e=null}(!o||32&c)&&a!==(a=i[17].name+"")&&ot(s,a)},i(i){o||(e&&p(e.$$.fragment,i),o=!0)},o(i){e&&m(e.$$.fragment,i),o=!1},d(i){i&&(v(t),v(n),v(s)),e&&_(e)}}}function Ks(r){let t,e,n,s,o,a;function l(){return r[11](r[17])}return e=new K({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[bl]},$$scope:{ctx:r}}}),{c(){t=k("button"),x(e.$$.fragment),n=T(),w(t,"class","c-navigation__item svelte-n5ccbo"),$t(t,"is-active",r[17].id===r[1])},m(i,c){y(i,t,c),S(e,t,null),b(t,n),s=!0,o||(a=zt(t,"click",l),o=!0)},p(i,c){r=i;const d={};4128&c&&(d.$$scope={dirty:c,ctx:r}),e.$set(d),(!s||34&c)&&$t(t,"is-active",r[17].id===r[1])},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){m(e.$$.fragment,i),s=!1},d(i){i&&v(t),_(e),o=!1,a()}}}function Ys(r){let t,e,n,s,o;const a=r[10].group,l=It(a,r,r[12],Bs),i=l||function(f){let $,g,h,C,N;return g=new yo({}),C=new K({props:{size:2,color:"primary",$$slots:{default:[Cl]},$$scope:{ctx:f}}}),{c(){$=k("div"),x(g.$$.fragment),h=T(),x(C.$$.fragment),w($,"class","c-navigation__head svelte-n5ccbo")},m(P,A){y(P,$,A),S(g,$,null),b($,h),S(C,$,null),N=!0},p(P,A){const M={};4128&A&&(M.$$scope={dirty:A,ctx:P}),C.$set(M)},i(P){N||(p(g.$$.fragment,P),p(C.$$.fragment,P),N=!0)},o(P){m(g.$$.fragment,P),m(C.$$.fragment,P),N=!1},d(P){P&&v($),_(g),_(C)}}}(r);let c=mt(r[14]),d=[];for(let f=0;f<c.length;f+=1)d[f]=Ks(qs(r,c,f));const u=f=>m(d[f],1,1,()=>{d[f]=null});return{c(){t=k("div"),i&&i.c(),e=T(),n=k("div");for(let f=0;f<d.length;f+=1)d[f].c();s=T(),w(n,"class","c-navigation__items svelte-n5ccbo"),w(t,"class","c-navigation__group")},m(f,$){y(f,t,$),i&&i.m(t,null),b(t,e),b(t,n);for(let g=0;g<d.length;g+=1)d[g]&&d[g].m(n,null);b(t,s),o=!0},p(f,$){if(l?l.p&&(!o||4132&$)&&Pt(l,a,f,f[12],o?Lt(a,f[12],$,hl):Rt(f[12]),Bs):i&&i.p&&(!o||32&$)&&i.p(f,o?$:-1),98&$){let g;for(c=mt(f[14]),g=0;g<c.length;g+=1){const h=qs(f,c,g);d[g]?(d[g].p(h,$),p(d[g],1)):(d[g]=Ks(h),d[g].c(),p(d[g],1),d[g].m(n,null))}for(U(),g=c.length;g<d.length;g+=1)u(g);V()}},i(f){if(!o){p(i,f);for(let $=0;$<c.length;$+=1)p(d[$]);o=!0}},o(f){m(i,f),d=d.filter(Boolean);for(let $=0;$<d.length;$+=1)m(d[$]);o=!1},d(f){f&&v(t),i&&i.d(f),ve(d,f)}}}function Xs(r){let t,e,n=mt(r[5]),s=[];for(let a=0;a<n.length;a+=1)s[a]=Ys(Vs(r,n,a));const o=a=>m(s[a],1,1,()=>{s[a]=null});return{c(){for(let a=0;a<s.length;a+=1)s[a].c();t=gt()},m(a,l){for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(a,l);y(a,t,l),e=!0},p(a,l){if(4198&l){let i;for(n=mt(a[5]),i=0;i<n.length;i+=1){const c=Vs(a,n,i);s[i]?(s[i].p(c,l),p(s[i],1)):(s[i]=Ys(c),s[i].c(),p(s[i],1),s[i].m(t.parentNode,t))}for(U(),i=n.length;i<s.length;i+=1)o(i);V()}},i(a){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(a){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(a){a&&v(t),ve(s,a)}}}function kl(r){let t,e,n=r[1],s=Xs(r);return{c(){t=k("nav"),s.c(),w(t,"class","c-navigation__nav svelte-n5ccbo"),w(t,"slot","left")},m(o,a){y(o,t,a),s.m(t,null),e=!0},p(o,a){2&a&&at(n,n=o[1])?(U(),m(s,1,1,B),V(),s=Xs(o),s.c(),p(s,1),s.m(t,null)):s.p(o,a)},i(o){e||(p(s),e=!0)},o(o){m(s),e=!1},d(o){o&&v(t),s.d(o)}}}function Ml(r){let t;const e=r[10].header,n=It(e,r,r[12],Js);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||4099&o)&&Pt(n,e,s,s[12],t?Lt(e,s[12],o,vl):Rt(s[12]),Js)},i(s){t||(p(n,s),t=!0)},o(s){m(n,s),t=!1},d(s){n&&n.d(s)}}}function Qs(r){let t,e,n;const s=[r[0].props];var o=r[0].component;function a(l,i){let c={};for(let d=0;d<s.length;d+=1)c=wt(c,s[d]);return i!==void 0&&1&i&&(c=wt(c,se(s,[os(l[0].props)]))),{props:c}}return o&&(t=pe(o,a(r))),{c(){t&&x(t.$$.fragment),e=gt()},m(l,i){t&&S(t,l,i),y(l,e,i),n=!0},p(l,i){if(1&i&&o!==(o=l[0].component)){if(t){U();const c=t;m(c.$$.fragment,1,0,()=>{_(c,1)}),V()}o?(t=pe(o,a(l,i)),x(t.$$.fragment),p(t.$$.fragment,1),S(t,e.parentNode,e)):t=null}else if(o){const c=1&i?se(s,[os(l[0].props)]):{};t.$set(c)}},i(l){n||(t&&p(t.$$.fragment,l),n=!0)},o(l){t&&m(t.$$.fragment,l),n=!1},d(l){l&&v(e),t&&_(t,l)}}}function Al(r){let t;const e=r[10].content,n=It(e,r,r[12],Gs),s=n||function(o){let a,l,i=Ps(o[0])&&tr(o[0],o[2],o[1]),c=i&&Qs(o);return{c(){c&&c.c(),a=gt()},m(d,u){c&&c.m(d,u),y(d,a,u),l=!0},p(d,u){7&u&&(i=Ps(d[0])&&tr(d[0],d[2],d[1])),i?c?(c.p(d,u),7&u&&p(c,1)):(c=Qs(d),c.c(),p(c,1),c.m(a.parentNode,a)):c&&(U(),m(c,1,1,()=>{c=null}),V())},i(d){l||(p(c),l=!0)},o(d){m(c),l=!1},d(d){d&&v(a),c&&c.d(d)}}}(r);return{c(){s&&s.c()},m(o,a){s&&s.m(o,a),t=!0},p(o,a){n?n.p&&(!t||4099&a)&&Pt(n,e,o,o[12],t?Lt(e,o[12],a,yl):Rt(o[12]),Gs):s&&s.p&&(!t||7&a)&&s.p(o,t?a:-1)},i(o){t||(p(s,o),t=!0)},o(o){m(s,o),t=!1},d(o){s&&s.d(o)}}}function Tl(r){let t,e;return t=new wo({props:{item:r[0],slot:"right",$$slots:{content:[Al],header:[Ml]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};1&s&&(o.item=n[0]),4103&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function El(r){let t,e,n,s,o;const a=[xl,wl],l=[];function i(c,d){return c[2]==="tree"?0:1}return e=i(r),n=l[e]=a[e](r),{c(){t=k("div"),n.c(),w(t,"class",s="c-navigation c-navigation--mode__"+r[2]+" "+r[4]+" svelte-n5ccbo")},m(c,d){y(c,t,d),l[e].m(t,null),o=!0},p(c,[d]){let u=e;e=i(c),e===u?l[e].p(c,d):(U(),m(l[u],1,1,()=>{l[u]=null}),V(),n=l[e],n?n.p(c,d):(n=l[e]=a[e](c),n.c()),p(n,1),n.m(t,null)),(!o||20&d&&s!==(s="c-navigation c-navigation--mode__"+c[2]+" "+c[4]+" svelte-n5ccbo"))&&w(t,"class",s)},i(c){o||(p(n),o=!0)},o(c){m(n),o=!1},d(c){c&&v(t),l[e].d()}}}function fn(r,t,e,n,s,o){return{name:r,description:t,icon:e,id:n}}function tr(r,t,e){return t!=="tree"||(r==null?void 0:r.id)===e}function Nl(r,t,e){let{$$slots:n={},$$scope:s}=t,{group:o="Workspace Settings"}=t,{items:a=[]}=t,{item:l}=t,{mode:i="tree"}=t,{selectedId:c}=t,{onNavigationChangeItem:d=h=>{}}=t,{showButton:u=!0}=t,{class:f=""}=t,$=new Map;function g(h){e(0,l=h),e(1,c=h==null?void 0:h.id)}return r.$$set=h=>{"group"in h&&e(7,o=h.group),"items"in h&&e(8,a=h.items),"item"in h&&e(0,l=h.item),"mode"in h&&e(2,i=h.mode),"selectedId"in h&&e(1,c=h.selectedId),"onNavigationChangeItem"in h&&e(9,d=h.onNavigationChangeItem),"showButton"in h&&e(3,u=h.showButton),"class"in h&&e(4,f=h.class),"$$scope"in h&&e(12,s=h.$$scope)},r.$$.update=()=>{259&r.$$.dirty&&(c?e(0,l=a.find(h=>(h==null?void 0:h.id)===c)):e(1,c=l==null?void 0:l.id)),384&r.$$.dirty&&e(5,$=a.reduce((h,C)=>{if(!C)return h;const N=C.group??o,P=h.get(N)??[];return P.push(C),h.set(N,P),h},new Map)),257&r.$$.dirty&&(l||e(0,l=a[0])),514&r.$$.dirty&&d(c)},[l,c,i,u,f,$,g,o,a,d,n,h=>g(h),s]}class Il extends ct{constructor(t){super(),dt(this,t,Nl,El,at,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function Pl(r){let t,e;return{c(){t=Ot("svg"),e=Ot("path"),w(e,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),w(e,"fill","currentColor"),w(t,"width","16"),w(t,"height","16"),w(t,"viewBox","0 0 16 16"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:B,i:B,o:B,d(n){n&&v(t)}}}class Rl extends ct{constructor(t){super(),dt(this,t,null,Pl,at,{})}}function Ll(r){let t,e;return{c(){t=Ot("svg"),e=Ot("path"),w(e,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),w(e,"fill","currentColor"),w(t,"width","16"),w(t,"height","16"),w(t,"viewBox","0 0 16 16"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:B,i:B,o:B,d(n){n&&v(t)}}}class Ol extends ct{constructor(t){super(),dt(this,t,null,Ll,at,{})}}const zl=r=>({}),er=r=>({}),Fl=r=>({}),nr=r=>({});function Zl(r){let t;const e=r[8]["header-left"],n=It(e,r,r[10],nr);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||1024&o)&&Pt(n,e,s,s[10],t?Lt(e,s[10],o,Fl):Rt(s[10]),nr)},i(s){t||(p(n,s),t=!0)},o(s){m(n,s),t=!1},d(s){n&&n.d(s)}}}function jl(r){let t,e,n,s=r[0]&&sr(r),o=r[1]&&rr(r);return{c(){s&&s.c(),t=T(),o&&o.c(),e=gt()},m(a,l){s&&s.m(a,l),y(a,t,l),o&&o.m(a,l),y(a,e,l),n=!0},p(a,l){a[0]?s?(s.p(a,l),1&l&&p(s,1)):(s=sr(a),s.c(),p(s,1),s.m(t.parentNode,t)):s&&(U(),m(s,1,1,()=>{s=null}),V()),a[1]?o?(o.p(a,l),2&l&&p(o,1)):(o=rr(a),o.c(),p(o,1),o.m(e.parentNode,e)):o&&(U(),m(o,1,1,()=>{o=null}),V())},i(a){n||(p(s),p(o),n=!0)},o(a){m(s),m(o),n=!1},d(a){a&&(v(t),v(e)),s&&s.d(a),o&&o.d(a)}}}function sr(r){let t,e,n;var s=r[0];return s&&(e=pe(s,{})),{c(){t=k("div"),e&&x(e.$$.fragment),w(t,"class","icon-wrapper svelte-13uht7n")},m(o,a){y(o,t,a),e&&S(e,t,null),n=!0},p(o,a){if(1&a&&s!==(s=o[0])){if(e){U();const l=e;m(l.$$.fragment,1,0,()=>{_(l,1)}),V()}s?(e=pe(s,{}),x(e.$$.fragment),p(e.$$.fragment,1),S(e,t,null)):e=null}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&v(t),e&&_(e)}}}function rr(r){let t,e;return t=new K({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Dl]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};1026&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Dl(r){let t;return{c(){t=L(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n&&ot(t,e[1])},d(e){e&&v(t)}}}function or(r){let t,e;const n=r[8].default,s=It(n,r,r[10],null);return{c(){t=k("div"),s&&s.c(),w(t,"class","settings-card-body")},m(o,a){y(o,t,a),s&&s.m(t,null),e=!0},p(o,a){s&&s.p&&(!e||1024&a)&&Pt(s,n,o,o[10],e?Lt(n,o[10],a,null):Rt(o[10]),null)},i(o){e||(p(s,o),e=!0)},o(o){m(s,o),e=!1},d(o){o&&v(t),s&&s.d(o)}}}function Ul(r){let t,e,n,s,o,a,l,i,c,d,u;const f=[jl,Zl],$=[];function g(M,I){return M[0]||M[1]?0:1}s=g(r),o=$[s]=f[s](r);const h=r[8]["header-right"],C=It(h,r,r[10],er);let N=r[5].default&&or(r),P=[{role:"button"},{class:r[3]},r[4]],A={};for(let M=0;M<P.length;M+=1)A=wt(A,P[M]);return{c(){t=k("div"),e=k("div"),n=k("div"),o.c(),a=T(),l=k("div"),C&&C.c(),i=T(),N&&N.c(),w(n,"class","settings-card-left svelte-13uht7n"),w(l,"class","settings-card-right svelte-13uht7n"),w(e,"class","settings-card-content svelte-13uht7n"),$n(t,A),$t(t,"clickable",r[2]),$t(t,"svelte-13uht7n",!0)},m(M,I){y(M,t,I),b(t,e),b(e,n),$[s].m(n,null),b(e,a),b(e,l),C&&C.m(l,null),b(t,i),N&&N.m(t,null),c=!0,d||(u=zt(t,"click",r[9]),d=!0)},p(M,[I]){let R=s;s=g(M),s===R?$[s].p(M,I):(U(),m($[R],1,1,()=>{$[R]=null}),V(),o=$[s],o?o.p(M,I):(o=$[s]=f[s](M),o.c()),p(o,1),o.m(n,null)),C&&C.p&&(!c||1024&I)&&Pt(C,h,M,M[10],c?Lt(h,M[10],I,zl):Rt(M[10]),er),M[5].default?N?(N.p(M,I),32&I&&p(N,1)):(N=or(M),N.c(),p(N,1),N.m(t,null)):N&&(U(),m(N,1,1,()=>{N=null}),V()),$n(t,A=se(P,[{role:"button"},(!c||8&I)&&{class:M[3]},16&I&&M[4]])),$t(t,"clickable",M[2]),$t(t,"svelte-13uht7n",!0)},i(M){c||(p(o),p(C,M),p(N),c=!0)},o(M){m(o),m(C,M),m(N),c=!1},d(M){M&&v(t),$[s].d(),C&&C.d(M),N&&N.d(),d=!1,u()}}}function Vl(r,t,e){let n,s,o;const a=["class","icon","title","isClickable"];let l=gn(t,a),{$$slots:i={},$$scope:c}=t;const d=Jr(i);let{class:u=""}=t,{icon:f}=t,{title:$}=t,{isClickable:g=!1}=t;return r.$$set=h=>{t=wt(wt({},t),Dt(h)),e(11,l=gn(t,a)),"class"in h&&e(6,u=h.class),"icon"in h&&e(0,f=h.icon),"title"in h&&e(1,$=h.title),"isClickable"in h&&e(2,g=h.isClickable),"$$scope"in h&&e(10,c=h.$$scope)},r.$$.update=()=>{e(7,{class:n,...s}=l,n,(e(4,s),e(11,l))),192&r.$$.dirty&&e(3,o=`settings-card ${u} ${n||""}`)},[f,$,g,o,s,d,u,n,i,function(h){Gr.call(this,r,h)},c]}class Ae extends ct{constructor(t){super(),dt(this,t,Vl,Ul,at,{class:6,icon:0,title:1,isClickable:2})}}function ql(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=wt(s,n[o]);return{c(){t=Ot("svg"),e=new nn(!0),this.h()},l(o){t=sn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=rn(t);e=on(a,!0),a.forEach(v),this.h()},h(){e.a=null,Gt(t,s)},m(o,a){an(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',t)},p(o,[a]){Gt(t,s=se(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&a&&o[0]]))},i:B,o:B,d(o){o&&v(t)}}}function Bl(r,t,e){return r.$$set=n=>{e(0,t=wt(wt({},t),Dt(n)))},[t=Dt(t)]}class Jl extends ct{constructor(t){super(),dt(this,t,Bl,ql,at,{})}}function Gl(r){let t,e,n,s,o,a,l,i;return o=new de({props:{triggerOn:[so.Hover],content:"Revoke Access",$$slots:{default:[Kl]},$$scope:{ctx:r}}}),l=new Xr.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Yl]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),n=k("div"),s=k("div"),x(o.$$.fragment),a=T(),x(l.$$.fragment),w(s,"class","icon-button-wrapper svelte-js5lik"),$t(s,"active",r[3]),w(n,"class","connection-status svelte-js5lik"),w(e,"class","icon-container svelte-js5lik"),w(t,"class","status-controls svelte-js5lik")},m(c,d){y(c,t,d),b(t,e),b(e,n),b(n,s),S(o,s,null),b(e,a),S(l,e,null),i=!0},p(c,d){const u={};2051&d&&(u.$$scope={dirty:d,ctx:c}),o.$set(u),(!i||8&d)&&$t(s,"active",c[3]);const f={};2048&d&&(f.$$scope={dirty:d,ctx:c}),l.$set(f)},i(c){i||(p(o.$$.fragment,c),p(l.$$.fragment,c),i=!0)},o(c){m(o.$$.fragment,c),m(l.$$.fragment,c),i=!1},d(c){c&&v(t),_(o),_(l)}}}function Hl(r){let t,e;return t=new xt({props:{variant:"ghost-block",color:r[2]?"neutral":"accent",size:1,$$slots:{default:[tc]},$$scope:{ctx:r}}}),t.$on("click",r[4]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};4&s&&(o.color=n[2]?"neutral":"accent"),2052&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Wl(r){let t,e;return t=new Jl({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Kl(r){let t,e;return t=new cn({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[Wl]},$$scope:{ctx:r}}}),t.$on("click",r[8]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2048&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Yl(r){let t;return{c(){t=L("Connected")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Xl(r){let t;return{c(){t=k("span"),t.textContent="Connect"},m(e,n){y(e,t,n)},i:B,o:B,d(e){e&&v(t)}}}function Ql(r){let t,e,n,s,o;return e=new Hr({props:{size:1,useCurrentColor:!0}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),s=k("span"),s.textContent="Cancel",w(t,"class","connect-button-spinner svelte-js5lik")},m(a,l){y(a,t,l),S(e,t,null),y(a,n,l),y(a,s,l),o=!0},i(a){o||(p(e.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),o=!1},d(a){a&&(v(t),v(n),v(s)),_(e)}}}function tc(r){let t,e,n,s;const o=[Ql,Xl],a=[];function l(i,c){return i[2]?0:1}return e=l(r),n=a[e]=o[e](r),{c(){t=k("div"),n.c(),w(t,"class","connect-button-content svelte-js5lik")},m(i,c){y(i,t,c),a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e!==d&&(U(),m(a[d],1,1,()=>{a[d]=null}),V(),n=a[e],n||(n=a[e]=o[e](i),n.c()),p(n,1),n.m(t,null))},i(i){s||(p(n),s=!0)},o(i){m(n),s=!1},d(i){i&&v(t),a[e].d()}}}function ec(r){let t,e,n,s;const o=[Hl,Gl],a=[];function l(i,c){return!i[0].isConfigured&&i[0].authUrl?0:i[0].isConfigured?1:-1}return~(e=l(r))&&(n=a[e]=o[e](r)),{c(){t=k("div"),n&&n.c(),w(t,"slot","header-right")},m(i,c){y(i,t,c),~e&&a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e===d?~e&&a[e].p(i,c):(n&&(U(),m(a[d],1,1,()=>{a[d]=null}),V()),~e?(n=a[e],n?n.p(i,c):(n=a[e]=o[e](i),n.c()),p(n,1),n.m(t,null)):n=null)},i(i){s||(p(n),s=!0)},o(i){m(n),s=!1},d(i){i&&v(t),~e&&a[e].d()}}}function ar(r){let t,e,n,s=r[0].statusMessage+"";return{c(){t=k("div"),e=L(s),w(t,"class",n="status-message "+r[0].statusType+" svelte-js5lik")},m(o,a){y(o,t,a),b(t,e)},p(o,a){1&a&&s!==(s=o[0].statusMessage+"")&&ot(e,s),1&a&&n!==(n="status-message "+o[0].statusType+" svelte-js5lik")&&w(t,"class",n)},d(o){o&&v(t)}}}function nc(r){let t,e,n,s,o,a;e=new Ae({props:{icon:r[0].icon,title:r[0].displayName,$$slots:{"header-right":[ec]},$$scope:{ctx:r}}});let l=r[0].showStatus&&ar(r);return{c(){t=k("div"),x(e.$$.fragment),n=T(),l&&l.c(),w(t,"class","config-wrapper"),w(t,"role","group"),w(t,"aria-label","Connection status controls")},m(i,c){y(i,t,c),S(e,t,null),b(t,n),l&&l.m(t,null),s=!0,o||(a=[zt(t,"mouseenter",r[9]),zt(t,"mouseleave",r[10])],o=!0)},p(i,[c]){const d={};1&c&&(d.icon=i[0].icon),1&c&&(d.title=i[0].displayName),2063&c&&(d.$$scope={dirty:c,ctx:i}),e.$set(d),i[0].showStatus?l?l.p(i,c):(l=ar(i),l.c(),l.m(t,null)):l&&(l.d(1),l=null)},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){m(e.$$.fragment,i),s=!1},d(i){i&&v(t),_(e),l&&l.d(),o=!1,Jn(a)}}}function sc(r,t,e){let{config:n}=t,{onAuthenticate:s}=t,{onRevokeAccess:o}=t,a=!1,l=null,i=!1;return r.$$set=c=>{"config"in c&&e(0,n=c.config),"onAuthenticate"in c&&e(5,s=c.onAuthenticate),"onRevokeAccess"in c&&e(1,o=c.onRevokeAccess)},r.$$.update=()=>{133&r.$$.dirty&&n.isConfigured&&a&&(e(2,a=!1),l&&(clearTimeout(l),e(7,l=null)))},[n,o,a,i,function(){if(a)e(2,a=!1),l&&(clearTimeout(l),e(7,l=null));else{e(2,a=!0);const c=n.authUrl||"";s(c),e(7,l=setTimeout(()=>{e(2,a=!1),e(7,l=null)},6e4))}},s,()=>{},l,()=>o(n),()=>e(3,i=!0),()=>e(3,i=!1)]}class rc extends ct{constructor(t){super(),dt(this,t,sc,nc,at,{config:0,onAuthenticate:5,onRevokeAccess:1,onToolApprovalConfigChange:6})}get onToolApprovalConfigChange(){return this.$$.ctx[6]}}function oc(r){let t;return{c(){t=L(r[0])},m(e,n){y(e,t,n)},p(e,n){1&n&&ot(t,e[0])},d(e){e&&v(t)}}}function ac(r){let t,e;const n=r[2].default,s=It(n,r,r[3],null);return{c(){t=k("div"),s&&s.c(),w(t,"class","category-content")},m(o,a){y(o,t,a),s&&s.m(t,null),e=!0},p(o,a){s&&s.p&&(!e||8&a)&&Pt(s,n,o,o[3],e?Lt(n,o[3],a,null):Rt(o[3]),null)},i(o){e||(p(s,o),e=!0)},o(o){m(s,o),e=!1},d(o){o&&v(t),s&&s.d(o)}}}function ic(r){let t,e,n,s,o;return e=new Hr({props:{size:1}}),s=new K({props:{size:1,color:"secondary",$$slots:{default:[lc]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),x(s.$$.fragment),w(t,"class","loading-container svelte-2bsejd")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),S(s,t,null),o=!0},p(a,l){const i={};8&l&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(p(e.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&v(t),_(e),_(s)}}}function lc(r){let t;return{c(){t=L("Loading...")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function cc(r){let t,e,n,s,o,a,l;n=new K({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[oc]},$$scope:{ctx:r}}});const i=[ic,ac],c=[];function d(u,f){return u[1]?0:1}return o=d(r),a=c[o]=i[o](r),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),a.c(),w(e,"class","category-heading"),w(t,"class","category")},m(u,f){y(u,t,f),b(t,e),S(n,e,null),b(t,s),c[o].m(t,null),l=!0},p(u,[f]){const $={};9&f&&($.$$scope={dirty:f,ctx:u}),n.$set($);let g=o;o=d(u),o===g?c[o].p(u,f):(U(),m(c[g],1,1,()=>{c[g]=null}),V(),a=c[o],a?a.p(u,f):(a=c[o]=i[o](u),a.c()),p(a,1),a.m(t,null))},i(u){l||(p(n.$$.fragment,u),p(a),l=!0)},o(u){m(n.$$.fragment,u),m(a),l=!1},d(u){u&&v(t),_(n),c[o].d()}}}function dc(r,t,e){let{$$slots:n={},$$scope:s}=t,{title:o}=t,{loading:a=!1}=t;return r.$$set=l=>{"title"in l&&e(0,o=l.title),"loading"in l&&e(1,a=l.loading),"$$scope"in l&&e(3,s=l.$$scope)},[o,a,n,s]}class uc extends ct{constructor(t){super(),dt(this,t,dc,cc,at,{title:0,loading:1})}}function ir(r,t,e){const n=r.slice();return n[5]=t[e],n}function lr(r){let t,e;return t=new rc({props:{config:r[5],onAuthenticate:r[2],onRevokeAccess:r[3],onToolApprovalConfigChange:r[4]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2&s&&(o.config=n[5]),4&s&&(o.onAuthenticate=n[2]),8&s&&(o.onRevokeAccess=n[3]),16&s&&(o.onToolApprovalConfigChange=n[4]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function pc(r){let t,e,n=mt(r[1]),s=[];for(let a=0;a<n.length;a+=1)s[a]=lr(ir(r,n,a));const o=a=>m(s[a],1,1,()=>{s[a]=null});return{c(){t=k("div");for(let a=0;a<s.length;a+=1)s[a].c();w(t,"class","tool-category-list svelte-on3wl5")},m(a,l){y(a,t,l);for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(t,null);e=!0},p(a,l){if(30&l){let i;for(n=mt(a[1]),i=0;i<n.length;i+=1){const c=ir(a,n,i);s[i]?(s[i].p(c,l),p(s[i],1)):(s[i]=lr(c),s[i].c(),p(s[i],1),s[i].m(t,null))}for(U(),i=n.length;i<s.length;i+=1)o(i);V()}},i(a){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(a){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(a){a&&v(t),ve(s,a)}}}function mc(r){let t,e;return t=new uc({props:{title:r[0],loading:r[1].length===0,$$slots:{default:[pc]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,[s]){const o={};1&s&&(o.title=n[0]),2&s&&(o.loading=n[1].length===0),286&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function fc(r,t,e){let{title:n}=t,{tools:s=[]}=t,{onAuthenticate:o}=t,{onRevokeAccess:a}=t,{onToolApprovalConfigChange:l=()=>{}}=t;return r.$$set=i=>{"title"in i&&e(0,n=i.title),"tools"in i&&e(1,s=i.tools),"onAuthenticate"in i&&e(2,o=i.onAuthenticate),"onRevokeAccess"in i&&e(3,a=i.onRevokeAccess),"onToolApprovalConfigChange"in i&&e(4,l=i.onToolApprovalConfigChange)},[n,s,o,a,l]}class $c extends ct{constructor(t){super(),dt(this,t,fc,mc,at,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3,onToolApprovalConfigChange:4})}}class Tn{constructor(t){Q(this,"servers",At([]));this.host=t,this.loadServersFromStorage()}handleMessageFromExtension(t){const e=t.data;if(e.type===ft.getStoredMCPServersResponse){const n=e.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(t){return this.importFromJSON(t)}loadServersFromStorage(){try{this.host.postMessage({type:ft.getStoredMCPServers})}catch(t){console.error("Failed to load MCP servers:",t),this.servers.set([])}}saveServers(t){try{this.host.postMessage({type:ft.setStoredMCPServers,data:t})}catch(e){throw console.error("Failed to save MCP servers:",e),new St("Failed to save MCP servers")}}getServers(){return this.servers}addServer(t){this.checkExistingServerName(t.name),this.servers.update(e=>{const n=[...e,{...t,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(t){for(const e of t)this.checkExistingServerName(e.name);this.servers.update(e=>{const n=[...e,...t.map(s=>({...s,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(t,e){const n=ce(this.servers).find(s=>s.name===t);if(n&&(n==null?void 0:n.id)!==e)throw new St(`Server name '${t}' already exists`)}updateServer(t){this.checkExistingServerName(t.name,t.id),this.servers.update(e=>{const n=e.map(s=>s.id===t.id?t:s);return this.saveServers(n),n})}deleteServer(t){this.servers.update(e=>{const n=e.filter(s=>s.id!==t);return this.saveServers(n),n})}toggleDisabledServer(t){this.servers.update(e=>{const n=e.map(s=>s.id===t?{...s,disabled:!s.disabled}:s);return this.saveServers(n),n})}static convertServerToJSON(t){if(jt(t))return JSON.stringify({mcpServers:{[t.name]:{url:t.url,type:t.type}}},null,2);{const e=t;return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}}static parseServerValidationMessages(t){const e=new Map,n=new Map;t.forEach(o=>{var l,i;const a=(l=o.tools)==null?void 0:l.filter(c=>!c.enabled).map(c=>c.definition.mcp_tool_name);o.disabled?e.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?e.set(o.id,"No tools are available for this MCP server"):a&&a.length===((i=o.tools)==null?void 0:i.length)?e.set(o.id,"All tools for this MCP server have validation errors: "+a.join(", ")):a&&a.length>0&&n.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+a.join(", "))});const s=this.parseDuplicateServerIds(t);return{errors:new Map([...e,...s]),warnings:n}}static parseDuplicateServerIds(t){const e=new Map;for(const s of t)e.has(s.name)||e.set(s.name,[]),e.get(s.name).push(s.id);const n=new Map;for(const[,s]of e)if(s.length>1)for(let o=1;o<s.length;o++)n.set(s[o],"MCP server is disabled due to duplicate server names");return n}static convertParsedServerToWebview(t){const{tools:e,...n}=t;return{...n,tools:void 0}}static parseServerConfigFromJSON(t){return fo(t).map(e=>this.convertParsedServerToWebview(e))}importFromJSON(t){try{const e=Tn.parseServerConfigFromJSON(t),n=ce(this.servers),s=new Set(n.map(o=>o.name));for(const o of e){if(!o.name)throw new St("All servers must have a name.");if(s.has(o.name))throw new St(`A server with the name '${o.name}' already exists.`);s.add(o.name)}return this.servers.update(o=>{const a=[...o,...e.map(l=>({...l,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof St?e:new St("Failed to import MCP servers from JSON. Please check the format.")}}}function cr(r,t,e){const n=r.slice();return n[11]=t[e],n[12]=t,n[13]=e,n}function gc(r){let t;return{c(){t=L("Environment Variables")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function dr(r){let t,e,n=[],s=new Map,o=mt(r[0]);const a=l=>l[11].id;for(let l=0;l<o.length;l+=1){let i=cr(r,o,l),c=a(i);s.set(c,n[l]=ur(c,i))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();t=gt()},m(l,i){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,i);y(l,t,i),e=!0},p(l,i){59&i&&(o=mt(l[0]),U(),n=ye(n,i,a,1,l,o,s,t.parentNode,we,ur,t,cr),V())},i(l){if(!e){for(let i=0;i<o.length;i+=1)p(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)m(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d(l)}}}function hc(r){let t,e;return t=new Qr({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function vc(r){let t,e;return t=new xt({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[hc]},$$scope:{ctx:r}}}),t.$on("focus",function(){Ce(r[1])&&r[1].apply(this,arguments)}),t.$on("click",function(){return r[10](r[11])}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){r=n;const o={};16384&s&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function ur(r,t){let e,n,s,o,a,l,i,c,d,u,f,$,g;function h(A){t[6](A,t[11])}let C={size:1,placeholder:"Name",class:"full-width"};function N(A){t[8](A,t[11])}t[11].key!==void 0&&(C.value=t[11].key),s=new me({props:C}),_t.push(()=>Ct(s,"value",h)),s.$on("focus",function(){Ce(t[1])&&t[1].apply(this,arguments)}),s.$on("change",function(){return t[7](t[11])});let P={size:1,placeholder:"Value",class:"full-width"};return t[11].value!==void 0&&(P.value=t[11].value),i=new me({props:P}),_t.push(()=>Ct(i,"value",N)),i.$on("focus",function(){Ce(t[1])&&t[1].apply(this,arguments)}),i.$on("change",function(){return t[9](t[11])}),f=new de({props:{content:"Remove",$$slots:{default:[vc]},$$scope:{ctx:t}}}),{key:r,first:null,c(){e=k("tr"),n=k("td"),x(s.$$.fragment),a=T(),l=k("td"),x(i.$$.fragment),d=T(),u=k("td"),x(f.$$.fragment),$=T(),w(n,"class","name-cell svelte-1mazg1z"),w(l,"class","value-cell svelte-1mazg1z"),w(u,"class","action-cell svelte-1mazg1z"),w(e,"class","env-var-row svelte-1mazg1z"),this.first=e},m(A,M){y(A,e,M),b(e,n),S(s,n,null),b(e,a),b(e,l),S(i,l,null),b(e,d),b(e,u),S(f,u,null),b(e,$),g=!0},p(A,M){t=A;const I={};!o&&1&M&&(o=!0,I.value=t[11].key,bt(()=>o=!1)),s.$set(I);const R={};!c&&1&M&&(c=!0,R.value=t[11].value,bt(()=>c=!1)),i.$set(R);const J={};16387&M&&(J.$$scope={dirty:M,ctx:t}),f.$set(J)},i(A){g||(p(s.$$.fragment,A),p(i.$$.fragment,A),p(f.$$.fragment,A),g=!0)},o(A){m(s.$$.fragment,A),m(i.$$.fragment,A),m(f.$$.fragment,A),g=!1},d(A){A&&v(e),_(s),_(i),_(f)}}}function yc(r){let t;return{c(){t=L("Variable")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function wc(r){let t,e;return t=new ln({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function xc(r){let t,e,n,s,o,a,l,i;t=new K({props:{size:1,weight:"medium",$$slots:{default:[gc]},$$scope:{ctx:r}}});let c=r[0].length>0&&dr(r);return l=new xt({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[wc],default:[yc]},$$scope:{ctx:r}}}),l.$on("click",r[2]),{c(){x(t.$$.fragment),e=T(),n=k("table"),s=k("tbody"),c&&c.c(),o=T(),a=k("div"),x(l.$$.fragment),w(n,"class","env-vars-table svelte-1mazg1z"),w(a,"class","new-var-button-container svelte-1mazg1z")},m(d,u){S(t,d,u),y(d,e,u),y(d,n,u),b(n,s),c&&c.m(s,null),y(d,o,u),y(d,a,u),S(l,a,null),i=!0},p(d,[u]){const f={};16384&u&&(f.$$scope={dirty:u,ctx:d}),t.$set(f),d[0].length>0?c?(c.p(d,u),1&u&&p(c,1)):(c=dr(d),c.c(),p(c,1),c.m(s,null)):c&&(U(),m(c,1,1,()=>{c=null}),V());const $={};16384&u&&($.$$scope={dirty:u,ctx:d}),l.$set($)},i(d){i||(p(t.$$.fragment,d),p(c),p(l.$$.fragment,d),i=!0)},o(d){m(t.$$.fragment,d),m(c),m(l.$$.fragment,d),i=!1},d(d){d&&(v(e),v(n),v(o),v(a)),_(t,d),c&&c.d(),_(l)}}}function Sc(r,t,e){let{handleEnterEditMode:n}=t,{envVarEntries:s=[]}=t;function o(i){n(),e(0,s=s.filter(c=>c.id!==i))}function a(i,c){const d=s.findIndex(u=>u.id===i);d!==-1&&(e(0,s[d].key=c,s),e(0,s))}function l(i,c){const d=s.findIndex(u=>u.id===i);d!==-1&&(e(0,s[d].value=c,s),e(0,s))}return r.$$set=i=>{"handleEnterEditMode"in i&&e(1,n=i.handleEnterEditMode),"envVarEntries"in i&&e(0,s=i.envVarEntries)},[s,n,function(){n(),e(0,s=[...s,{id:crypto.randomUUID(),key:"",value:""}])},o,a,l,function(i,c){r.$$.not_equal(c.key,i)&&(c.key=i,e(0,s))},i=>a(i.id,i.key),function(i,c){r.$$.not_equal(c.value,i)&&(c.value=i,e(0,s))},i=>l(i.id,i.value),i=>o(i.id)]}class _c extends ct{constructor(t){super(),dt(this,t,Sc,xc,at,{handleEnterEditMode:1,envVarEntries:0})}}function Cc(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=wt(s,n[o]);return{c(){t=Ot("svg"),e=new nn(!0),this.h()},l(o){t=sn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=rn(t);e=on(a,!0),a.forEach(v),this.h()},h(){e.a=null,Gt(t,s)},m(o,a){an(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M305 239c9.4 9.4 9.4 24.6 0 33.9L113 465c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l175-175L79 81c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"/>',t)},p(o,[a]){Gt(t,s=se(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&a&&o[0]]))},i:B,o:B,d(o){o&&v(t)}}}function bc(r,t,e){return r.$$set=n=>{e(0,t=wt(wt({},t),Dt(n)))},[t=Dt(t)]}class kc extends ct{constructor(t){super(),dt(this,t,bc,Cc,at,{})}}function pr(r,t,e){const n=r.slice();return n[45]=t[e],n}function Mc(r){let t,e,n,s,o,a,l,i,c,d,u,f,$,g,h,C,N,P,A,M,I,R,J,tt,et,E=(r[0]==="add"||r[0]==="edit")&&!jt(r[3]);a=new Wo({}),i=new K({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[Tc]},$$scope:{ctx:r}}});const z=[Nc,Ec],D=[];function rt(G,F){return G[0]==="addJson"?0:G[0]==="add"||G[0]==="addRemote"||G[0]==="edit"?1:-1}~(d=rt(r))&&(u=D[d]=z[d](r));let ut=E&&fr(r);return C=new kn({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Bc],default:[qc]},$$scope:{ctx:r}}}),A=new xt({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[Jc]},$$scope:{ctx:r}}}),A.$on("click",r[22]),I=new xt({props:{size:1,variant:"solid",color:"accent",loading:r[2],type:"submit",disabled:r[17],$$slots:{default:[Yc]},$$scope:{ctx:r}}}),{c(){t=k("form"),e=k("div"),n=k("div"),s=k("div"),o=k("div"),x(a.$$.fragment),l=T(),x(i.$$.fragment),c=T(),u&&u.c(),f=T(),ut&&ut.c(),$=T(),g=k("div"),h=k("div"),x(C.$$.fragment),N=T(),P=k("div"),x(A.$$.fragment),M=T(),x(I.$$.fragment),w(o,"class","server-icon svelte-igdbzh"),w(s,"class","server-title svelte-igdbzh"),w(n,"class","server-header svelte-igdbzh"),w(h,"class","error-container svelte-igdbzh"),$t(h,"is-error",!!r[1]),w(P,"class","form-actions svelte-igdbzh"),w(g,"class","form-actions-row svelte-igdbzh"),w(e,"class","server-edit-form svelte-igdbzh"),w(t,"class",R="c-mcp-server-card "+(r[0]==="add"||r[0]==="addJson"||r[0]==="addRemote"?"add-server-section":"server-item")+" svelte-igdbzh")},m(G,F){y(G,t,F),b(t,e),b(e,n),b(n,s),b(s,o),S(a,o,null),b(s,l),S(i,s,null),b(e,c),~d&&D[d].m(e,null),b(e,f),ut&&ut.m(e,null),b(e,$),b(e,g),b(g,h),S(C,h,null),b(g,N),b(g,P),S(A,P,null),b(P,M),S(I,P,null),J=!0,tt||(et=zt(t,"submit",To(r[21])),tt=!0)},p(G,F){const Z={};65536&F[0]|131072&F[1]&&(Z.$$scope={dirty:F,ctx:G}),i.$set(Z);let kt=d;d=rt(G),d===kt?~d&&D[d].p(G,F):(u&&(U(),m(D[kt],1,1,()=>{D[kt]=null}),V()),~d?(u=D[d],u?u.p(G,F):(u=D[d]=z[d](G),u.c()),p(u,1),u.m(e,f)):u=null),9&F[0]&&(E=(G[0]==="add"||G[0]==="edit")&&!jt(G[3])),E?ut?(ut.p(G,F),9&F[0]&&p(ut,1)):(ut=fr(G),ut.c(),p(ut,1),ut.m(e,$)):ut&&(U(),m(ut,1,1,()=>{ut=null}),V());const Kt={};2&F[0]|131072&F[1]&&(Kt.$$scope={dirty:F,ctx:G}),C.$set(Kt),(!J||2&F[0])&&$t(h,"is-error",!!G[1]);const Fe={};131072&F[1]&&(Fe.$$scope={dirty:F,ctx:G}),A.$set(Fe);const xe={};4&F[0]&&(xe.loading=G[2]),131072&F[0]&&(xe.disabled=G[17]),1&F[0]|131072&F[1]&&(xe.$$scope={dirty:F,ctx:G}),I.$set(xe),(!J||1&F[0]&&R!==(R="c-mcp-server-card "+(G[0]==="add"||G[0]==="addJson"||G[0]==="addRemote"?"add-server-section":"server-item")+" svelte-igdbzh"))&&w(t,"class",R)},i(G){J||(p(a.$$.fragment,G),p(i.$$.fragment,G),p(u),p(ut),p(C.$$.fragment,G),p(A.$$.fragment,G),p(I.$$.fragment,G),J=!0)},o(G){m(a.$$.fragment,G),m(i.$$.fragment,G),m(u),m(ut),m(C.$$.fragment,G),m(A.$$.fragment,G),m(I.$$.fragment,G),J=!1},d(G){G&&v(t),_(a),_(i),~d&&D[d].d(),ut&&ut.d(),_(C),_(A),_(I),tt=!1,et()}}}function Ac(r){let t,e,n;function s(a){r[36](a)}let o={$$slots:{footer:[Sd],header:[vd]},$$scope:{ctx:r}};return r[14]!==void 0&&(o.collapsed=r[14]),t=new oo({props:o}),_t.push(()=>Ct(t,"collapsed",s)),{c(){x(t.$$.fragment)},m(a,l){S(t,a,l),n=!0},p(a,l){const i={};311544&l[0]|131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),!e&&16384&l[0]&&(e=!0,i.collapsed=a[14],bt(()=>e=!1)),t.$set(i)},i(a){n||(p(t.$$.fragment,a),n=!0)},o(a){m(t.$$.fragment,a),n=!1},d(a){_(t,a)}}}function Tc(r){let t;return{c(){t=L(r[16])},m(e,n){y(e,t,n)},p(e,n){65536&n[0]&&ot(t,e[16])},d(e){e&&v(t)}}}function Ec(r){var N,P;let t,e,n,s,o,a,l,i,c,d,u=(r[0]==="addRemote"||r[0]==="edit"&&(((N=r[3])==null?void 0:N.type)==="http"||((P=r[3])==null?void 0:P.type)==="sse"))&&mr(r);function f(A){r[40](A)}let $={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[Oc]},$$scope:{ctx:r}};r[8]!==void 0&&($.value=r[8]),s=new me({props:$}),_t.push(()=>Ct(s,"value",f)),s.$on("focus",r[19]);const g=[Fc,zc],h=[];function C(A,M){var I,R;return A[0]==="addRemote"||((I=A[3])==null?void 0:I.type)==="http"||((R=A[3])==null?void 0:R.type)==="sse"?0:1}return l=C(r),i=h[l]=g[l](r),{c(){u&&u.c(),t=T(),e=k("div"),n=k("div"),x(s.$$.fragment),a=T(),i.c(),c=gt(),w(n,"class","input-field svelte-igdbzh"),w(e,"class","form-row svelte-igdbzh")},m(A,M){u&&u.m(A,M),y(A,t,M),y(A,e,M),b(e,n),S(s,n,null),y(A,a,M),h[l].m(A,M),y(A,c,M),d=!0},p(A,M){var J,tt;A[0]==="addRemote"||A[0]==="edit"&&(((J=A[3])==null?void 0:J.type)==="http"||((tt=A[3])==null?void 0:tt.type)==="sse")?u?(u.p(A,M),9&M[0]&&p(u,1)):(u=mr(A),u.c(),p(u,1),u.m(t.parentNode,t)):u&&(U(),m(u,1,1,()=>{u=null}),V());const I={};131072&M[1]&&(I.$$scope={dirty:M,ctx:A}),!o&&256&M[0]&&(o=!0,I.value=A[8],bt(()=>o=!1)),s.$set(I);let R=l;l=C(A),l===R?h[l].p(A,M):(U(),m(h[R],1,1,()=>{h[R]=null}),V(),i=h[l],i?i.p(A,M):(i=h[l]=g[l](A),i.c()),p(i,1),i.m(c.parentNode,c))},i(A){d||(p(u),p(s.$$.fragment,A),p(i),d=!0)},o(A){m(u),m(s.$$.fragment,A),m(i),d=!1},d(A){A&&(v(t),v(e),v(a),v(c)),u&&u.d(A),_(s),h[l].d(A)}}}function Nc(r){let t,e,n,s,o,a,l,i,c;function d(f){r[37](f)}n=new K({props:{size:1,weight:"medium",$$slots:{default:[Vc]},$$scope:{ctx:r}}});let u={size:1,placeholder:"Paste JSON here..."};return r[11]!==void 0&&(u.value=r[11]),l=new ro({props:u}),_t.push(()=>Ct(l,"value",d)),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),a=k("div"),x(l.$$.fragment),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh"),w(a,"class","input-field svelte-igdbzh"),w(o,"class","form-row svelte-igdbzh")},m(f,$){y(f,t,$),b(t,e),S(n,e,null),y(f,s,$),y(f,o,$),b(o,a),S(l,a,null),c=!0},p(f,$){const g={};131072&$[1]&&(g.$$scope={dirty:$,ctx:f}),n.$set(g);const h={};!i&&2048&$[0]&&(i=!0,h.value=f[11],bt(()=>i=!1)),l.$set(h)},i(f){c||(p(n.$$.fragment,f),p(l.$$.fragment,f),c=!0)},o(f){m(n.$$.fragment,f),m(l.$$.fragment,f),c=!1},d(f){f&&(v(t),v(s),v(o)),_(n),_(l)}}}function mr(r){let t,e,n,s,o,a,l,i,c;return n=new K({props:{size:1,weight:"medium",$$slots:{default:[Ic]},$$scope:{ctx:r}}}),a=new xt({props:{size:1,variant:r[12]==="http"?"solid":"ghost",color:r[12]==="http"?"accent":"neutral",type:"button",$$slots:{default:[Pc]},$$scope:{ctx:r}}}),a.$on("click",r[38]),i=new xt({props:{size:1,variant:r[12]==="sse"?"solid":"ghost",color:r[12]==="sse"?"accent":"neutral",type:"button",$$slots:{default:[Rc]},$$scope:{ctx:r}}}),i.$on("click",r[39]),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),x(a.$$.fragment),l=T(),x(i.$$.fragment),w(o,"class","connection-type-buttons svelte-igdbzh"),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(d,u){y(d,t,u),b(t,e),S(n,e,null),b(e,s),b(e,o),S(a,o,null),b(o,l),S(i,o,null),c=!0},p(d,u){const f={};131072&u[1]&&(f.$$scope={dirty:u,ctx:d}),n.$set(f);const $={};4096&u[0]&&($.variant=d[12]==="http"?"solid":"ghost"),4096&u[0]&&($.color=d[12]==="http"?"accent":"neutral"),131072&u[1]&&($.$$scope={dirty:u,ctx:d}),a.$set($);const g={};4096&u[0]&&(g.variant=d[12]==="sse"?"solid":"ghost"),4096&u[0]&&(g.color=d[12]==="sse"?"accent":"neutral"),131072&u[1]&&(g.$$scope={dirty:u,ctx:d}),i.$set(g)},i(d){c||(p(n.$$.fragment,d),p(a.$$.fragment,d),p(i.$$.fragment,d),c=!0)},o(d){m(n.$$.fragment,d),m(a.$$.fragment,d),m(i.$$.fragment,d),c=!1},d(d){d&&v(t),_(n),_(a),_(i)}}}function Ic(r){let t;return{c(){t=L("Connection Type")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Pc(r){let t;return{c(){t=L("HTTP")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Rc(r){let t;return{c(){t=L("SSE")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Lc(r){let t;return{c(){t=L("Name")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Oc(r){let t,e;return t=new K({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Lc]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function zc(r){let t,e,n,s,o;function a(i){r[42](i)}let l={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[jc]},$$scope:{ctx:r}};return r[9]!==void 0&&(l.value=r[9]),n=new me({props:l}),_t.push(()=>Ct(n,"value",a)),n.$on("focus",r[19]),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(i,c){y(i,t,c),b(t,e),S(n,e,null),o=!0},p(i,c){const d={};131072&c[1]&&(d.$$scope={dirty:c,ctx:i}),!s&&512&c[0]&&(s=!0,d.value=i[9],bt(()=>s=!1)),n.$set(d)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){m(n.$$.fragment,i),o=!1},d(i){i&&v(t),_(n)}}}function Fc(r){let t,e,n,s,o;function a(i){r[41](i)}let l={size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",$$slots:{label:[Uc]},$$scope:{ctx:r}};return r[10]!==void 0&&(l.value=r[10]),n=new me({props:l}),_t.push(()=>Ct(n,"value",a)),n.$on("focus",r[19]),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(i,c){y(i,t,c),b(t,e),S(n,e,null),o=!0},p(i,c){const d={};131072&c[1]&&(d.$$scope={dirty:c,ctx:i}),!s&&1024&c[0]&&(s=!0,d.value=i[10],bt(()=>s=!1)),n.$set(d)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){m(n.$$.fragment,i),o=!1},d(i){i&&v(t),_(n)}}}function Zc(r){let t;return{c(){t=L("Command")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function jc(r){let t,e;return t=new K({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Zc]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Dc(r){let t;return{c(){t=L("URL")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Uc(r){let t,e;return t=new K({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Dc]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Vc(r){let t;return{c(){t=L("Code Snippet")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function fr(r){let t,e,n;function s(a){r[43](a)}let o={handleEnterEditMode:r[19]};return r[13]!==void 0&&(o.envVarEntries=r[13]),t=new _c({props:o}),_t.push(()=>Ct(t,"envVarEntries",s)),{c(){x(t.$$.fragment)},m(a,l){S(t,a,l),n=!0},p(a,l){const i={};!e&&8192&l[0]&&(e=!0,i.envVarEntries=a[13],bt(()=>e=!1)),t.$set(i)},i(a){n||(p(t.$$.fragment,a),n=!0)},o(a){m(t.$$.fragment,a),n=!1},d(a){_(t,a)}}}function qc(r){let t;return{c(){t=L(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n[0]&&ot(t,e[1])},d(e){e&&v(t)}}}function Bc(r){let t,e;return t=new Ko({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Jc(r){let t;return{c(){t=L("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Gc(r){let t;return{c(){t=L("Save")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Hc(r){let t;return{c(){t=L("Add")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Wc(r){let t;return{c(){t=L("Add")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Kc(r){let t;return{c(){t=L("Import")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Yc(r){let t;function e(o,a){return o[0]==="addJson"?Kc:o[0]==="add"?Wc:o[0]==="addRemote"?Hc:o[0]==="edit"?Gc:void 0}let n=e(r),s=n&&n(r);return{c(){s&&s.c(),t=gt()},m(o,a){s&&s.m(o,a),y(o,t,a)},p(o,a){n!==(n=e(o))&&(s&&s.d(1),s=n&&n(o),s&&(s.c(),s.m(t.parentNode,t)))},d(o){o&&v(t),s&&s.d(o)}}}function $r(r){let t,e;return t=new cn({props:{size:1,variant:"ghost",$$slots:{default:[td]},$$scope:{ctx:r}}}),t.$on("click",r[35]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};16384&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Xc(r){let t,e;return t=new Kn({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Qc(r){let t,e;return t=new kc({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function td(r){let t,e,n,s;const o=[Qc,Xc],a=[];function l(i,c){return i[14]?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=gt()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t!==d&&(U(),m(a[d],1,1,()=>{a[d]=null}),V(),e=a[t],e||(e=a[t]=o[t](i),e.c()),p(e,1),e.m(n.parentNode,n))},i(i){s||(p(e),s=!0)},o(i){m(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function ed(r){let t;return{c(){t=k("div"),w(t,"class","c-dot svelte-igdbzh"),$t(t,"c-green",!r[6]),$t(t,"c-warning",!r[6]&&!!r[7]),$t(t,"c-red",!!r[6]),$t(t,"c-disabled",r[3].disabled)},m(e,n){y(e,t,n)},p(e,n){64&n[0]&&$t(t,"c-green",!e[6]),192&n[0]&&$t(t,"c-warning",!e[6]&&!!e[7]),64&n[0]&&$t(t,"c-red",!!e[6]),8&n[0]&&$t(t,"c-disabled",e[3].disabled)},d(e){e&&v(t)}}}function gr(r){let t,e,n,s=r[18].length+"";return{c(){t=L("("),e=L(s),n=L(") tools")},m(o,a){y(o,t,a),y(o,e,a),y(o,n,a)},p(o,a){262144&a[0]&&s!==(s=o[18].length+"")&&ot(e,s)},d(o){o&&(v(t),v(e),v(n))}}}function nd(r){let t,e,n,s=r[3].name+"",o=r[18].length>0&&gr(r);return{c(){t=L(s),e=T(),o&&o.c(),n=gt()},m(a,l){y(a,t,l),y(a,e,l),o&&o.m(a,l),y(a,n,l)},p(a,l){8&l[0]&&s!==(s=a[3].name+"")&&ot(t,s),a[18].length>0?o?o.p(a,l):(o=gr(a),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null)},d(a){a&&(v(t),v(e),v(n)),o&&o.d(a)}}}function sd(r){let t,e,n;return e=new K({props:{size:1,weight:"medium",$$slots:{default:[nd]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),w(t,"class","server-name svelte-igdbzh")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};262152&o[0]|131072&o[1]&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function rd(r){let t,e=bn(r[3])+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){8&s[0]&&e!==(e=bn(n[3])+"")&&ot(t,e)},d(n){n&&v(t)}}}function od(r){let t,e;return t=new K({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[rd]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};8&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function ad(r){let t,e,n,s,o,a,l,i,c,d=r[18].length>0&&$r(r);return n=new de({props:{content:r[6]||r[7],$$slots:{default:[ed]},$$scope:{ctx:r}}}),o=new de({props:{content:r[3].name,side:"top",align:"start",$$slots:{default:[sd]},$$scope:{ctx:r}}}),i=new de({props:{content:bn(r[3]),side:"top",align:"start",$$slots:{default:[od]},$$scope:{ctx:r}}}),{c(){t=k("div"),d&&d.c(),e=T(),x(n.$$.fragment),s=T(),x(o.$$.fragment),a=T(),l=k("div"),x(i.$$.fragment),w(l,"class","command-text svelte-igdbzh"),w(t,"slot","header-left"),w(t,"class","l-header svelte-igdbzh")},m(u,f){y(u,t,f),d&&d.m(t,null),b(t,e),S(n,t,null),b(t,s),S(o,t,null),b(t,a),b(t,l),S(i,l,null),c=!0},p(u,f){u[18].length>0?d?(d.p(u,f),262144&f[0]&&p(d,1)):(d=$r(u),d.c(),p(d,1),d.m(t,e)):d&&(U(),m(d,1,1,()=>{d=null}),V());const $={};192&f[0]&&($.content=u[6]||u[7]),200&f[0]|131072&f[1]&&($.$$scope={dirty:f,ctx:u}),n.$set($);const g={};8&f[0]&&(g.content=u[3].name),262152&f[0]|131072&f[1]&&(g.$$scope={dirty:f,ctx:u}),o.$set(g);const h={};8&f[0]&&(h.content=bn(u[3])),8&f[0]|131072&f[1]&&(h.$$scope={dirty:f,ctx:u}),i.$set(h)},i(u){c||(p(d),p(n.$$.fragment,u),p(o.$$.fragment,u),p(i.$$.fragment,u),c=!0)},o(u){m(d),m(n.$$.fragment,u),m(o.$$.fragment,u),m(i.$$.fragment,u),c=!1},d(u){u&&v(t),d&&d.d(),_(n),_(o),_(i)}}}function id(r){let t,e;return t=new la({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function ld(r){let t,e;return t=new cn({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[id]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function cd(r){let t;return{c(){t=L("Edit")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function dd(r){let t,e,n,s,o;return e=new ca({}),s=new K({props:{size:1,weight:"medium",$$slots:{default:[cd]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),x(s.$$.fragment),w(t,"class","status-controls-button svelte-igdbzh")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),S(s,t,null),o=!0},p(a,l){const i={};131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(p(e.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&v(t),_(e),_(s)}}}function ud(r){let t;return{c(){t=L("Copy JSON")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function pd(r){let t,e,n,s,o;return e=new da({}),s=new K({props:{size:1,weight:"medium",$$slots:{default:[ud]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),x(s.$$.fragment),w(t,"class","status-controls-button svelte-igdbzh")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),S(s,t,null),o=!0},p(a,l){const i={};131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(p(e.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&v(t),_(e),_(s)}}}function md(r){let t;return{c(){t=L("Delete")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function fd(r){let t,e,n,s,o;return e=new Qr({}),s=new K({props:{size:1,weight:"medium",$$slots:{default:[md]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),x(s.$$.fragment),w(t,"class","status-controls-button svelte-igdbzh")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),S(s,t,null),o=!0},p(a,l){const i={};131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(p(e.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&v(t),_(e),_(s)}}}function $d(r){let t,e,n,s,o,a;return t=new yt.Item({props:{onSelect:r[19],$$slots:{default:[dd]},$$scope:{ctx:r}}}),n=new yt.Item({props:{onSelect:r[32],$$slots:{default:[pd]},$$scope:{ctx:r}}}),o=new yt.Item({props:{color:"error",onSelect:r[33],$$slots:{default:[fd]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment),s=T(),x(o.$$.fragment)},m(l,i){S(t,l,i),y(l,e,i),S(n,l,i),y(l,s,i),S(o,l,i),a=!0},p(l,i){const c={};131072&i[1]&&(c.$$scope={dirty:i,ctx:l}),t.$set(c);const d={};32768&i[0]&&(d.onSelect=l[32]),131072&i[1]&&(d.$$scope={dirty:i,ctx:l}),n.$set(d);const u={};32792&i[0]&&(u.onSelect=l[33]),131072&i[1]&&(u.$$scope={dirty:i,ctx:l}),o.$set(u)},i(l){a||(p(t.$$.fragment,l),p(n.$$.fragment,l),p(o.$$.fragment,l),a=!0)},o(l){m(t.$$.fragment,l),m(n.$$.fragment,l),m(o.$$.fragment,l),a=!1},d(l){l&&(v(e),v(s)),_(t,l),_(n,l),_(o,l)}}}function gd(r){let t,e,n,s;return t=new yt.Trigger({props:{$$slots:{default:[ld]},$$scope:{ctx:r}}}),n=new yt.Content({props:{side:"bottom",align:"end",$$slots:{default:[$d]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment)},m(o,a){S(t,o,a),y(o,e,a),S(n,o,a),s=!0},p(o,a){const l={};131072&a[1]&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};32792&a[0]|131072&a[1]&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(p(t.$$.fragment,o),p(n.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(t,o),_(n,o)}}}function hd(r){let t,e,n,s,o,a,l=Ro(),i=l&&function(u){let f,$;return f=new Hn({props:{size:1,checked:!u[3].disabled}}),f.$on("change",u[31]),{c(){x(f.$$.fragment)},m(g,h){S(f,g,h),$=!0},p(g,h){const C={};8&h[0]&&(C.checked=!g[3].disabled),f.$set(C)},i(g){$||(p(f.$$.fragment,g),$=!0)},o(g){m(f.$$.fragment,g),$=!1},d(g){_(f,g)}}}(r);function c(u){r[34](u)}let d={$$slots:{default:[gd]},$$scope:{ctx:r}};return r[15]!==void 0&&(d.requestClose=r[15]),s=new yt.Root({props:d}),_t.push(()=>Ct(s,"requestClose",c)),{c(){t=k("div"),e=k("div"),i&&i.c(),n=T(),x(s.$$.fragment),w(e,"class","status-controls svelte-igdbzh"),w(t,"class","server-actions svelte-igdbzh"),w(t,"slot","header-right")},m(u,f){y(u,t,f),b(t,e),i&&i.m(e,null),b(e,n),S(s,e,null),a=!0},p(u,f){l&&i.p(u,f);const $={};32792&f[0]|131072&f[1]&&($.$$scope={dirty:f,ctx:u}),!o&&32768&f[0]&&(o=!0,$.requestClose=u[15],bt(()=>o=!1)),s.$set($)},i(u){a||(p(i),p(s.$$.fragment,u),a=!0)},o(u){m(i),m(s.$$.fragment,u),a=!1},d(u){u&&v(t),i&&i.d(),_(s)}}}function vd(r){let t,e;return t=new Ae({props:{slot:"header",$$slots:{"header-right":[hd],"header-left":[ad]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};311544&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function yd(r){let t,e=(r[45].definition.mcp_tool_name||r[45].definition.name)+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){262144&s[0]&&e!==(e=(n[45].definition.mcp_tool_name||n[45].definition.name)+"")&&ot(t,e)},d(n){n&&v(t)}}}function hr(r){let t,e;return t=new K({props:{size:1,color:"secondary",$$slots:{default:[wd]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};262144&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function wd(r){let t,e=r[45].definition.description+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){262144&s[0]&&e!==(e=n[45].definition.description+"")&&ot(t,e)},d(n){n&&v(t)}}}function xd(r){let t,e,n=r[45].definition.description&&hr(r);return{c(){n&&n.c(),t=gt()},m(s,o){n&&n.m(s,o),y(s,t,o),e=!0},p(s,o){s[45].definition.description?n?(n.p(s,o),262144&o[0]&&p(n,1)):(n=hr(s),n.c(),p(n,1),n.m(t.parentNode,t)):n&&(U(),m(n,1,1,()=>{n=null}),V())},i(s){e||(p(n),e=!0)},o(s){m(n),e=!1},d(s){s&&v(t),n&&n.d(s)}}}function vr(r){let t,e,n,s,o,a,l,i,c,d,u;return a=new K({props:{size:1,weight:"medium",$$slots:{default:[yd]},$$scope:{ctx:r}}}),c=new de({props:{content:r[45].definition.description,align:"start",$$slots:{default:[xd]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),n=k("div"),s=k("div"),o=T(),x(a.$$.fragment),l=T(),i=k("div"),x(c.$$.fragment),d=T(),w(s,"class","tool-status-dot svelte-igdbzh"),$t(s,"enabled",r[45].enabled),$t(s,"disabled",!r[45].enabled),w(n,"class","tool-status svelte-igdbzh"),w(i,"class","c-tool-description svelte-igdbzh"),w(e,"class","c-tool-info svelte-igdbzh"),w(t,"class","c-tool-item svelte-igdbzh")},m(f,$){y(f,t,$),b(t,e),b(e,n),b(n,s),b(n,o),S(a,n,null),b(e,l),b(e,i),S(c,i,null),b(t,d),u=!0},p(f,$){(!u||262144&$[0])&&$t(s,"enabled",f[45].enabled),(!u||262144&$[0])&&$t(s,"disabled",!f[45].enabled);const g={};262144&$[0]|131072&$[1]&&(g.$$scope={dirty:$,ctx:f}),a.$set(g);const h={};262144&$[0]&&(h.content=f[45].definition.description),262144&$[0]|131072&$[1]&&(h.$$scope={dirty:$,ctx:f}),c.$set(h)},i(f){u||(p(a.$$.fragment,f),p(c.$$.fragment,f),u=!0)},o(f){m(a.$$.fragment,f),m(c.$$.fragment,f),u=!1},d(f){f&&v(t),_(a),_(c)}}}function Sd(r){let t,e,n=mt(r[18]),s=[];for(let a=0;a<n.length;a+=1)s[a]=vr(pr(r,n,a));const o=a=>m(s[a],1,1,()=>{s[a]=null});return{c(){t=k("div");for(let a=0;a<s.length;a+=1)s[a].c();w(t,"slot","footer")},m(a,l){y(a,t,l);for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(t,null);e=!0},p(a,l){if(262144&l[0]){let i;for(n=mt(a[18]),i=0;i<n.length;i+=1){const c=pr(a,n,i);s[i]?(s[i].p(c,l),p(s[i],1)):(s[i]=vr(c),s[i].c(),p(s[i],1),s[i].m(t,null))}for(U(),i=n.length;i<s.length;i+=1)o(i);V()}},i(a){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(a){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(a){a&&v(t),ve(s,a)}}}function _d(r){let t,e,n,s;const o=[Ac,Mc],a=[];function l(i,c){return i[0]==="view"&&i[3]?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=gt()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t===d?a[t].p(i,c):(U(),m(a[d],1,1,()=>{a[d]=null}),V(),e=a[t],e?e.p(i,c):(e=a[t]=o[t](i),e.c()),p(e,1),e.m(n.parentNode,n))},i(i){s||(p(e),s=!0)},o(i){m(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function Cd({key:r,value:t}){return r.trim()&&t.trim()}function bd(r,t,e){let n,s,o,a,l,{server:i=null}=t,{onDelete:c}=t,{onAdd:d}=t,{onSave:u}=t,{onEdit:f}=t,{onToggleDisableServer:$}=t,{onJSONImport:g}=t,{onCancel:h}=t,{disabledText:C}=t,{warningText:N}=t,{mode:P="view"}=t,{mcpServerError:A=""}=t,M=(i==null?void 0:i.name)??"",I=jt(i)?"":Ie(i)?i.command:"",R=jt(i)?i.url:"",J=Ie(i)?i.env??{}:{},tt="",et=jt(i)?i.type:"http",E=[];D();let z=!0;function D(){e(13,E=Object.entries(J).map(([F,Z])=>({id:crypto.randomUUID(),key:F,value:Z})))}let rt=()=>{},{busy:ut=!1}=t;function G(){if(i){const F=Tn.convertServerToJSON(i);navigator.clipboard.writeText(F)}}return r.$$set=F=>{"server"in F&&e(3,i=F.server),"onDelete"in F&&e(4,c=F.onDelete),"onAdd"in F&&e(23,d=F.onAdd),"onSave"in F&&e(24,u=F.onSave),"onEdit"in F&&e(25,f=F.onEdit),"onToggleDisableServer"in F&&e(5,$=F.onToggleDisableServer),"onJSONImport"in F&&e(26,g=F.onJSONImport),"onCancel"in F&&e(27,h=F.onCancel),"disabledText"in F&&e(6,C=F.disabledText),"warningText"in F&&e(7,N=F.warningText),"mode"in F&&e(0,P=F.mode),"mcpServerError"in F&&e(1,A=F.mcpServerError),"busy"in F&&e(2,ut=F.busy)},r.$$.update=()=>{8&r.$$.dirty[0]&&e(18,n=(i==null?void 0:i.tools)??[]),768&r.$$.dirty[0]&&M&&I&&e(1,A=""),1793&r.$$.dirty[0]&&e(30,s=!((P!=="add"||M.trim()&&I.trim())&&(P!=="addRemote"||M.trim()&&R.trim()))),2049&r.$$.dirty[0]&&e(29,o=P==="addJson"&&!tt.trim()),1610612737&r.$$.dirty[0]&&e(17,a=s||P==="view"||o),1&r.$$.dirty[0]&&e(16,l=(()=>{switch(P){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())},[P,A,ut,i,c,$,C,N,M,I,R,tt,et,E,z,rt,l,a,n,function(){i&&P==="view"&&(e(0,P="edit"),f(i),rt())},G,async function(){e(1,A=""),e(2,ut=!0);const F=E.filter(Cd);J=Object.fromEntries(F.map(({key:Z,value:kt})=>[Z.trim(),kt.trim()])),D();try{if(P==="add"){const Z={type:"stdio",name:M.trim(),command:I.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(J).length>0?J:void 0};await d(Z)}else if(P==="addRemote"){const Z={type:et,name:M.trim(),url:R.trim()};await d(Z)}else if(P==="addJson"){try{JSON.parse(tt)}catch(Z){const kt=Z instanceof Error?Z.message:String(Z);throw new St(`Invalid JSON format: ${kt}`)}await g(tt)}else if(P==="edit"&&i){if(jt(i)){const Z={...i,type:et,name:M.trim(),url:R.trim()};await u(Z)}else if(Ie(i)){const Z={...i,name:M.trim(),command:I.trim(),arguments:"",env:Object.keys(J).length>0?J:void 0};await u(Z)}}}catch(Z){e(1,A=Z instanceof St?Z.message:"Failed to save server"),console.warn(Z)}finally{e(2,ut=!1)}},function(){e(2,ut=!1),e(1,A=""),h==null||h(),e(11,tt=""),e(8,M=(i==null?void 0:i.name)??""),e(9,I=jt(i)?"":Ie(i)?i.command:""),e(10,R=jt(i)?i.url:""),J=Ie(i)&&i.env?{...i.env}:{},e(12,et=jt(i)?i.type:"http"),D()},d,u,f,g,h,D,o,s,()=>{i&&$(i.id),rt()},()=>{G(),rt()},()=>{c(i.id),rt()},function(F){rt=F,e(15,rt)},()=>e(14,z=!z),function(F){z=F,e(14,z)},function(F){tt=F,e(11,tt)},()=>e(12,et="http"),()=>e(12,et="sse"),function(F){M=F,e(8,M)},function(F){R=F,e(10,R)},function(F){I=F,e(9,I)},function(F){E=F,e(13,E)}]}class xo extends ct{constructor(t){super(),dt(this,t,bd,_d,at,{server:3,onDelete:4,onAdd:23,onSave:24,onEdit:25,onToggleDisableServer:5,onJSONImport:26,onCancel:27,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:28,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[28]}}function yr(r,t,e){const n=r.slice();return n[21]=t[e],n}function kd(r){let t,e,n;return e=new Yo({}),{c(){t=k("div"),x(e.$$.fragment),w(t,"slot","iconLeft"),w(t,"class","search-icon")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p:B,i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function Md(r){let t,e=r[21].label+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p:B,d(n){n&&v(t)}}}function Ad(r){let t,e=r[21].description+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p:B,d(n){n&&v(t)}}}function wr(r){let t,e,n,s,o,a,l,i,c,d;function u(g){r[16](g)}function f(g){r[17](g)}let $={placeholder:"Enter your API key...",size:1,variant:"surface",type:"password"};return r[4]!==void 0&&($.value=r[4]),r[5]!==void 0&&($.textInput=r[5]),e=new me({props:$}),_t.push(()=>Ct(e,"value",u)),_t.push(()=>Ct(e,"textInput",f)),e.$on("keydown",function(...g){return r[18](r[21],...g)}),l=new xt({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Td]},$$scope:{ctx:r}}}),l.$on("click",function(){return r[19](r[21])}),c=new xt({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Ed]},$$scope:{ctx:r}}}),c.$on("click",r[10]),{c(){t=k("div"),x(e.$$.fragment),o=T(),a=k("div"),x(l.$$.fragment),i=T(),x(c.$$.fragment),w(a,"class","api-key-actions svelte-1y2bury"),w(t,"class","api-key-input-container svelte-1y2bury")},m(g,h){y(g,t,h),S(e,t,null),b(t,o),b(t,a),S(l,a,null),b(a,i),S(c,a,null),d=!0},p(g,h){r=g;const C={};!n&&16&h&&(n=!0,C.value=r[4],bt(()=>n=!1)),!s&&32&h&&(s=!0,C.textInput=r[5],bt(()=>s=!1)),e.$set(C);const N={};16777216&h&&(N.$$scope={dirty:h,ctx:r}),l.$set(N);const P={};16777216&h&&(P.$$scope={dirty:h,ctx:r}),c.$set(P)},i(g){d||(p(e.$$.fragment,g),p(l.$$.fragment,g),p(c.$$.fragment,g),d=!0)},o(g){m(e.$$.fragment,g),m(l.$$.fragment,g),m(c.$$.fragment,g),d=!1},d(g){g&&v(t),_(e),_(l),_(c)}}}function Td(r){let t;return{c(){t=L("Install")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Ed(r){let t;return{c(){t=L("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Nd(r){let t,e,n,s,o,a;n=new K({props:{size:1,weight:"medium",$$slots:{default:[Md]},$$scope:{ctx:r}}});let l=r[21].description&&function(c){let d,u;return d=new K({props:{size:1,color:"secondary",$$slots:{default:[Ad]},$$scope:{ctx:c}}}),{c(){x(d.$$.fragment)},m(f,$){S(d,f,$),u=!0},p(f,$){const g={};16777216&$&&(g.$$scope={dirty:$,ctx:f}),d.$set(g)},i(f){u||(p(d.$$.fragment,f),u=!0)},o(f){m(d.$$.fragment,f),u=!1},d(f){_(d,f)}}}(r),i=r[3]===r[21].value&&wr(r);return{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),l&&l.c(),o=T(),i&&i.c(),w(e,"class","mcp-service-title svelte-1y2bury"),w(t,"slot","header-left"),w(t,"class","mcp-service-info svelte-1y2bury")},m(c,d){y(c,t,d),b(t,e),S(n,e,null),b(t,s),l&&l.m(t,null),b(t,o),i&&i.m(t,null),a=!0},p(c,d){const u={};16777216&d&&(u.$$scope={dirty:d,ctx:c}),n.$set(u),c[21].description&&l.p(c,d),c[3]===c[21].value?i?(i.p(c,d),8&d&&p(i,1)):(i=wr(c),i.c(),p(i,1),i.m(t,null)):i&&(U(),m(i,1,1,()=>{i=null}),V())},i(c){a||(p(n.$$.fragment,c),p(l),p(i),a=!0)},o(c){m(n.$$.fragment,c),m(l),m(i),a=!1},d(c){c&&v(t),_(n),l&&l.d(),i&&i.d()}}}function Id(r){let t,e;return t=new xt({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Rd]},$$scope:{ctx:r}}}),t.$on("click",function(){return r[15](r[21])}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){r=n;const o={};16777216&s&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Pd(r){let t,e,n;return e=new Xr.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Ld]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),w(t,"class","installed-indicator svelte-1y2bury")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};16777216&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function Rd(r){let t;return{c(){t=k("span"),t.textContent="+"},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function Ld(r){let t;return{c(){t=L("Installed")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Od(r){let t,e,n,s,o;function a(...d){return r[13](r[21],...d)}const l=[Pd,Id],i=[];function c(d,u){return 1&u&&(e=null),e==null&&(e=!!d[0].some(a)),e?0:1}return n=c(r,-1),s=i[n]=l[n](r),{c(){t=k("div"),s.c(),w(t,"slot","header-right"),w(t,"class","mcp-service-actions svelte-1y2bury")},m(d,u){y(d,t,u),i[n].m(t,null),o=!0},p(d,u){let f=n;n=c(r=d,u),n===f?i[n].p(r,u):(U(),m(i[f],1,1,()=>{i[f]=null}),V(),s=i[n],s?s.p(r,u):(s=i[n]=l[n](r),s.c()),p(s,1),s.m(t,null))},i(d){o||(p(s),o=!0)},o(d){m(s),o=!1},d(d){d&&v(t),i[n].d()}}}function xr(r){let t,e,n,s;return e=new Ae({props:{$$slots:{"header-right":[Od],"header-left":[Nd]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),w(t,"class","mcp-service-item")},m(o,a){y(o,t,a),S(e,t,null),b(t,n),s=!0},p(o,a){const l={};16777273&a&&(l.$$scope={dirty:a,ctx:o}),e.$set(l)},i(o){s||(p(e.$$.fragment,o),s=!0)},o(o){m(e.$$.fragment,o),s=!1},d(o){o&&v(t),_(e)}}}function zd(r){let t,e,n,s,o=r[6].length>9&&function(c){let d,u,f,$;function g(C){c[14](C)}let h={placeholder:"Search MCPs...",size:1,variant:"surface",$$slots:{iconLeft:[kd]},$$scope:{ctx:c}};return c[2]!==void 0&&(h.value=c[2]),u=new me({props:h}),_t.push(()=>Ct(u,"value",g)),u.$on("input",c[7]),{c(){d=k("div"),x(u.$$.fragment),w(d,"class","mcp-search-container")},m(C,N){y(C,d,N),S(u,d,null),$=!0},p(C,N){const P={};16777216&N&&(P.$$scope={dirty:N,ctx:C}),!f&&4&N&&(f=!0,P.value=C[2],bt(()=>f=!1)),u.$set(P)},i(C){$||(p(u.$$.fragment,C),$=!0)},o(C){m(u.$$.fragment,C),$=!1},d(C){C&&v(d),_(u)}}}(r),a=mt(r[6]),l=[];for(let c=0;c<a.length;c+=1)l[c]=xr(yr(r,a,c));const i=c=>m(l[c],1,1,()=>{l[c]=null});return{c(){t=k("div"),o&&o.c(),e=T(),n=k("div");for(let c=0;c<l.length;c+=1)l[c].c();w(n,"class","mcp-list-container svelte-1y2bury"),w(t,"class","mcp-install-content svelte-1y2bury")},m(c,d){y(c,t,d),o&&o.m(t,null),b(t,e),b(t,n);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(n,null);s=!0},p(c,d){if(c[6].length>9&&o.p(c,d),1913&d){let u;for(a=mt(c[6]),u=0;u<a.length;u+=1){const f=yr(c,a,u);l[u]?(l[u].p(f,d),p(l[u],1)):(l[u]=xr(f),l[u].c(),p(l[u],1),l[u].m(n,null))}for(U(),u=a.length;u<l.length;u+=1)i(u);V()}},i(c){if(!s){p(o);for(let d=0;d<a.length;d+=1)p(l[d]);s=!0}},o(c){m(o),l=l.filter(Boolean);for(let d=0;d<l.length;d+=1)m(l[d]);s=!1},d(c){c&&v(t),o&&o.d(),ve(l,c)}}}function Fd(r){let t;return{c(){t=L("Easy MCP Installation")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Zd(r){let t,e,n,s,o;return e=new pa({}),s=new K({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Fd]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),n=T(),x(s.$$.fragment),w(t,"slot","header-left"),w(t,"class","mcp-install-left svelte-1y2bury")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),S(s,t,null),o=!0},p(a,l){const i={};16777216&l&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(p(e.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&v(t),_(e),_(s)}}}function jd(r){let t,e,n;return e=new Ae({props:{$$slots:{"header-left":[Zd]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),w(t,"slot","header"),w(t,"class","mcp-install-header svelte-1y2bury")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};16777216&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function Dd(r){let t,e,n,s;function o(l){r[20](l)}let a={$$slots:{header:[jd],default:[zd]},$$scope:{ctx:r}};return r[1]!==void 0&&(a.collapsed=r[1]),e=new oo({props:a}),_t.push(()=>Ct(e,"collapsed",o)),{c(){t=k("div"),x(e.$$.fragment),w(t,"class","mcp-install-wrapper svelte-1y2bury")},m(l,i){y(l,t,i),S(e,t,null),s=!0},p(l,[i]){const c={};16777277&i&&(c.$$scope={dirty:i,ctx:l}),!n&&2&i&&(n=!0,c.collapsed=l[1],bt(()=>n=!1)),e.$set(c)},i(l){s||(p(e.$$.fragment,l),s=!0)},o(l){m(e.$$.fragment,l),s=!1},d(l){l&&v(t),_(e)}}}const Fn="easyMCPInstall.collapsed";function Ud(r,t){return r.value==="tavily"?`npx -y tavily-mcp@latest --TAVILY_API_KEY=${t}`:r.command}function Vd(r,t){switch(r.value){case"tavily":return{};case"exa-search":return{EXA_API_KEY:t};default:return{API_KEY:t}}}function qd(r,t,e){let{onMCPServerAdd:n}=t,{servers:s=[]}=t,o,a=!1,l="",i=!1,c=null,d="";async function u(g){try{if(s.some(C=>C.name===g.label))return;const h={type:"stdio",name:g.label,command:g.command,arguments:"",useShellInterpolation:!0,env:void 0};n&&n(h)}catch(h){console.error(`Failed to install ${g.label}:`,h)}}async function f(g){try{if(!d.trim())return void(o==null?void 0:o.focus());const h={type:"stdio",name:g.label,command:Ud(g,d.trim()),arguments:"",useShellInterpolation:!0,env:Vd(g,d.trim())};n&&n(h),e(3,c=null),e(4,d="")}catch(h){console.error(`Failed to install ${g.label}:`,h)}}function $(){e(3,c=null),e(4,d="")}r.$$set=g=>{"onMCPServerAdd"in g&&e(11,n=g.onMCPServerAdd),"servers"in g&&e(0,s=g.servers)},r.$$.update=()=>{4098&r.$$.dirty&&typeof window<"u"&&i&&localStorage.setItem(Fn,JSON.stringify(a))};{const g=localStorage.getItem(Fn);if(g!==null)try{e(1,a=JSON.parse(g))}catch{localStorage.removeItem(Fn)}e(12,i=!0)}return[s,a,l,c,d,o,[{value:"context7",label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{value:"playwright",label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{value:"sequential-thinking",label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],function(g){const h=g.target;e(2,l=h.value)},u,f,$,n,i,(g,h)=>h.name===g.label,function(g){l=g,e(2,l)},g=>u(g),function(g){d=g,e(4,d)},function(g){o=g,e(5,o)},(g,h)=>{h.key==="Enter"?f(g):h.key==="Escape"&&$()},g=>f(g),function(g){a=g,e(1,a)}]}class Bd extends ct{constructor(t){super(),dt(this,t,qd,Dd,at,{onMCPServerAdd:11,servers:0})}}const Jd={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},Gd={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},Hd=Lo(),Wd=new class{constructor(r){Q(this,"strings");let t={[ls.vscode]:{},[ls.jetbrains]:Gd};this.strings={...Jd,...t[r]}}get(r){return this.strings[r]}}(Hd.clientType);function Sr(r,t,e){const n=r.slice();return n[24]=t[e],n}function Kd(r){let t;return{c(){t=k("div"),t.textContent="MCP",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function _r(r,t){let e,n,s;return n=new xo({props:{mode:t[2]===t[24].id?"edit":"view",server:t[24],onAdd:t[8],onSave:t[9],onDelete:t[11],onToggleDisableServer:t[12],onEdit:t[7],onCancel:t[6],onJSONImport:t[10],disabledText:t[4].errors.get(t[24].id),warningText:t[4].warnings.get(t[24].id)}}),{key:r,first:null,c(){e=gt(),x(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),S(n,o,a),s=!0},p(o,a){t=o;const l={};5&a&&(l.mode=t[2]===t[24].id?"edit":"view"),1&a&&(l.server=t[24]),17&a&&(l.disabledText=t[4].errors.get(t[24].id)),17&a&&(l.warningText=t[4].warnings.get(t[24].id)),n.$set(l)},i(o){s||(p(n.$$.fragment,o),s=!0)},o(o){m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(n,o)}}}function Cr(r){let t,e;return t=new xo({props:{mode:r[3],onAdd:r[8],onSave:r[9],onDelete:r[11],onToggleDisableServer:r[12],onEdit:r[7],onCancel:r[6],onJSONImport:r[10]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};8&s&&(o.mode=n[3]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Yd(r){let t;return{c(){t=L("Add MCP")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Xd(r){let t,e;return t=new ln({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Qd(r){let t;return{c(){t=L("Add remote MCP")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function tu(r){let t,e;return t=new ln({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function br(r){let t,e;return t=new xt({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[nu],default:[eu]},$$scope:{ctx:r}}}),t.$on("click",r[22]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};32&s&&(o.disabled=n[5]),134217728&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function eu(r){let t;return{c(){t=L("Import from JSON")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function nu(r){let t,e;return t=new to({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function su(r){let t,e,n,s,o,a,l,i,c,d,u,f,$,g,h,C,N,P,A,M,I=[],R=new Map;n=new K({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Kd]},$$scope:{ctx:r}}}),u=new Bd({props:{onMCPServerAdd:r[8],servers:r[0]}});let J=mt(r[0]);const tt=z=>z[24].id;for(let z=0;z<J.length;z+=1){let D=Sr(r,J,z),rt=tt(D);R.set(rt,I[z]=_r(rt,D))}let et=(r[3]==="add"||r[3]==="addJson"||r[3]==="addRemote")&&Cr(r);C=new xt({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[Xd],default:[Yd]},$$scope:{ctx:r}}}),C.$on("click",r[20]),P=new xt({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[tu],default:[Qd]},$$scope:{ctx:r}}}),P.$on("click",r[21]);let E=r[1]&&br(r);return{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),a=L(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),l=k("a"),i=L("in the docs"),c=L("."),d=T(),x(u.$$.fragment),f=T();for(let z=0;z<I.length;z+=1)I[z].c();$=T(),et&&et.c(),g=T(),h=k("div"),x(C.$$.fragment),N=T(),x(P.$$.fragment),A=T(),E&&E.c(),w(e,"class","section-heading svelte-1vnq4q3"),w(l,"href",r[13]),w(o,"class","description-text svelte-1vnq4q3"),w(t,"class","mcp-servers svelte-1vnq4q3"),w(h,"class","add-mcp-button-container svelte-1vnq4q3")},m(z,D){y(z,t,D),b(t,e),S(n,e,null),b(t,s),b(t,o),b(o,a),b(o,l),b(l,i),b(o,c),b(t,d),S(u,t,null),b(t,f);for(let rt=0;rt<I.length;rt+=1)I[rt]&&I[rt].m(t,null);y(z,$,D),et&&et.m(z,D),y(z,g,D),y(z,h,D),S(C,h,null),b(h,N),S(P,h,null),b(h,A),E&&E.m(h,null),M=!0},p(z,[D]){const rt={};134217728&D&&(rt.$$scope={dirty:D,ctx:z}),n.$set(rt);const ut={};1&D&&(ut.servers=z[0]),u.$set(ut),8149&D&&(J=mt(z[0]),U(),I=ye(I,D,tt,1,z,J,R,t,we,_r,null,Sr),V()),z[3]==="add"||z[3]==="addJson"||z[3]==="addRemote"?et?(et.p(z,D),8&D&&p(et,1)):(et=Cr(z),et.c(),p(et,1),et.m(g.parentNode,g)):et&&(U(),m(et,1,1,()=>{et=null}),V());const G={};32&D&&(G.disabled=z[5]),134217728&D&&(G.$$scope={dirty:D,ctx:z}),C.$set(G);const F={};32&D&&(F.disabled=z[5]),134217728&D&&(F.$$scope={dirty:D,ctx:z}),P.$set(F),z[1]?E?(E.p(z,D),2&D&&p(E,1)):(E=br(z),E.c(),p(E,1),E.m(h,null)):E&&(U(),m(E,1,1,()=>{E=null}),V())},i(z){if(!M){p(n.$$.fragment,z),p(u.$$.fragment,z);for(let D=0;D<J.length;D+=1)p(I[D]);p(et),p(C.$$.fragment,z),p(P.$$.fragment,z),p(E),M=!0}},o(z){m(n.$$.fragment,z),m(u.$$.fragment,z);for(let D=0;D<I.length;D+=1)m(I[D]);m(et),m(C.$$.fragment,z),m(P.$$.fragment,z),m(E),M=!1},d(z){z&&(v(t),v($),v(g),v(h)),_(n),_(u);for(let D=0;D<I.length;D+=1)I[D].d();et&&et.d(z),_(C),_(P),E&&E.d()}}}function ru(r,t,e){let n,s,{servers:o}=t,{onMCPServerAdd:a}=t,{onMCPServerSave:l}=t,{onMCPServerDelete:i}=t,{onMCPServerToggleDisable:c}=t,{onCancel:d}=t,{onMCPServerJSONImport:u}=t,{isMCPImportEnabled:f=!0}=t,$=null,g=null;function h(R){return async function(...J){const tt=await R(...J);return e(3,g=null),e(2,$=null),tt}}const C=h(a),N=h(l),P=h(u),A=h(i),M=h(c),I=Wd.get("mcpDocsURL");return r.$$set=R=>{"servers"in R&&e(0,o=R.servers),"onMCPServerAdd"in R&&e(14,a=R.onMCPServerAdd),"onMCPServerSave"in R&&e(15,l=R.onMCPServerSave),"onMCPServerDelete"in R&&e(16,i=R.onMCPServerDelete),"onMCPServerToggleDisable"in R&&e(17,c=R.onMCPServerToggleDisable),"onCancel"in R&&e(18,d=R.onCancel),"onMCPServerJSONImport"in R&&e(19,u=R.onMCPServerJSONImport),"isMCPImportEnabled"in R&&e(1,f=R.isMCPImportEnabled)},r.$$.update=()=>{12&r.$$.dirty&&e(5,n=g==="add"||g==="addJson"||g==="addRemote"||$!==null),1&r.$$.dirty&&e(4,s=Tn.parseServerValidationMessages(o))},[o,f,$,g,s,n,function(){e(2,$=null),e(3,g=null),d==null||d()},function(R){e(2,$=R.id)},C,N,P,A,M,I,a,l,i,c,d,u,()=>{e(3,g="add")},()=>{e(3,g="addRemote")},()=>{e(3,g="addJson")}]}class ou extends ct{constructor(t){super(),dt(this,t,ru,su,at,{servers:0,onMCPServerAdd:14,onMCPServerSave:15,onMCPServerDelete:16,onMCPServerToggleDisable:17,onCancel:18,onMCPServerJSONImport:19,isMCPImportEnabled:1})}}function kr(r,t,e){const n=r.slice();return n[12]=t[e],n}function au(r){let t;return{c(){t=k("div"),t.textContent="Terminal",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function iu(r){let t;return{c(){t=L("Shell:")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function lu(r){let t;return{c(){t=L("Select a shell")},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function cu(r){let t;return{c(){t=L("No shells available")},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function du(r){let t,e,n,s,o=r[5].friendlyName+"",a=r[5].supportString+"";return{c(){t=L(o),e=L(`
            (`),n=L(a),s=L(")")},m(l,i){y(l,t,i),y(l,e,i),y(l,n,i),y(l,s,i)},p(l,i){32&i&&o!==(o=l[5].friendlyName+"")&&ot(t,o),32&i&&a!==(a=l[5].supportString+"")&&ot(n,a)},d(l){l&&(v(t),v(e),v(n),v(s))}}}function uu(r){let t;function e(o,a){return o[5]&&o[1].length>0?du:o[1].length===0?cu:lu}let n=e(r),s=n(r);return{c(){s.c(),t=gt()},m(o,a){s.m(o,a),y(o,t,a)},p(o,a){n===(n=e(o))&&s?s.p(o,a):(s.d(1),s=n(o),s&&(s.c(),s.m(t.parentNode,t)))},d(o){o&&v(t),s.d(o)}}}function pu(r){let t,e;return t=new Xo({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function mu(r){let t,e;return t=new xt({props:{size:1,variant:"outline",color:"neutral",disabled:r[1].length===0,$$slots:{iconRight:[pu],default:[uu]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2&s&&(o.disabled=n[1].length===0),32802&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function fu(r){let t,e;return t=new yt.Label({props:{$$slots:{default:[gu]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};32768&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function $u(r){let t,e,n=[],s=new Map,o=mt(r[1]);const a=l=>l[12].friendlyName;for(let l=0;l<o.length;l+=1){let i=kr(r,o,l),c=a(i);s.set(c,n[l]=Mr(c,i))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();t=gt()},m(l,i){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,i);y(l,t,i),e=!0},p(l,i){30&i&&(o=mt(l[1]),U(),n=ye(n,i,a,1,l,o,s,t.parentNode,we,Mr,t,kr),V())},i(l){if(!e){for(let i=0;i<o.length;i+=1)p(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)m(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d(l)}}}function gu(r){let t;return{c(){t=L("No shells available")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function hu(r){let t,e,n,s,o=r[12].friendlyName+"",a=r[12].supportString+"";return{c(){t=L(o),e=L(`
              (`),n=L(a),s=L(`)
            `)},m(l,i){y(l,t,i),y(l,e,i),y(l,n,i),y(l,s,i)},p(l,i){2&i&&o!==(o=l[12].friendlyName+"")&&ot(t,o),2&i&&a!==(a=l[12].supportString+"")&&ot(n,a)},d(l){l&&(v(t),v(e),v(n),v(s))}}}function Mr(r,t){let e,n,s;function o(){return t[8](t[12])}return n=new yt.Item({props:{onSelect:o,highlight:t[2]===t[12].friendlyName,$$slots:{default:[hu]},$$scope:{ctx:t}}}),{key:r,first:null,c(){e=gt(),x(n.$$.fragment),this.first=e},m(a,l){y(a,e,l),S(n,a,l),s=!0},p(a,l){t=a;const i={};26&l&&(i.onSelect=o),6&l&&(i.highlight=t[2]===t[12].friendlyName),32770&l&&(i.$$scope={dirty:l,ctx:t}),n.$set(i)},i(a){s||(p(n.$$.fragment,a),s=!0)},o(a){m(n.$$.fragment,a),s=!1},d(a){a&&v(e),_(n,a)}}}function vu(r){let t,e,n,s;const o=[$u,fu],a=[];function l(i,c){return i[1].length>0?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=gt()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t===d?a[t].p(i,c):(U(),m(a[d],1,1,()=>{a[d]=null}),V(),e=a[t],e?e.p(i,c):(e=a[t]=o[t](i),e.c()),p(e,1),e.m(n.parentNode,n))},i(i){s||(p(e),s=!0)},o(i){m(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function yu(r){let t,e,n,s;return t=new yt.Trigger({props:{$$slots:{default:[mu]},$$scope:{ctx:r}}}),n=new yt.Content({props:{side:"bottom",align:"start",$$slots:{default:[vu]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment)},m(o,a){S(t,o,a),y(o,e,a),S(n,o,a),s=!0},p(o,a){const l={};32802&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};32798&a&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(p(t.$$.fragment,o),p(n.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(t,o),_(n,o)}}}function wu(r){let t;return{c(){t=L("Start-up script: Code to run wherever a new terminal is opened")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function xu(r){let t,e,n,s,o,a,l,i,c,d,u,f,$,g,h;function C(M){r[9](M)}e=new K({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[au]},$$scope:{ctx:r}}}),o=new K({props:{size:1,$$slots:{default:[iu]},$$scope:{ctx:r}}});let N={$$slots:{default:[yu]},$$scope:{ctx:r}};function P(M){r[10](M)}r[4]!==void 0&&(N.requestClose=r[4]),l=new yt.Root({props:N}),_t.push(()=>Ct(l,"requestClose",C)),u=new K({props:{size:1,$$slots:{default:[wu]},$$scope:{ctx:r}}});let A={placeholder:"Enter shell commands to run on terminal startup",resize:"vertical"};return r[0]!==void 0&&(A.value=r[0]),$=new ro({props:A}),_t.push(()=>Ct($,"value",P)),$.$on("change",r[6]),{c(){t=k("div"),x(e.$$.fragment),n=T(),s=k("div"),x(o.$$.fragment),a=T(),x(l.$$.fragment),c=T(),d=k("div"),x(u.$$.fragment),f=T(),x($.$$.fragment),w(s,"class","shell-selector svelte-dndd5n"),w(d,"class","startup-script-container svelte-dndd5n"),w(t,"class","terminal-settings svelte-dndd5n")},m(M,I){y(M,t,I),S(e,t,null),b(t,n),b(t,s),S(o,s,null),b(s,a),S(l,s,null),b(t,c),b(t,d),S(u,d,null),b(d,f),S($,d,null),h=!0},p(M,[I]){const R={};32768&I&&(R.$$scope={dirty:I,ctx:M}),e.$set(R);const J={};32768&I&&(J.$$scope={dirty:I,ctx:M}),o.$set(J);const tt={};32830&I&&(tt.$$scope={dirty:I,ctx:M}),!i&&16&I&&(i=!0,tt.requestClose=M[4],bt(()=>i=!1)),l.$set(tt);const et={};32768&I&&(et.$$scope={dirty:I,ctx:M}),u.$set(et);const E={};!g&&1&I&&(g=!0,E.value=M[0],bt(()=>g=!1)),$.$set(E)},i(M){h||(p(e.$$.fragment,M),p(o.$$.fragment,M),p(l.$$.fragment,M),p(u.$$.fragment,M),p($.$$.fragment,M),h=!0)},o(M){m(e.$$.fragment,M),m(o.$$.fragment,M),m(l.$$.fragment,M),m(u.$$.fragment,M),m($.$$.fragment,M),h=!1},d(M){M&&v(t),_(e),_(o),_(l),_(u),_($)}}}function Su(r,t,e){let n,s,{supportedShells:o=[]}=t,{selectedShell:a}=t,{startupScript:l}=t,{onShellSelect:i}=t,{onStartupScriptChange:c}=t;return r.$$set=d=>{"supportedShells"in d&&e(1,o=d.supportedShells),"selectedShell"in d&&e(2,a=d.selectedShell),"startupScript"in d&&e(0,l=d.startupScript),"onShellSelect"in d&&e(3,i=d.onShellSelect),"onStartupScriptChange"in d&&e(7,c=d.onStartupScriptChange)},r.$$.update=()=>{var d;4&r.$$.dirty&&e(5,n=a?(d=a,o.find(u=>u.friendlyName===d)):void 0)},[l,o,a,i,s,n,function(d){const u=d.target;c(u.value)},c,d=>{i(d.friendlyName),s()},function(d){s=d,e(4,s)},function(d){l=d,e(0,l)}]}class _u extends ct{constructor(t){super(),dt(this,t,Su,xu,at,{supportedShells:1,selectedShell:2,startupScript:0,onShellSelect:3,onStartupScriptChange:7})}}function Cu(r){let t;return{c(){t=k("div"),t.textContent="Sound Settings",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function bu(r){let t;return{c(){t=L("Enable Sound Effects")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function ku(r){let t;return{c(){t=L("Play a sound when an agent completes a task")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Mu(r){let t,e,n,s,o,a,l;return n=new K({props:{size:2,weight:"medium",$$slots:{default:[bu]},$$scope:{ctx:r}}}),a=new K({props:{size:1,weight:"medium",$$slots:{default:[ku]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),x(a.$$.fragment),w(t,"class","c-sound-setting__info svelte-8awonv"),w(t,"slot","header-left")},m(i,c){y(i,t,c),b(t,e),S(n,e,null),b(t,s),b(t,o),S(a,o,null),l=!0},p(i,c){const d={};64&c&&(d.$$scope={dirty:c,ctx:i}),n.$set(d);const u={};64&c&&(u.$$scope={dirty:c,ctx:i}),a.$set(u)},i(i){l||(p(n.$$.fragment,i),p(a.$$.fragment,i),l=!0)},o(i){m(n.$$.fragment,i),m(a.$$.fragment,i),l=!1},d(i){i&&v(t),_(n),_(a)}}}function Au(r){let t,e,n;return e=new Hn({props:{size:1,checked:r[0]}}),e.$on("change",r[5]),{c(){t=k("div"),x(e.$$.fragment),w(t,"slot","header-right")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};1&o&&(a.checked=s[0]),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function Ar(r){let t,e;return t=new Ae({props:{$$slots:{"header-right":[Ru],"header-left":[Nu]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};65&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Tu(r){let t;return{c(){t=L("Test Sound")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Eu(r){let t;return{c(){t=L("Play a sample of the agent completion sound")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Nu(r){let t,e,n,s,o,a,l;return n=new K({props:{size:2,weight:"medium",$$slots:{default:[Tu]},$$scope:{ctx:r}}}),a=new K({props:{size:1,weight:"medium",$$slots:{default:[Eu]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),x(a.$$.fragment),w(t,"class","c-sound-setting__info svelte-8awonv"),w(t,"slot","header-left")},m(i,c){y(i,t,c),b(t,e),S(n,e,null),b(t,s),b(t,o),S(a,o,null),l=!0},p(i,c){const d={};64&c&&(d.$$scope={dirty:c,ctx:i}),n.$set(d);const u={};64&c&&(u.$$scope={dirty:c,ctx:i}),a.$set(u)},i(i){l||(p(n.$$.fragment,i),p(a.$$.fragment,i),l=!0)},o(i){m(n.$$.fragment,i),m(a.$$.fragment,i),l=!1},d(i){i&&v(t),_(n),_(a)}}}function Iu(r){let t;return{c(){t=L("Play")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Pu(r){let t,e;return t=new ua({props:{size:1,defaultColor:"neutral",enabled:r[0],stickyColor:!1,disabled:!r[0],onClick:r[3],tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},$$slots:{default:[Iu]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};1&s&&(o.enabled=n[0]),1&s&&(o.disabled=!n[0]),64&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Ru(r){let t,e,n;return e=new de({props:{content:r[0]?"":"Enable sound effects to test",triggerOn:[so.Hover],$$slots:{default:[Pu]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),w(t,"slot","header-right")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};1&o&&(a.content=s[0]?"":"Enable sound effects to test"),65&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function Lu(r){let t,e,n,s,o,a;t=new K({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Cu]},$$scope:{ctx:r}}}),s=new Ae({props:{$$slots:{"header-right":[Au],"header-left":[Mu]},$$scope:{ctx:r}}});let l=r[0]&&Ar(r);return{c(){x(t.$$.fragment),e=T(),n=k("div"),x(s.$$.fragment),o=T(),l&&l.c(),w(n,"class","c-sound-settings svelte-8awonv")},m(i,c){S(t,i,c),y(i,e,c),y(i,n,c),S(s,n,null),b(n,o),l&&l.m(n,null),a=!0},p(i,[c]){const d={};64&c&&(d.$$scope={dirty:c,ctx:i}),t.$set(d);const u={};65&c&&(u.$$scope={dirty:c,ctx:i}),s.$set(u),i[0]?l?(l.p(i,c),1&c&&p(l,1)):(l=Ar(i),l.c(),p(l,1),l.m(n,null)):l&&(U(),m(l,1,1,()=>{l=null}),V())},i(i){a||(p(t.$$.fragment,i),p(s.$$.fragment,i),p(l),a=!0)},o(i){m(t.$$.fragment,i),m(s.$$.fragment,i),m(l),a=!1},d(i){i&&(v(e),v(n)),_(t,i),_(s),l&&l.d()}}}function Ou(r,t,e){let n,s,o,a=B;r.$$.on_destroy.push(()=>a());const l=Wr(Io.key);return r.$$.update=()=>{16&r.$$.dirty&&e(0,s=o.enabled)},e(1,n=l.getCurrentSettings),a(),a=ze(n,i=>e(4,o=i)),[s,n,l,async function(){return await l.playAgentComplete(),"success"},o,()=>l.updateEnabled(!s)]}class zu extends ct{constructor(t){super(),dt(this,t,Ou,Lu,at,{})}}const Ue=class Ue{constructor(t){Q(this,"_swarmModeSettings",At(le));Q(this,"_isLoaded",!1);Q(this,"_pollInterval",null);Q(this,"_lastKnownSettingsHash","");Q(this,"dispose",()=>{this.stopPolling()});this._msgBroker=t,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const t=await this._msgBroker.sendToSidecar({type:ee.getSwarmModeSettings});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data)),this._isLoaded=!0}catch(t){console.warn("Failed to load swarm mode settings, using defaults:",t),this._swarmModeSettings.set(le),this._lastKnownSettingsHash=JSON.stringify(le),this._isLoaded=!0}}async updateSettings(t){try{const e=await this._msgBroker.sendToSidecar({type:ee.updateSwarmModeSettings,data:t});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data))}catch(e){throw console.error("Failed to update swarm mode settings:",e),e}}async setEnabled(t){await this.updateSettings({enabled:t})}async resetToDefaults(){await this.updateSettings(le)}updateEnabled(t){this.setEnabled(t).catch(e=>{console.error("Failed to update enabled setting:",e)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Ue.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const t=await this._msgBroker.sendToSidecar({type:ee.getSwarmModeSettings}),e=JSON.stringify(t.data);this._lastKnownSettingsHash&&e!==this._lastKnownSettingsHash&&t.data&&this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=e}catch(t){console.warn("Failed to check for swarm mode settings updates:",t)}}};Q(Ue,"key","swarmModeModel"),Q(Ue,"POLLING_INTERVAL_MS",5e3);let Bn=Ue;function Tr(r){let t,e,n,s,o;return t=new K({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Fu]},$$scope:{ctx:r}}}),s=new Ae({props:{$$slots:{"header-right":[Uu],"header-left":[Du]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),n=k("div"),x(s.$$.fragment),w(n,"class","c-agent-settings svelte-wzfxyl")},m(a,l){S(t,a,l),y(a,e,l),y(a,n,l),S(s,n,null),o=!0},p(a,l){const i={};128&l&&(i.$$scope={dirty:l,ctx:a}),t.$set(i);const c={};132&l&&(c.$$scope={dirty:l,ctx:a}),s.$set(c)},i(a){o||(p(t.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(t.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&(v(e),v(n)),_(t,a),_(s)}}}function Fu(r){let t;return{c(){t=k("div"),t.textContent="Agent Settings",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:B,d(e){e&&v(t)}}}function Zu(r){let t;return{c(){t=L("Enable Swarm Mode")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function ju(r){let t;return{c(){t=L("Allow agents to coordinate and work together on complex tasks")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Du(r){let t,e,n,s,o,a,l;return n=new K({props:{size:2,weight:"medium",$$slots:{default:[Zu]},$$scope:{ctx:r}}}),a=new K({props:{size:1,weight:"medium",$$slots:{default:[ju]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),x(a.$$.fragment),w(t,"class","c-agent-setting__info svelte-wzfxyl"),w(t,"slot","header-left")},m(i,c){y(i,t,c),b(t,e),S(n,e,null),b(t,s),b(t,o),S(a,o,null),l=!0},p(i,c){const d={};128&c&&(d.$$scope={dirty:c,ctx:i}),n.$set(d);const u={};128&c&&(u.$$scope={dirty:c,ctx:i}),a.$set(u)},i(i){l||(p(n.$$.fragment,i),p(a.$$.fragment,i),l=!0)},o(i){m(n.$$.fragment,i),m(a.$$.fragment,i),l=!1},d(i){i&&v(t),_(n),_(a)}}}function Uu(r){let t,e,n;return e=new Hn({props:{size:1,checked:r[2]}}),e.$on("change",r[6]),{c(){t=k("div"),x(e.$$.fragment),w(t,"slot","header-right")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};4&o&&(a.checked=s[2]),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function Vu(r){let t,e,n=r[0]&&r[1]&&Tr(r);return{c(){n&&n.c(),t=gt()},m(s,o){n&&n.m(s,o),y(s,t,o),e=!0},p(s,[o]){s[0]&&s[1]?n?(n.p(s,o),3&o&&p(n,1)):(n=Tr(s),n.c(),p(n,1),n.m(t.parentNode,t)):n&&(U(),m(n,1,1,()=>{n=null}),V())},i(s){e||(p(n),e=!0)},o(s){m(n),e=!1},d(s){s&&v(t),n&&n.d(s)}}}function qu(r,t,e){let n,s,o,a=B;r.$$.on_destroy.push(()=>a());let{isSwarmModeEnabled:l=!1}=t,{hasEverUsedRemoteAgent:i=!1}=t;const c=Wr(Bn.key);return r.$$set=d=>{"isSwarmModeEnabled"in d&&e(0,l=d.isSwarmModeEnabled),"hasEverUsedRemoteAgent"in d&&e(1,i=d.hasEverUsedRemoteAgent)},r.$$.update=()=>{32&r.$$.dirty&&e(2,s=o.enabled)},e(3,n=c.getCurrentSettings),a(),a=ze(n,d=>e(5,o=d)),[l,i,s,n,c,o,()=>c.updateEnabled(!s)]}class Bu extends ct{constructor(t){super(),dt(this,t,qu,Vu,at,{isSwarmModeEnabled:0,hasEverUsedRemoteAgent:1})}}function Er(r){let t,e;return t=new ou({props:{servers:r[1],onMCPServerAdd:r[12],onMCPServerSave:r[13],onMCPServerDelete:r[14],onMCPServerToggleDisable:r[15],onMCPServerJSONImport:r[16],onCancel:r[17],isMCPImportEnabled:r[3]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2&s&&(o.servers=n[1]),4096&s&&(o.onMCPServerAdd=n[12]),8192&s&&(o.onMCPServerSave=n[13]),16384&s&&(o.onMCPServerDelete=n[14]),32768&s&&(o.onMCPServerToggleDisable=n[15]),65536&s&&(o.onMCPServerJSONImport=n[16]),131072&s&&(o.onCancel=n[17]),8&s&&(o.isMCPImportEnabled=n[3]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Nr(r){let t,e;return t=new _u({props:{supportedShells:r[18],selectedShell:r[19],startupScript:r[20],onShellSelect:r[21],onStartupScriptChange:r[22]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};262144&s&&(o.supportedShells=n[18]),524288&s&&(o.selectedShell=n[19]),1048576&s&&(o.startupScript=n[20]),2097152&s&&(o.onShellSelect=n[21]),4194304&s&&(o.onStartupScriptChange=n[22]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Ir(r){let t,e;return t=new zu({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Pr(r){let t,e;return t=new Bu({props:{isSwarmModeEnabled:r[7],hasEverUsedRemoteAgent:r[8]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};128&s&&(o.isSwarmModeEnabled=n[7]),256&s&&(o.hasEverUsedRemoteAgent=n[8]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Ju(r){let t,e,n,s,o,a,l;e=new $c({props:{title:"Services",tools:r[0],onAuthenticate:r[9],onRevokeAccess:r[10],onToolApprovalConfigChange:r[11]}});let i=r[2]&&Er(r),c=r[4]&&Nr(r),d=r[5]&&Ir(),u=r[6]&&Pr(r);return{c(){t=k("div"),x(e.$$.fragment),n=T(),i&&i.c(),s=T(),c&&c.c(),o=T(),d&&d.c(),a=T(),u&&u.c(),w(t,"class","c-settings-tools svelte-181yusq")},m(f,$){y(f,t,$),S(e,t,null),b(t,n),i&&i.m(t,null),b(t,s),c&&c.m(t,null),b(t,o),d&&d.m(t,null),b(t,a),u&&u.m(t,null),l=!0},p(f,[$]){const g={};1&$&&(g.tools=f[0]),512&$&&(g.onAuthenticate=f[9]),1024&$&&(g.onRevokeAccess=f[10]),2048&$&&(g.onToolApprovalConfigChange=f[11]),e.$set(g),f[2]?i?(i.p(f,$),4&$&&p(i,1)):(i=Er(f),i.c(),p(i,1),i.m(t,s)):i&&(U(),m(i,1,1,()=>{i=null}),V()),f[4]?c?(c.p(f,$),16&$&&p(c,1)):(c=Nr(f),c.c(),p(c,1),c.m(t,o)):c&&(U(),m(c,1,1,()=>{c=null}),V()),f[5]?d?32&$&&p(d,1):(d=Ir(),d.c(),p(d,1),d.m(t,a)):d&&(U(),m(d,1,1,()=>{d=null}),V()),f[6]?u?(u.p(f,$),64&$&&p(u,1)):(u=Pr(f),u.c(),p(u,1),u.m(t,null)):u&&(U(),m(u,1,1,()=>{u=null}),V())},i(f){l||(p(e.$$.fragment,f),p(i),p(c),p(d),p(u),l=!0)},o(f){m(e.$$.fragment,f),m(i),m(c),m(d),m(u),l=!1},d(f){f&&v(t),_(e),i&&i.d(),c&&c.d(),d&&d.d(),u&&u.d()}}}function Gu(r,t,e){let{tools:n=[]}=t,{servers:s=[]}=t,{isMCPEnabled:o=!0}=t,{isMCPImportEnabled:a=!0}=t,{isTerminalEnabled:l=!0}=t,{isSoundCategoryEnabled:i=!1}=t,{isAgentCategoryEnabled:c=!1}=t,{isSwarmModeFeatureFlagEnabled:d=!1}=t,{hasEverUsedRemoteAgent:u=!1}=t,{onAuthenticate:f}=t,{onRevokeAccess:$}=t,{onToolApprovalConfigChange:g=()=>{}}=t,{onMCPServerAdd:h}=t,{onMCPServerSave:C}=t,{onMCPServerDelete:N}=t,{onMCPServerToggleDisable:P}=t,{onMCPServerJSONImport:A}=t,{onCancel:M}=t,{supportedShells:I=[]}=t,{selectedShell:R}=t,{startupScript:J}=t,{onShellSelect:tt=()=>{}}=t,{onStartupScriptChange:et=()=>{}}=t;return r.$$set=E=>{"tools"in E&&e(0,n=E.tools),"servers"in E&&e(1,s=E.servers),"isMCPEnabled"in E&&e(2,o=E.isMCPEnabled),"isMCPImportEnabled"in E&&e(3,a=E.isMCPImportEnabled),"isTerminalEnabled"in E&&e(4,l=E.isTerminalEnabled),"isSoundCategoryEnabled"in E&&e(5,i=E.isSoundCategoryEnabled),"isAgentCategoryEnabled"in E&&e(6,c=E.isAgentCategoryEnabled),"isSwarmModeFeatureFlagEnabled"in E&&e(7,d=E.isSwarmModeFeatureFlagEnabled),"hasEverUsedRemoteAgent"in E&&e(8,u=E.hasEverUsedRemoteAgent),"onAuthenticate"in E&&e(9,f=E.onAuthenticate),"onRevokeAccess"in E&&e(10,$=E.onRevokeAccess),"onToolApprovalConfigChange"in E&&e(11,g=E.onToolApprovalConfigChange),"onMCPServerAdd"in E&&e(12,h=E.onMCPServerAdd),"onMCPServerSave"in E&&e(13,C=E.onMCPServerSave),"onMCPServerDelete"in E&&e(14,N=E.onMCPServerDelete),"onMCPServerToggleDisable"in E&&e(15,P=E.onMCPServerToggleDisable),"onMCPServerJSONImport"in E&&e(16,A=E.onMCPServerJSONImport),"onCancel"in E&&e(17,M=E.onCancel),"supportedShells"in E&&e(18,I=E.supportedShells),"selectedShell"in E&&e(19,R=E.selectedShell),"startupScript"in E&&e(20,J=E.startupScript),"onShellSelect"in E&&e(21,tt=E.onShellSelect),"onStartupScriptChange"in E&&e(22,et=E.onStartupScriptChange)},[n,s,o,a,l,i,c,d,u,f,$,g,h,C,N,P,A,M,I,R,J,tt,et]}class Hu extends ct{constructor(t){super(),dt(this,t,Gu,Ju,at,{tools:0,servers:1,isMCPEnabled:2,isMCPImportEnabled:3,isTerminalEnabled:4,isSoundCategoryEnabled:5,isAgentCategoryEnabled:6,isSwarmModeFeatureFlagEnabled:7,hasEverUsedRemoteAgent:8,onAuthenticate:9,onRevokeAccess:10,onToolApprovalConfigChange:11,onMCPServerAdd:12,onMCPServerSave:13,onMCPServerDelete:14,onMCPServerToggleDisable:15,onMCPServerJSONImport:16,onCancel:17,supportedShells:18,selectedShell:19,startupScript:20,onShellSelect:21,onStartupScriptChange:22})}}function Wu(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=wt(s,n[o]);return{c(){t=Ot("svg"),e=new nn(!0),this.h()},l(o){t=sn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=rn(t);e=on(a,!0),a.forEach(v),this.h()},h(){e.a=null,Gt(t,s)},m(o,a){an(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',t)},p(o,[a]){Gt(t,s=se(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&a&&o[0]]))},i:B,o:B,d(o){o&&v(t)}}}function Ku(r,t,e){return r.$$set=n=>{e(0,t=wt(wt({},t),Dt(n)))},[t=Dt(t)]}class Yu extends ct{constructor(t){super(),dt(this,t,Ku,Wu,at,{})}}function Xu(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=wt(s,n[o]);return{c(){t=Ot("svg"),e=new nn(!0),this.h()},l(o){t=sn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=rn(t);e=on(a,!0),a.forEach(v),this.h()},h(){e.a=null,Gt(t,s)},m(o,a){an(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',t)},p(o,[a]){Gt(t,s=se(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&o[0]]))},i:B,o:B,d(o){o&&v(t)}}}function Qu(r,t,e){return r.$$set=n=>{e(0,t=wt(wt({},t),Dt(n)))},[t=Dt(t)]}class tp extends ct{constructor(t){super(),dt(this,t,Qu,Xu,at,{})}}function ep(r){let t,e,n,s;function o(l){r[6](l)}let a={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:r[3]};return r[0]!==void 0&&(a.value=r[0]),e=new ga({props:a}),_t.push(()=>Ct(e,"value",o)),e.$on("focus",r[7]),{c(){t=k("div"),x(e.$$.fragment),w(t,"class","c-user-guidelines-category__input svelte-10borzo")},m(l,i){y(l,t,i),S(e,t,null),s=!0},p(l,[i]){const c={};!n&&1&i&&(n=!0,c.value=l[0],bt(()=>n=!1)),e.$set(c)},i(l){s||(p(e.$$.fragment,l),s=!0)},o(l){m(e.$$.fragment,l),s=!1},d(l){l&&v(t),_(e)}}}function np(r,t,e){let n;const s=Gn();let{userGuidelines:o=""}=t,{userGuidelinesLengthLimit:a}=t,{updateUserGuideline:l=()=>!1}=t;const i=At(void 0);function c(){const d=o.trim();if(n!==d){if(!l(d))throw a&&d.length>a?`The user guideline must be less than ${a} character long`:"An error occurred updating the user";as(i,n=d,n)}}return Zt(r,i,d=>e(8,n=d)),Kr(()=>{as(i,n=o.trim(),n)}),Yr(()=>{c()}),r.$$set=d=>{"userGuidelines"in d&&e(0,o=d.userGuidelines),"userGuidelinesLengthLimit"in d&&e(4,a=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&e(5,l=d.updateUserGuideline)},[o,s,i,c,a,l,function(d){o=d,e(0,o)},d=>{s("focus",d)}]}class So extends ct{constructor(t){super(),dt(this,t,np,ep,at,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}class sp{constructor(t,e,n){Q(this,"_showCreateRuleDialog",At(!1));Q(this,"_createRuleError",At(""));Q(this,"_extensionClient");this._host=t,this._msgBroker=e,this._rulesModel=n;const s=new va;this._extensionClient=new ya(t,e,s)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(t){if(t&&t.trim()){this._createRuleError.set("");try{const e=await this._rulesModel.createRule(t.trim());e&&e.path&&await this.openRule(e.path),this._extensionClient.reportAgentSessionEvent({eventName:On.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:mn.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const n=`Failed to create rule "${t.trim()}"`;this._createRuleError.set(n)}}else this.hideCreateRuleDialog()}async openRule(t){try{const e=await this._rulesModel.getWorkspaceRoot();t===cs?this._extensionClient.openFile({repoRoot:e,pathName:cs}):this._extensionClient.openFile({repoRoot:e,pathName:`${sa}/${ra}/${t}`})}catch(e){console.error("Failed to open rule:",e)}}async deleteRule(t){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(t)}catch(e){console.error("Failed to delete rule:",e)}}async selectFileToImport(){try{const t=await this._msgBroker.send({type:ft.triggerImportDialogRequest},1e5);if(t.data.selectedPaths&&t.data.selectedPaths.length>0){const e=await this._rulesModel.processSelectedPaths(t.data.selectedPaths);this._showImportNotification(e),this._reportSelectedImportMetrics(e)}}catch(t){console.error("Failed to import files:",t)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(t){const e=await this._rulesModel.processAutoImportSelection(t);return this._showImportNotification(e),this._reportAutoImportMetrics(e),e}_showImportNotification(t){let e;t.importedRulesCount===0?e=t.source?`No new rules imported from ${t.source}`:"No new rules imported":(e=`Successfully imported ${t.importedRulesCount} rule${t.importedRulesCount!==1?"s":""}`,t.duplicatesCount&&t.duplicatesCount>0&&(e+=` and skipped ${t.duplicatesCount} duplicate${t.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:e,type:t.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(t){const e=t.directoryOrFile==="directory"?mn.selectedDirectory:(t.directoryOrFile,mn.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:On.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:e,numFiles:t.importedRulesCount,source:""}}})}_reportAutoImportMetrics(t){this._extensionClient.reportAgentSessionEvent({eventName:On.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:mn.auto,numFiles:t.importedRulesCount,source:t.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}function rp(r){let t;return{c(){t=L("Enter a name for the new rule file (e.g., architecture.md):")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Rr(r){let t,e;return t=new kn({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[ap],default:[op]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};4098&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function op(r){let t;return{c(){t=L(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n&&ot(t,e[1])},d(e){e&&v(t)}}}function ap(r){let t,e;return t=new Wn({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function ip(r){let t,e,n,s,o,a,l,i;function c($){r[9]($)}function d($){r[10]($)}t=new K({props:{size:2,color:"secondary",$$slots:{default:[rp]},$$scope:{ctx:r}}});let u={placeholder:"rule-name.md",disabled:r[4]};r[3]!==void 0&&(u.value=r[3]),r[2]!==void 0&&(u.textInput=r[2]),n=new me({props:u}),_t.push(()=>Ct(n,"value",c)),_t.push(()=>Ct(n,"textInput",d)),n.$on("keydown",r[8]);let f=r[1]&&Rr(r);return{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment),a=T(),f&&f.c(),l=gt()},m($,g){S(t,$,g),y($,e,g),S(n,$,g),y($,a,g),f&&f.m($,g),y($,l,g),i=!0},p($,g){const h={};4096&g&&(h.$$scope={dirty:g,ctx:$}),t.$set(h);const C={};16&g&&(C.disabled=$[4]),!s&&8&g&&(s=!0,C.value=$[3],bt(()=>s=!1)),!o&&4&g&&(o=!0,C.textInput=$[2],bt(()=>o=!1)),n.$set(C),$[1]?f?(f.p($,g),2&g&&p(f,1)):(f=Rr($),f.c(),p(f,1),f.m(l.parentNode,l)):f&&(U(),m(f,1,1,()=>{f=null}),V())},i($){i||(p(t.$$.fragment,$),p(n.$$.fragment,$),p(f),i=!0)},o($){m(t.$$.fragment,$),m(n.$$.fragment,$),m(f),i=!1},d($){$&&(v(e),v(a),v(l)),_(t,$),_(n,$),f&&f.d($)}}}function lp(r){let t;return{c(){t=L("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function cp(r){let t,e=r[4]?"Creating...":"Create";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){16&s&&e!==(e=n[4]?"Creating...":"Create")&&ot(t,e)},d(n){n&&v(t)}}}function dp(r){let t,e,n,s,o;return e=new xt({props:{variant:"solid",color:"neutral",disabled:r[4],$$slots:{default:[lp]},$$scope:{ctx:r}}}),e.$on("click",r[6]),s=new xt({props:{variant:"solid",color:"accent",disabled:!r[3].trim()||r[4],loading:r[4],$$slots:{default:[cp]},$$scope:{ctx:r}}}),s.$on("click",r[5]),{c(){t=k("div"),x(e.$$.fragment),n=T(),x(s.$$.fragment),w(t,"slot","footer")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),S(s,t,null),o=!0},p(a,l){const i={};16&l&&(i.disabled=a[4]),4096&l&&(i.$$scope={dirty:l,ctx:a}),e.$set(i);const c={};24&l&&(c.disabled=!a[3].trim()||a[4]),16&l&&(c.loading=a[4]),4112&l&&(c.$$scope={dirty:l,ctx:a}),s.$set(c)},i(a){o||(p(e.$$.fragment,a),p(s.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),m(s.$$.fragment,a),o=!1},d(a){a&&v(t),_(e),_(s)}}}function up(r){let t,e;return t=new ao({props:{show:r[0],title:"Create New Rule",ariaLabelledBy:"dialog-title",preventBackdropClose:r[4],preventEscapeClose:r[4],$$slots:{footer:[dp],body:[ip]},$$scope:{ctx:r}}}),t.$on("cancel",r[6]),t.$on("keydown",r[7]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,[s]){const o={};1&s&&(o.show=n[0]),16&s&&(o.preventBackdropClose=n[4]),16&s&&(o.preventEscapeClose=n[4]),4126&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function pp(r,t,e){const n=Gn();let s,{show:o=!1}=t,{errorMessage:a=""}=t,l="",i=!1;function c(){l.trim()&&!i&&(e(4,i=!0),n("create",l.trim()))}function d(){i||(n("cancel"),e(3,l=""))}return r.$$set=u=>{"show"in u&&e(0,o=u.show),"errorMessage"in u&&e(1,a=u.errorMessage)},r.$$.update=()=>{5&r.$$.dirty&&o&&s&&setTimeout(()=>s==null?void 0:s.focus(),100),3&r.$$.dirty&&(o&&!a||e(4,i=!1)),3&r.$$.dirty&&(o||a||e(3,l=""))},[o,a,s,l,i,c,d,function(u){i||u.detail.key==="Enter"&&(u.detail.preventDefault(),c())},function(u){i||(u.key==="Enter"?(u.preventDefault(),c()):u.key==="Escape"&&(u.preventDefault(),d()))},function(u){l=u,e(3,l),e(0,o),e(1,a)},function(u){s=u,e(2,s)}]}class mp extends ct{constructor(t){super(),dt(this,t,pp,up,at,{show:0,errorMessage:1})}}function Lr(r,t,e){const n=r.slice();return n[18]=t[e],n}function fp(r){let t,e,n,s,o,a,l,i,c;function d(h){r[15](h)}function u(h){r[16](h)}t=new K({props:{size:2,color:"secondary",$$slots:{default:[gp]},$$scope:{ctx:r}}});let f={triggerOn:r[1].length===0?[]:void 0,$$slots:{default:[xp]},$$scope:{ctx:r}};r[7]!==void 0&&(f.requestClose=r[7]),r[6]!==void 0&&(f.focusedIndex=r[6]),n=new yt.Root({props:f}),_t.push(()=>Ct(n,"requestClose",d)),_t.push(()=>Ct(n,"focusedIndex",u));let $=r[3]&&Fr(r),g=r[4]&&Zr(r);return{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment),a=T(),$&&$.c(),l=T(),g&&g.c(),i=gt()},m(h,C){S(t,h,C),y(h,e,C),S(n,h,C),y(h,a,C),$&&$.m(h,C),y(h,l,C),g&&g.m(h,C),y(h,i,C),c=!0},p(h,C){const N={};2097152&C&&(N.$$scope={dirty:C,ctx:h}),t.$set(N);const P={};2&C&&(P.triggerOn=h[1].length===0?[]:void 0),2097442&C&&(P.$$scope={dirty:C,ctx:h}),!s&&128&C&&(s=!0,P.requestClose=h[7],bt(()=>s=!1)),!o&&64&C&&(o=!0,P.focusedIndex=h[6],bt(()=>o=!1)),n.$set(P),h[3]?$?($.p(h,C),8&C&&p($,1)):($=Fr(h),$.c(),p($,1),$.m(l.parentNode,l)):$&&(U(),m($,1,1,()=>{$=null}),V()),h[4]?g?(g.p(h,C),16&C&&p(g,1)):(g=Zr(h),g.c(),p(g,1),g.m(i.parentNode,i)):g&&(U(),m(g,1,1,()=>{g=null}),V())},i(h){c||(p(t.$$.fragment,h),p(n.$$.fragment,h),p($),p(g),c=!0)},o(h){m(t.$$.fragment,h),m(n.$$.fragment,h),m($),m(g),c=!1},d(h){h&&(v(e),v(a),v(l),v(i)),_(t,h),_(n,h),$&&$.d(h),g&&g.d(h)}}}function $p(r){let t;return{c(){t=k("input"),w(t,"type","text"),t.value="No existing rules found",t.readOnly=!0,w(t,"class","c-dropdown-input svelte-z1s6x7")},m(e,n){y(e,t,n)},p:B,i:B,o:B,d(e){e&&v(t)}}}function gp(r){let t;return{c(){t=L("Select existing rules to auto import to .augment/rules")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function hp(r){let t,e,n,s,o,a;return o=new Kn({props:{class:"c-dropdown-chevron"}}),{c(){t=k("div"),e=k("input"),s=T(),x(o.$$.fragment),w(e,"type","text"),e.value=n=r[5]?r[5].label:"Existing rules",e.readOnly=!0,w(e,"class","c-dropdown-input svelte-z1s6x7"),w(t,"class","c-dropdown-trigger svelte-z1s6x7")},m(l,i){y(l,t,i),b(t,e),b(t,s),S(o,t,null),a=!0},p(l,i){(!a||32&i&&n!==(n=l[5]?l[5].label:"Existing rules")&&e.value!==n)&&(e.value=n)},i(l){a||(p(o.$$.fragment,l),a=!0)},o(l){m(o.$$.fragment,l),a=!1},d(l){l&&v(t),_(o)}}}function vp(r){let t,e=r[18].label+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){2&s&&e!==(e=n[18].label+"")&&ot(t,e)},d(n){n&&v(t)}}}function Or(r){var s;let t,e;function n(){return r[14](r[18])}return t=new yt.Item({props:{onSelect:n,highlight:((s=r[5])==null?void 0:s.label)===r[18].label,$$slots:{default:[vp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(o,a){S(t,o,a),e=!0},p(o,a){var i;r=o;const l={};2&a&&(l.onSelect=n),34&a&&(l.highlight=((i=r[5])==null?void 0:i.label)===r[18].label),2097154&a&&(l.$$scope={dirty:a,ctx:r}),t.$set(l)},i(o){e||(p(t.$$.fragment,o),e=!0)},o(o){m(t.$$.fragment,o),e=!1},d(o){_(t,o)}}}function zr(r){let t,e,n,s;return t=new yt.Separator({}),n=new yt.Label({props:{$$slots:{default:[yp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment)},m(o,a){S(t,o,a),y(o,e,a),S(n,o,a),s=!0},p(o,a){const l={};2097442&a&&(l.$$scope={dirty:a,ctx:o}),n.$set(l)},i(o){s||(p(t.$$.fragment,o),p(n.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(t,o),_(n,o)}}}function yp(r){var n;let t,e=(r[8]!==void 0?r[1][r[8]].description:(n=r[5])==null?void 0:n.description)+"";return{c(){t=L(e)},m(s,o){y(s,t,o)},p(s,o){var a;290&o&&e!==(e=(s[8]!==void 0?s[1][s[8]].description:(a=s[5])==null?void 0:a.description)+"")&&ot(t,e)},d(s){s&&v(t)}}}function wp(r){let t,e,n,s=mt(r[1]),o=[];for(let i=0;i<s.length;i+=1)o[i]=Or(Lr(r,s,i));const a=i=>m(o[i],1,1,()=>{o[i]=null});let l=(r[8]!==void 0||r[5])&&zr(r);return{c(){for(let i=0;i<o.length;i+=1)o[i].c();t=T(),l&&l.c(),e=gt()},m(i,c){for(let d=0;d<o.length;d+=1)o[d]&&o[d].m(i,c);y(i,t,c),l&&l.m(i,c),y(i,e,c),n=!0},p(i,c){if(546&c){let d;for(s=mt(i[1]),d=0;d<s.length;d+=1){const u=Lr(i,s,d);o[d]?(o[d].p(u,c),p(o[d],1)):(o[d]=Or(u),o[d].c(),p(o[d],1),o[d].m(t.parentNode,t))}for(U(),d=s.length;d<o.length;d+=1)a(d);V()}i[8]!==void 0||i[5]?l?(l.p(i,c),288&c&&p(l,1)):(l=zr(i),l.c(),p(l,1),l.m(e.parentNode,e)):l&&(U(),m(l,1,1,()=>{l=null}),V())},i(i){if(!n){for(let c=0;c<s.length;c+=1)p(o[c]);p(l),n=!0}},o(i){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)m(o[c]);m(l),n=!1},d(i){i&&(v(t),v(e)),ve(o,i),l&&l.d(i)}}}function xp(r){let t,e,n,s;return t=new yt.Trigger({props:{$$slots:{default:[hp]},$$scope:{ctx:r}}}),n=new yt.Content({props:{align:"start",side:"bottom",$$slots:{default:[wp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment)},m(o,a){S(t,o,a),y(o,e,a),S(n,o,a),s=!0},p(o,a){const l={};2097184&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};2097442&a&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(p(t.$$.fragment,o),p(n.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(t,o),_(n,o)}}}function Fr(r){let t,e;return t=new kn({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[_p],default:[Sp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2097160&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Sp(r){let t;return{c(){t=L(r[3])},m(e,n){y(e,t,n)},p(e,n){8&n&&ot(t,e[3])},d(e){e&&v(t)}}}function _p(r){let t,e;return t=new Wn({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Zr(r){let t,e;return t=new kn({props:{variant:"soft",color:"success",size:1,$$slots:{icon:[bp],default:[Cp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2097168&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Cp(r){let t;return{c(){t=L(r[4])},m(e,n){y(e,t,n)},p(e,n){16&n&&ot(t,e[4])},d(e){e&&v(t)}}}function bp(r){let t,e;return t=new Qo({props:{slot:"icon"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function kp(r){let t,e,n,s;const o=[$p,fp],a=[];function l(i,c){return i[1].length===0?0:1}return e=l(r),n=a[e]=o[e](r),{c(){t=k("div"),n.c(),w(t,"slot","body"),w(t,"class","c-auto-import-rules-dialog svelte-z1s6x7")},m(i,c){y(i,t,c),a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e===d?a[e].p(i,c):(U(),m(a[d],1,1,()=>{a[d]=null}),V(),n=a[e],n?n.p(i,c):(n=a[e]=o[e](i),n.c()),p(n,1),n.m(t,null))},i(i){s||(p(n),s=!0)},o(i){m(n),s=!1},d(i){i&&v(t),a[e].d()}}}function Mp(r){let t;return{c(){t=L("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function jr(r){let t,e;return t=new xt({props:{color:"accent",variant:"solid",disabled:!r[5]||r[2],loading:r[2],$$slots:{default:[Ap]},$$scope:{ctx:r}}}),t.$on("click",r[10]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};36&s&&(o.disabled=!n[5]||n[2]),4&s&&(o.loading=n[2]),2097156&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Ap(r){let t,e=r[2]?"Importing...":"Import ";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){4&s&&e!==(e=n[2]?"Importing...":"Import ")&&ot(t,e)},d(n){n&&v(t)}}}function Tp(r){let t,e,n,s;e=new xt({props:{variant:"solid",color:"neutral",disabled:r[2],$$slots:{default:[Mp]},$$scope:{ctx:r}}}),e.$on("click",r[11]);let o=r[1].length>0&&jr(r);return{c(){t=k("div"),x(e.$$.fragment),n=T(),o&&o.c(),w(t,"slot","footer")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),o&&o.m(t,null),s=!0},p(a,l){const i={};4&l&&(i.disabled=a[2]),2097152&l&&(i.$$scope={dirty:l,ctx:a}),e.$set(i),a[1].length>0?o?(o.p(a,l),2&l&&p(o,1)):(o=jr(a),o.c(),p(o,1),o.m(t,null)):o&&(U(),m(o,1,1,()=>{o=null}),V())},i(a){s||(p(e.$$.fragment,a),p(o),s=!0)},o(a){m(e.$$.fragment,a),m(o),s=!1},d(a){a&&v(t),_(e),o&&o.d()}}}function Ep(r){let t,e,n,s;return t=new ao({props:{show:r[0],title:"Auto Import Rules",ariaLabelledBy:"dialog-title",preventBackdropClose:r[2],preventEscapeClose:r[2],$$slots:{footer:[Tp],body:[kp]},$$scope:{ctx:r}}}),t.$on("cancel",r[11]),{c(){x(t.$$.fragment)},m(o,a){S(t,o,a),e=!0,n||(s=zt(window,"keydown",r[12]),n=!0)},p(o,[a]){const l={};1&a&&(l.show=o[0]),4&a&&(l.preventBackdropClose=o[2]),4&a&&(l.preventEscapeClose=o[2]),2097662&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)},i(o){e||(p(t.$$.fragment,o),e=!0)},o(o){m(t.$$.fragment,o),e=!1},d(o){_(t,o),n=!1,s()}}}function Np(r,t,e){let n,s,o=B,a=()=>(o(),o=ze(i,A=>e(8,s=A)),i);r.$$.on_destroy.push(()=>o());const l=Gn();let i,{show:c=!1}=t,{options:d=[]}=t,{isLoading:u=!1}=t,{errorMessage:f=""}=t,{successMessage:$=""}=t,g=n;a();let h=()=>{};function C(A){e(5,g=A),h()}function N(){g&&!u&&l("select",g)}function P(){u||(l("cancel"),e(5,g=n))}return r.$$set=A=>{"show"in A&&e(0,c=A.show),"options"in A&&e(1,d=A.options),"isLoading"in A&&e(2,u=A.isLoading),"errorMessage"in A&&e(3,f=A.errorMessage),"successMessage"in A&&e(4,$=A.successMessage)},r.$$.update=()=>{2&r.$$.dirty&&e(13,n=d.length>0?d[0]:null),8193&r.$$.dirty&&c&&e(5,g=n)},[c,d,u,f,$,g,i,h,s,C,N,P,function(A){c&&!u&&(A.key==="Escape"?(A.preventDefault(),P()):A.key==="Enter"&&g&&(A.preventDefault(),N()))},n,A=>C(A),function(A){h=A,e(7,h)},function(A){i=A,a(e(6,i))}]}class Ip extends ct{constructor(t){super(),dt(this,t,Np,Ep,at,{show:0,options:1,isLoading:2,errorMessage:3,successMessage:4})}}function Dr(r,t,e){const n=r.slice();return n[35]=t[e],n}function Ur(r,t,e){const n=r.slice();return n[38]=t[e],n}function Pp(r){let t;return{c(){t=L("Rules")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Rp(r){let t;return{c(){t=L("Learn more")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Lp(r){let t,e,n=[],s=new Map,o=mt(r[10]);const a=l=>l[38].path;for(let l=0;l<o.length;l+=1){let i=Ur(r,o,l),c=a(i);s.set(c,n[l]=Vr(c,i))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();t=gt()},m(l,i){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,i);y(l,t,i),e=!0},p(l,i){99328&i[0]&&(o=mt(l[10]),U(),n=ye(n,i,a,1,l,o,s,t.parentNode,we,Vr,t,Ur),V())},i(l){if(!e){for(let i=0;i<o.length;i+=1)p(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)m(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d(l)}}}function Op(r){let t,e,n;return e=new K({props:{size:1,color:"neutral",$$slots:{default:[Bp]},$$scope:{ctx:r}}}),{c(){t=k("div"),x(e.$$.fragment),w(t,"class","c-rules-list-empty svelte-5krsve")},m(s,o){y(s,t,o),S(e,t,null),n=!0},p(s,o){const a={};1024&o[1]&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&v(t),_(e)}}}function zp(r){let t,e;return t=new ea({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Fp(r){let t,e;return t=new de({props:{content:"No description found",$$slots:{default:[Zp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Zp(r){let t,e;return t=new Wn({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function jp(r){let t,e=r[38].path+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){1024&s[0]&&e!==(e=n[38].path+"")&&ot(t,e)},d(n){n&&v(t)}}}function Dp(r){let t,e,n,s,o,a,l,i,c;const d=[Fp,zp],u=[];function f($,g){return $[38].type!==Oo.AGENT_REQUESTED||$[38].description?1:0}return n=f(r),s=u[n]=d[n](r),l=new K({props:{size:1,color:"neutral",$$slots:{default:[jp]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),s.c(),o=T(),a=k("div"),x(l.$$.fragment),i=T(),w(e,"class","l-icon-wrapper svelte-5krsve"),w(a,"class","c-rule-item-path svelte-5krsve"),w(t,"class","c-rule-item-info svelte-5krsve"),w(t,"slot","header-left")},m($,g){y($,t,g),b(t,e),u[n].m(e,null),b(t,o),b(t,a),S(l,a,null),b(t,i),c=!0},p($,g){let h=n;n=f($),n!==h&&(U(),m(u[h],1,1,()=>{u[h]=null}),V(),s=u[n],s||(s=u[n]=d[n]($),s.c()),p(s,1),s.m(e,null));const C={};1024&g[0]|1024&g[1]&&(C.$$scope={dirty:g,ctx:$}),l.$set(C)},i($){c||(p(s),p(l.$$.fragment,$),c=!0)},o($){m(s),m(l.$$.fragment,$),c=!1},d($){$&&v(t),u[n].d(),_(l)}}}function Up(r){let t,e;return t=new ma({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Vp(r){let t,e;return t=new ta({props:{slot:"iconRight"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function qp(r){let t,e,n,s,o,a,l,i,c,d;function u(...f){return r[26](r[38],...f)}return s=new wa({props:{rule:r[38],onSave:u}}),a=new xt({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Up]},$$scope:{ctx:r}}}),a.$on("click",function(...f){return r[27](r[38],...f)}),i=new xt({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Vp]},$$scope:{ctx:r}}}),i.$on("click",function(...f){return r[28](r[38],...f)}),{c(){t=k("div"),e=k("div"),n=k("div"),x(s.$$.fragment),o=T(),x(a.$$.fragment),l=T(),x(i.$$.fragment),c=T(),w(n,"class","c-rules-dropdown svelte-5krsve"),w(e,"class","status-controls svelte-5krsve"),w(t,"class","server-actions"),w(t,"slot","header-right")},m(f,$){y(f,t,$),b(t,e),b(e,n),S(s,n,null),b(e,o),S(a,e,null),b(e,l),S(i,e,null),b(t,c),d=!0},p(f,$){r=f;const g={};1024&$[0]&&(g.rule=r[38]),1024&$[0]&&(g.onSave=u),s.$set(g);const h={};1024&$[1]&&(h.$$scope={dirty:$,ctx:r}),a.$set(h);const C={};1024&$[1]&&(C.$$scope={dirty:$,ctx:r}),i.$set(C)},i(f){d||(p(s.$$.fragment,f),p(a.$$.fragment,f),p(i.$$.fragment,f),d=!0)},o(f){m(s.$$.fragment,f),m(a.$$.fragment,f),m(i.$$.fragment,f),d=!1},d(f){f&&v(t),_(s),_(a),_(i)}}}function Vr(r,t){let e,n,s;return n=new ho({props:{isClickable:!0,$$slots:{"header-right":[qp],"header-left":[Dp]},$$scope:{ctx:t}}}),n.$on("click",function(){return t[29](t[38])}),{key:r,first:null,c(){e=gt(),x(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),S(n,o,a),s=!0},p(o,a){t=o;const l={};1024&a[0]|1024&a[1]&&(l.$$scope={dirty:a,ctx:t}),n.$set(l)},i(o){s||(p(n.$$.fragment,o),s=!0)},o(o){m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(n,o)}}}function Bp(r){let t;return{c(){t=L("No rules files found")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Jp(r){let t,e,n,s,o;return e=new ln({}),{c(){t=k("div"),x(e.$$.fragment),n=T(),s=k("span"),s.textContent="Create new rule file",w(t,"class","c-rules-actions-button-content svelte-5krsve")},m(a,l){y(a,t,l),S(e,t,null),b(t,n),b(t,s),o=!0},p:B,i(a){o||(p(e.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),o=!1},d(a){a&&v(t),_(e)}}}function Gp(r){let t,e,n,s,o,a,l;return e=new to({}),a=new Kn({}),{c(){t=k("div"),x(e.$$.fragment),n=T(),s=k("span"),s.textContent="Import rules",o=T(),x(a.$$.fragment),w(t,"class","c-rules-actions-button-content svelte-5krsve")},m(i,c){y(i,t,c),S(e,t,null),b(t,n),b(t,s),b(t,o),S(a,t,null),l=!0},p:B,i(i){l||(p(e.$$.fragment,i),p(a.$$.fragment,i),l=!0)},o(i){m(e.$$.fragment,i),m(a.$$.fragment,i),l=!1},d(i){i&&v(t),_(e),_(a)}}}function Hp(r){let t,e;return t=new xt({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Gp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};1024&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function Wp(r){let t,e=r[35].label+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p:B,d(n){n&&v(t)}}}function qr(r,t){let e,n,s;return n=new yt.Item({props:{onSelect:function(){return t[31](t[35])},$$slots:{default:[Wp]},$$scope:{ctx:t}}}),{key:r,first:null,c(){e=gt(),x(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),S(n,o,a),s=!0},p(o,a){t=o;const l={};1024&a[1]&&(l.$$scope={dirty:a,ctx:t}),n.$set(l)},i(o){s||(p(n.$$.fragment,o),s=!0)},o(o){m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(n,o)}}}function Br(r){let t,e,n,s;return t=new yt.Separator({}),n=new yt.Label({props:{$$slots:{default:[Kp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment)},m(o,a){S(t,o,a),y(o,e,a),S(n,o,a),s=!0},p(o,a){const l={};2048&a[0]|1024&a[1]&&(l.$$scope={dirty:a,ctx:o}),n.$set(l)},i(o){s||(p(t.$$.fragment,o),p(n.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(t,o),_(n,o)}}}function Kp(r){let t,e=(r[11]!==void 0?r[20][r[11]].description:r[20][0])+"";return{c(){t=L(e)},m(n,s){y(n,t,s)},p(n,s){2048&s[0]&&e!==(e=(n[11]!==void 0?n[20][n[11]].description:n[20][0])+"")&&ot(t,e)},d(n){n&&v(t)}}}function Yp(r){let t,e,n,s=[],o=new Map,a=mt(r[20]);const l=c=>c[35].id;for(let c=0;c<a.length;c+=1){let d=Dr(r,a,c),u=l(d);o.set(u,s[c]=qr(u,d))}let i=r[11]!==void 0&&Br(r);return{c(){for(let c=0;c<s.length;c+=1)s[c].c();t=T(),i&&i.c(),e=gt()},m(c,d){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(c,d);y(c,t,d),i&&i.m(c,d),y(c,e,d),n=!0},p(c,d){3145728&d[0]&&(a=mt(c[20]),U(),s=ye(s,d,l,1,c,a,o,t.parentNode,we,qr,t,Dr),V()),c[11]!==void 0?i?(i.p(c,d),2048&d[0]&&p(i,1)):(i=Br(c),i.c(),p(i,1),i.m(e.parentNode,e)):i&&(U(),m(i,1,1,()=>{i=null}),V())},i(c){if(!n){for(let d=0;d<a.length;d+=1)p(s[d]);p(i),n=!0}},o(c){for(let d=0;d<s.length;d+=1)m(s[d]);m(i),n=!1},d(c){c&&(v(t),v(e));for(let d=0;d<s.length;d+=1)s[d].d(c);i&&i.d(c)}}}function Xp(r){let t,e,n,s;return t=new yt.Trigger({props:{$$slots:{default:[Hp]},$$scope:{ctx:r}}}),n=new yt.Content({props:{align:"start",side:"bottom",$$slots:{default:[Yp]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment),e=T(),x(n.$$.fragment)},m(o,a){S(t,o,a),y(o,e,a),S(n,o,a),s=!0},p(o,a){const l={};1024&a[1]&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};2048&a[0]|1024&a[1]&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(p(t.$$.fragment,o),p(n.$$.fragment,o),s=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),s=!1},d(o){o&&v(e),_(t,o),_(n,o)}}}function Qp(r){let t;return{c(){t=L("User Guidelines")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function tm(r){let t;return{c(){t=L("Learn more")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function em(r){let t,e,n,s,o,a,l,i,c,d,u,f,$,g,h,C,N,P,A,M,I,R,J,tt,et,E,z,D,rt,ut,G,F,Z,kt,Kt,Fe;n=new K({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Pp]},$$scope:{ctx:r}}}),i=new K({props:{size:1,weight:"regular",$$slots:{default:[Rp]},$$scope:{ctx:r}}});const xe=[Op,Lp],re=[];function Xn(W,it){return W[10].length===0?0:1}function _o(W){r[32](W)}function Co(W){r[33](W)}u=Xn(r),f=re[u]=xe[u](r),h=new xt({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Jp]},$$scope:{ctx:r}}}),h.$on("click",r[30]);let En={$$slots:{default:[Xp]},$$scope:{ctx:r}};return r[9]!==void 0&&(En.requestClose=r[9]),r[8]!==void 0&&(En.focusedIndex=r[8]),N=new yt.Root({props:En}),_t.push(()=>Ct(N,"requestClose",_o)),_t.push(()=>Ct(N,"focusedIndex",Co)),R=new K({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Qp]},$$scope:{ctx:r}}}),z=new K({props:{size:1,weight:"regular",$$slots:{default:[tm]},$$scope:{ctx:r}}}),rt=new So({props:{userGuidelines:r[0],userGuidelinesLengthLimit:r[1],updateUserGuideline:r[2]}}),G=new mp({props:{show:r[12],errorMessage:r[13]}}),G.$on("create",r[24]),G.$on("cancel",r[25]),Z=new Ip({props:{show:r[3],options:r[4],isLoading:r[5],errorMessage:r[6],successMessage:r[7]}}),Z.$on("select",r[22]),Z.$on("cancel",r[23]),{c(){t=k("div"),e=k("div"),x(n.$$.fragment),s=T(),o=k("div"),a=L(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),l=k("a"),x(i.$$.fragment),c=T(),d=k("div"),f.c(),$=T(),g=k("div"),x(h.$$.fragment),C=T(),x(N.$$.fragment),M=T(),I=k("div"),x(R.$$.fragment),J=T(),tt=k("div"),et=L(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),E=k("a"),x(z.$$.fragment),D=T(),x(rt.$$.fragment),ut=T(),x(G.$$.fragment),F=T(),x(Z.$$.fragment),w(l,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),w(l,"target","_blank"),w(d,"class","c-rules-list svelte-5krsve"),w(g,"class","c-rules-actions-container svelte-5krsve"),w(e,"class","c-rules-section svelte-5krsve"),w(E,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),w(E,"target","_blank"),w(I,"class","c-user-guidelines-section svelte-5krsve"),w(t,"class","c-rules-category svelte-5krsve")},m(W,it){y(W,t,it),b(t,e),S(n,e,null),b(e,s),b(e,o),b(o,a),b(o,l),S(i,l,null),b(e,c),b(e,d),re[u].m(d,null),b(e,$),b(e,g),S(h,g,null),b(g,C),S(N,g,null),b(t,M),b(t,I),S(R,I,null),b(I,J),b(I,tt),b(tt,et),b(tt,E),S(z,E,null),b(I,D),S(rt,I,null),y(W,ut,it),S(G,W,it),y(W,F,it),S(Z,W,it),kt=!0,Kt||(Fe=zt(window,"message",r[14].onMessageFromExtension),Kt=!0)},p(W,it){const Qn={};1024&it[1]&&(Qn.$$scope={dirty:it,ctx:W}),n.$set(Qn);const ts={};1024&it[1]&&(ts.$$scope={dirty:it,ctx:W}),i.$set(ts);let Nn=u;u=Xn(W),u===Nn?re[u].p(W,it):(U(),m(re[Nn],1,1,()=>{re[Nn]=null}),V(),f=re[u],f?f.p(W,it):(f=re[u]=xe[u](W),f.c()),p(f,1),f.m(d,null));const es={};1024&it[1]&&(es.$$scope={dirty:it,ctx:W}),h.$set(es);const un={};2048&it[0]|1024&it[1]&&(un.$$scope={dirty:it,ctx:W}),!P&&512&it[0]&&(P=!0,un.requestClose=W[9],bt(()=>P=!1)),!A&&256&it[0]&&(A=!0,un.focusedIndex=W[8],bt(()=>A=!1)),N.$set(un);const ns={};1024&it[1]&&(ns.$$scope={dirty:it,ctx:W}),R.$set(ns);const ss={};1024&it[1]&&(ss.$$scope={dirty:it,ctx:W}),z.$set(ss);const pn={};1&it[0]&&(pn.userGuidelines=W[0]),2&it[0]&&(pn.userGuidelinesLengthLimit=W[1]),4&it[0]&&(pn.updateUserGuideline=W[2]),rt.$set(pn);const In={};4096&it[0]&&(In.show=W[12]),8192&it[0]&&(In.errorMessage=W[13]),G.$set(In);const Te={};8&it[0]&&(Te.show=W[3]),16&it[0]&&(Te.options=W[4]),32&it[0]&&(Te.isLoading=W[5]),64&it[0]&&(Te.errorMessage=W[6]),128&it[0]&&(Te.successMessage=W[7]),Z.$set(Te)},i(W){kt||(p(n.$$.fragment,W),p(i.$$.fragment,W),p(f),p(h.$$.fragment,W),p(N.$$.fragment,W),p(R.$$.fragment,W),p(z.$$.fragment,W),p(rt.$$.fragment,W),p(G.$$.fragment,W),p(Z.$$.fragment,W),kt=!0)},o(W){m(n.$$.fragment,W),m(i.$$.fragment,W),m(f),m(h.$$.fragment,W),m(N.$$.fragment,W),m(R.$$.fragment,W),m(z.$$.fragment,W),m(rt.$$.fragment,W),m(G.$$.fragment,W),m(Z.$$.fragment,W),kt=!1},d(W){W&&(v(t),v(ut),v(F)),_(n),_(i),re[u].d(),_(h),_(N),_(R),_(z),_(rt),_(G,W),_(Z,W),Kt=!1,Fe()}}}function nm(r,t,e){let n,s,o,a,l=B,i=()=>(l(),l=ze(J,E=>e(11,s=E)),J);r.$$.on_destroy.push(()=>l());let{userGuidelines:c=""}=t,{userGuidelinesLengthLimit:d}=t,{updateUserGuideline:u=()=>!1}=t;const f=new eo(Mt),$=new ha(f),g=new sp(Mt,f,$);f.registerConsumer($);const h=$.getRulesFiles();Zt(r,h,E=>e(10,n=E));const C=g.getShowCreateRuleDialog();Zt(r,C,E=>e(12,o=E));const N=g.getCreateRuleError();Zt(r,N,E=>e(13,a=E));let P=!1,A=[],M=!1,I="",R="",J;i();let tt=()=>{};async function et(E){try{E.id==="select_file_or_directory"?await g.selectFileToImport():E.id==="auto_import"&&await async function(){try{e(6,I=""),e(7,R="");const z=await g.getAutoImportOptions();e(4,A=z.data.options),e(3,P=!0)}catch(z){console.error("Failed to get auto-import options:",z),e(6,I="Failed to detect existing rules in workspace.")}}()}catch(z){console.error("Failed to handle import select:",z)}tt&&tt()}return Kr(()=>{$.requestRules()}),r.$$set=E=>{"userGuidelines"in E&&e(0,c=E.userGuidelines),"userGuidelinesLengthLimit"in E&&e(1,d=E.userGuidelinesLengthLimit),"updateUserGuideline"in E&&e(2,u=E.updateUserGuideline)},[c,d,u,P,A,M,I,R,J,tt,n,s,o,a,f,$,g,h,C,N,[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],et,async function(E){const z=E.detail;try{e(5,M=!0),e(6,I="");const D=await g.processAutoImportSelection(z);let rt=`Successfully imported ${D.importedRulesCount} rule${D.importedRulesCount!==1?"s":""} from ${z.label}`;D.duplicatesCount>0&&(rt+=`, ${D.duplicatesCount} duplicate${D.duplicatesCount!==1?"s":""} skipped`),D.totalAttempted>D.importedRulesCount+D.duplicatesCount&&(rt+=`, ${D.totalAttempted-D.importedRulesCount-D.duplicatesCount} failed`),e(7,R=rt),setTimeout(()=>{e(3,P=!1),e(7,R="")},500)}catch(D){console.error("Failed to process auto-import selection:",D),e(6,I="Failed to import rules. Please try again.")}finally{e(5,M=!1)}},function(){e(3,P=!1),e(6,I=""),e(7,R="")},function(E){g.handleCreateRuleWithName(E.detail)},function(){g.hideCreateRuleDialog()},async(E,z,D)=>{await $.updateRuleContent({type:z,path:E.path,content:E.content,description:D})},(E,z)=>{z.stopPropagation(),g.openRule(E.path)},(E,z)=>{z.stopPropagation(),g.deleteRule(E.path)},E=>g.openRule(E.path),()=>g.createRule(),E=>et(E),function(E){tt=E,e(9,tt)},function(E){J=E,i(e(8,J))}]}class sm extends ct{constructor(t){super(),dt(this,t,nm,em,at,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2},null,[-1,-1])}}function rm(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=wt(s,n[o]);return{c(){t=Ot("svg"),e=new nn(!0),this.h()},l(o){t=sn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=rn(t);e=on(a,!0),a.forEach(v),this.h()},h(){e.a=null,Gt(t,s)},m(o,a){an(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',t)},p(o,[a]){Gt(t,s=se(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&o[0]]))},i:B,o:B,d(o){o&&v(t)}}}function om(r,t,e){return r.$$set=n=>{e(0,t=wt(wt({},t),Dt(n)))},[t=Dt(t)]}class am extends ct{constructor(t){super(),dt(this,t,om,rm,at,{})}}function im(r){let t;return{c(){t=L("Sign Out")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function lm(r){let t,e;return t=new am({props:{slot:"iconLeft"}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function cm(r){let t,e;return t=new xt({props:{loading:r[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[lm],default:[im]},$$scope:{ctx:r}}}),t.$on("click",r[1]),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,[s]){const o={};1&s&&(o.loading=n[0]),8&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function dm(r,t,e){let{onSignOut:n}=t,s=!1;return r.$$set=o=>{"onSignOut"in o&&e(2,n=o.onSignOut)},[s,function(){n(),e(0,s=!0)},n]}class um extends ct{constructor(t){super(),dt(this,t,dm,cm,at,{onSignOut:2})}}function pm(r){let t,e;return t=new Hu({props:{tools:r[6],onAuthenticate:r[22],onRevokeAccess:r[23],onToolApprovalConfigChange:r[26],servers:r[7],onMCPServerAdd:r[29],onMCPServerSave:r[30],onMCPServerDelete:r[31],onMCPServerToggleDisable:r[32],onMCPServerJSONImport:r[33],isMCPEnabled:r[8]&&r[2].mcpServerList,isMCPImportEnabled:r[8]&&r[2].mcpServerImport,supportedShells:r[9].supportedShells,selectedShell:r[9].selectedShell,startupScript:r[9].startupScript,onShellSelect:r[24],onStartupScriptChange:r[25],isTerminalEnabled:r[2].terminal,isSoundCategoryEnabled:!0,isAgentCategoryEnabled:r[8],isSwarmModeFeatureFlagEnabled:r[10],hasEverUsedRemoteAgent:r[11]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};64&s[0]&&(o.tools=n[6]),128&s[0]&&(o.servers=n[7]),260&s[0]&&(o.isMCPEnabled=n[8]&&n[2].mcpServerList),260&s[0]&&(o.isMCPImportEnabled=n[8]&&n[2].mcpServerImport),512&s[0]&&(o.supportedShells=n[9].supportedShells),512&s[0]&&(o.selectedShell=n[9].selectedShell),512&s[0]&&(o.startupScript=n[9].startupScript),4&s[0]&&(o.isTerminalEnabled=n[2].terminal),256&s[0]&&(o.isAgentCategoryEnabled=n[8]),1024&s[0]&&(o.isSwarmModeFeatureFlagEnabled=n[10]),2048&s[0]&&(o.hasEverUsedRemoteAgent=n[11]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function mm(r){let t,e;return t=new um({props:{onSignOut:r[27]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function fm(r){let t,e,n,s;const o=[vm,hm],a=[];function l(i,c){return i[2].rules?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=gt()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t===d?a[t].p(i,c):(U(),m(a[d],1,1,()=>{a[d]=null}),V(),e=a[t],e?e.p(i,c):(e=a[t]=o[t](i),e.c()),p(e,1),e.m(n.parentNode,n))},i(i){s||(p(e),s=!0)},o(i){m(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function $m(r){let t,e;return t=new ol({}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p:B,i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function gm(r){return{c:B,m:B,p:B,i:B,o:B,d:B}}function hm(r){let t,e;return t=new So({props:{userGuidelines:r[5],userGuidelinesLengthLimit:r[4],updateUserGuideline:r[21]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};32&s[0]&&(o.userGuidelines=n[5]),16&s[0]&&(o.userGuidelinesLengthLimit=n[4]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function vm(r){let t,e;return t=new sm({props:{userGuidelines:r[5],userGuidelinesLengthLimit:r[4],updateUserGuideline:r[21]}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};32&s[0]&&(o.userGuidelines=n[5]),16&s[0]&&(o.userGuidelinesLengthLimit=n[4]),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function ym(r){let t,e,n,s,o;const a=[gm,$m,fm,mm,pm],l=[];function i(c,d){var u,f,$;return 1024&d[1]&&(e=null),e==null&&(e=!vo(c[41])),e?0:((u=c[41])==null?void 0:u.id)==="context"?1:((f=c[41])==null?void 0:f.id)==="guidelines"?2:(($=c[41])==null?void 0:$.id)==="account"?3:4}return n=i(r,[-1,-1]),s=l[n]=a[n](r),{c(){t=k("span"),s.c(),w(t,"slot","content")},m(c,d){y(c,t,d),l[n].m(t,null),o=!0},p(c,d){let u=n;n=i(c,d),n===u?l[n].p(c,d):(U(),m(l[u],1,1,()=>{l[u]=null}),V(),s=l[n],s?s.p(c,d):(s=l[n]=a[n](c),s.c()),p(s,1),s.m(t,null))},i(c){o||(p(s),o=!0)},o(c){m(s),o=!1},d(c){c&&v(t),l[n].d()}}}function wm(r){let t,e;return t=new Il({props:{items:r[1],mode:"tree",class:"c-settings-navigation",selectedId:r[0],$$slots:{content:[ym,({item:n})=>({41:n}),({item:n})=>[0,n?1024:0]]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(n,s){S(t,n,s),e=!0},p(n,s){const o={};2&s[0]&&(o.items=n[1]),1&s[0]&&(o.selectedId=n[0]),4084&s[0]|3072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){_(t,n)}}}function xm(r){let t,e,n,s;return t=new $a.Root({props:{$$slots:{default:[wm]},$$scope:{ctx:r}}}),{c(){x(t.$$.fragment)},m(o,a){S(t,o,a),e=!0,n||(s=zt(window,"message",r[13].onMessageFromExtension),n=!0)},p(o,a){const l={};4087&a[0]|2048&a[1]&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)},i(o){e||(p(t.$$.fragment,o),e=!0)},o(o){m(t.$$.fragment,o),e=!1},d(o){_(t,o),n=!1,s()}}}function Sm(r,t,e){let n,s,o,a,l,i,c,d,u,f,$,g,h=B;r.$$.on_destroy.push(()=>h());const C=new Sa(Mt),N=new Ai(Mt),P=new Ti(Mt),A=new eo(Mt),M=new na,I=new fa(Mt,A,M),R=new qn(A);is(qn.key,R);const J=new gs(A);is(gs.key,J);const tt=C.getSettingsComponentSupported();Zt(r,tt,Z=>e(2,l=Z));const et=C.getEnableAgentMode();Zt(r,et,Z=>e(8,u=Z));const E=C.getEnableAgentSwarmMode();Zt(r,E,Z=>e(10,$=Z));const z=C.getHasEverUsedRemoteAgent();Zt(r,z,Z=>e(11,g=Z)),A.registerConsumer(C),A.registerConsumer(N),A.registerConsumer(P);const D=P.getTerminalSettings();let rt;Zt(r,D,Z=>e(9,f=Z));const ut={handleMessageFromExtension:Z=>!(!Z.data||Z.data.type!==ft.navigateToSettingsSection)&&(Z.data.data&&typeof Z.data.data=="string"&&e(0,rt=Z.data.data),!0)};A.registerConsumer(ut);const G=C.getDisplayableTools();Zt(r,G,Z=>e(6,c=Z));const F=C.getGuidelines();return Zt(r,F,Z=>e(28,i=Z)),Yr(()=>{C.dispose(),R.dispose(),J.dispose()}),C.notifyLoaded(),Mt.postMessage({type:ft.getOrientationStatus}),Mt.postMessage({type:ft.settingsPanelLoaded}),r.$$.update=()=>{var Z,kt,Kt;*********&r.$$.dirty[0]&&e(5,n=(Z=i.userGuidelines)==null?void 0:Z.contents),*********&r.$$.dirty[0]&&e(4,s=(kt=i.userGuidelines)==null?void 0:kt.lengthLimit),4&r.$$.dirty[0]&&e(1,a=[l.remoteTools?fn("Tools","",Rl,"section-tools"):void 0,l.userGuidelines&&!l.rules?fn("User Guidelines","Guidelines for Augment Chat to follow.",Yu,"guidelines"):void 0,l.rules?fn("Rules and User Guidelines","",tp,"guidelines"):void 0,l.workspaceContext?{name:"Context",description:"",icon:Ol,id:"context"}:void 0,fn("Account","Manage your Augment account settings.",Po,"account")].filter(Boolean)),3&r.$$.dirty[0]&&a.length>1&&!rt&&e(0,rt=(Kt=a[0])==null?void 0:Kt.id)},e(3,o=N.getServers()),h(),h=ze(o,Z=>e(7,d=Z)),[rt,a,l,o,s,n,c,d,u,f,$,g,N,A,tt,et,E,z,D,G,F,function(Z){const kt=Z.trim();return!(s&&kt.length>s)&&(C.updateLocalUserGuidelines(kt),Mt.postMessage({type:ft.updateUserGuidelines,data:Z}),!0)},function(Z){Mt.postMessage({type:ft.toolConfigStartOAuth,data:{authUrl:Z}}),C.startPolling()},async function(Z){await I.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${Z.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&Mt.postMessage({type:ft.toolConfigRevokeAccess,data:{toolId:Z.identifier}})},function(Z){P.updateSelectedShell(Z)},function(Z){P.updateStartupScript(Z)},function(Z,kt){Mt.postMessage({type:ft.toolApprovalConfigSetRequest,data:{toolId:Z,approvalConfig:kt}})},function(){Mt.postMessage({type:ft.signOut})},i,Z=>N.addServer(Z),Z=>N.updateServer(Z),Z=>N.deleteServer(Z),Z=>N.toggleDisabledServer(Z),Z=>N.importServersFromJSON(Z)]}class _m extends ct{constructor(t){super(),dt(this,t,Sm,xm,at,{},null,[-1,-1])}}(async function(){Mt&&Mt.initialize&&await Mt.initialize(),new _m({target:document.getElementById("app")})})();

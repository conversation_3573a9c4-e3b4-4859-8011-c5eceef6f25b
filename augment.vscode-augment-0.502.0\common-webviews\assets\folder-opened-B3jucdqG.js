import{S as H,i as L,s as f,D as M,c as e,a9 as g,e as h,n as c,h as d,a8 as v,Y as m,V as x,f as u,Z as b,b as a}from"./SpinnerAugment-CL9SZpf8.js";import{e as w}from"./IconButtonAugment-C4xMcLhX.js";function V(i,s,n){const l=i.slice();return l[3]=s[n],l}function Z(i){let s,n,l,t=i[3]+"";return{c(){s=M("span"),n=m(t),l=x(),e(s,"class","c-keyboard-shortcut-hint__icon svelte-1txw16l")},m(o,r){h(o,s,r),u(s,n),u(s,l)},p(o,r){2&r&&t!==(t=o[3]+"")&&b(n,t)},d(o){o&&d(s)}}}function k(i){let s,n,l=w(i[1]),t=[];for(let o=0;o<l.length;o+=1)t[o]=Z(V(i,l,o));return{c(){s=M("span");for(let o=0;o<t.length;o+=1)t[o].c();e(s,"class",n=g(`c-keyboard-shortcut-hint ${i[0]}`)+" svelte-1txw16l")},m(o,r){h(o,s,r);for(let C=0;C<t.length;C+=1)t[C]&&t[C].m(s,null)},p(o,[r]){if(2&r){let C;for(l=w(o[1]),C=0;C<l.length;C+=1){const p=V(o,l,C);t[C]?t[C].p(p,r):(t[C]=Z(p),t[C].c(),t[C].m(s,null))}for(;C<t.length;C+=1)t[C].d(1);t.length=l.length}1&r&&n!==(n=g(`c-keyboard-shortcut-hint ${o[0]}`)+" svelte-1txw16l")&&e(s,"class",n)},i:c,o:c,d(o){o&&d(s),v(t,o)}}}function y(i,s,n){let{class:l=""}=s,{keybinding:t}=s,{icons:o=(t==null?void 0:t.split("-"))??[]}=s;return i.$$set=r=>{"class"in r&&n(0,l=r.class),"keybinding"in r&&n(2,t=r.keybinding),"icons"in r&&n(1,o=r.icons)},[l,o,t]}class K extends H{constructor(s){super(),L(this,s,y,k,f,{class:0,keybinding:2,icons:1})}}function $(i){let s,n;return{c(){s=a("svg"),n=a("path"),e(n,"fill-rule","evenodd"),e(n,"clip-rule","evenodd"),e(n,"d","M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z"),e(n,"fill","currentColor"),e(s,"width","15"),e(s,"height","15"),e(s,"viewBox","0 0 15 15"),e(s,"fill","none"),e(s,"xmlns","http://www.w3.org/2000/svg")},m(l,t){h(l,s,t),u(s,n)},p:c,i:c,o:c,d(l){l&&d(s)}}}class S extends H{constructor(s){super(),L(this,s,null,$,f,{})}}function B(i){let s,n;return{c(){s=a("svg"),n=a("path"),e(n,"fill-rule","evenodd"),e(n,"clip-rule","evenodd"),e(n,"d","M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z"),e(n,"fill","currentColor"),e(s,"width","16"),e(s,"height","16"),e(s,"viewBox","0 0 16 16"),e(s,"fill","none"),e(s,"xmlns","http://www.w3.org/2000/svg")},m(l,t){h(l,s,t),u(s,n)},p:c,i:c,o:c,d(l){l&&d(s)}}}class Y extends H{constructor(s){super(),L(this,s,null,B,f,{})}}function _(i){let s,n;return{c(){s=a("svg"),n=a("path"),e(n,"d","M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z"),e(n,"fill","currentColor"),e(s,"width","16"),e(s,"height","16"),e(s,"viewBox","0 0 16 16"),e(s,"fill","none"),e(s,"xmlns","http://www.w3.org/2000/svg")},m(l,t){h(l,s,t),u(s,n)},p:c,i:c,o:c,d(l){l&&d(s)}}}class j extends H{constructor(s){super(),L(this,s,null,_,f,{})}}export{S as C,j as F,K,Y as a};

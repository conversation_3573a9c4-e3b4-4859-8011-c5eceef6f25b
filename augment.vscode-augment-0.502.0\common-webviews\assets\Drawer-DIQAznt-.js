import{S as U,i as Y,s as Z,J as E,D as _,V as X,c as z,P as k,a2 as g,e as O,f as W,Q as D,ac as ee,K as F,L as G,M as S,u as x,q as te,t as L,r as se,af as ae,h as P,T as de,aa as oe,E as V,F as C,ag as ne,ah as j,G as H,a5 as I}from"./SpinnerAugment-CL9SZpf8.js";import{I as re}from"./IconButtonAugment-C4xMcLhX.js";import{f as J}from"./index-D2Ut0gK2.js";import{E as le}from"./ellipsis-B3ZqaMmA.js";const ie=(s,{onResize:t,options:e})=>{const d=new ResizeObserver(t);return d.observe(s,e),{destroy(){d.unobserve(s),d.disconnect()}}},ce=s=>({}),K=s=>({}),ue=s=>({}),Q=s=>({});function A(s){let t,e,d,u;return e=new re({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:s}}}),e.$on("click",s[12]),{c(){t=_("div"),V(e.$$.fragment),z(t,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(r,$){O(r,t,$),C(e,t,null),u=!0},p(r,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:r}),e.$set(p)},i(r){u||(x(e.$$.fragment,r),r&&ne(()=>{u&&(d||(d=j(t,J,{y:0,x:0,duration:200},!0)),d.run(1))}),u=!0)},o(r){L(e.$$.fragment,r),r&&(d||(d=j(t,J,{y:0,x:0,duration:200},!1)),d.run(0)),u=!1},d(r){r&&P(t),H(e),r&&d&&d.end()}}}function me(s){let t,e;return t=new le({}),{c(){V(t.$$.fragment)},m(d,u){C(t,d,u),e=!0},i(d){e||(x(t.$$.fragment,d),e=!0)},o(d){L(t.$$.fragment,d),e=!1},d(d){H(t,d)}}}function he(s){let t,e,d,u,r,$,p,T,v,f,i,m,b;const M=s[20].left,h=E(M,s,s[24],Q),y=s[20].right,l=E(y,s,s[24],K);let n=s[0]&&s[3]&&A(s);return{c(){t=_("div"),e=_("div"),d=_("div"),h&&h.c(),u=X(),r=_("div"),$=X(),p=_("div"),l&&l.c(),T=X(),n&&n.c(),z(d,"class","c-drawer__left-content svelte-18f0m3o"),d.inert=s[7],k(d,"width","var(--augment-drawer-width)"),k(d,"min-width","var(--augment-drawer-width)"),k(d,"max-width","var(--augment-drawer-width)"),z(e,"class","c-drawer__left svelte-18f0m3o"),k(e,"--augment-drawer-width",s[8]+"px"),z(r,"aria-hidden","true"),z(r,"class","c-drawer__handle svelte-18f0m3o"),g(r,"is-locked",s[4]),z(p,"class","c-drawer__right svelte-18f0m3o"),z(t,"class",v="c-drawer "+s[2]+" svelte-18f0m3o"),g(t,"is-dragging",s[7]),g(t,"is-hidden",!s[8]),g(t,"is-column",s[4])},m(a,c){O(a,t,c),W(t,e),W(e,d),h&&h.m(d,null),s[21](e),W(t,u),W(t,r),W(t,$),W(t,p),l&&l.m(p,null),W(t,T),n&&n.m(t,null),s[22](t),i=!0,m||(b=[D(window,"mousemove",s[10]),D(window,"mouseup",s[11]),D(r,"mousedown",s[9]),D(r,"dblclick",s[12]),ee(f=ie.call(null,t,{onResize:s[23]}))],m=!0)},p(a,[c]){h&&h.p&&(!i||16777216&c)&&F(h,M,a,a[24],i?S(M,a[24],c,ue):G(a[24]),Q),(!i||128&c)&&(d.inert=a[7]),(!i||256&c)&&k(e,"--augment-drawer-width",a[8]+"px"),(!i||16&c)&&g(r,"is-locked",a[4]),l&&l.p&&(!i||16777216&c)&&F(l,y,a,a[24],i?S(y,a[24],c,ce):G(a[24]),K),a[0]&&a[3]?n?(n.p(a,c),9&c&&x(n,1)):(n=A(a),n.c(),x(n,1),n.m(t,null)):n&&(te(),L(n,1,1,()=>{n=null}),se()),(!i||4&c&&v!==(v="c-drawer "+a[2]+" svelte-18f0m3o"))&&z(t,"class",v),f&&ae(f.update)&&2&c&&f.update.call(null,{onResize:a[23]}),(!i||132&c)&&g(t,"is-dragging",a[7]),(!i||260&c)&&g(t,"is-hidden",!a[8]),(!i||20&c)&&g(t,"is-column",a[4])},i(a){i||(x(h,a),x(l,a),x(n),i=!0)},o(a){L(h,a),L(l,a),L(n),i=!1},d(a){a&&P(t),h&&h.d(a),s[21](null),l&&l.d(a),n&&n.d(),s[22](null),m=!1,de(b)}}}function fe(s,t,e){let d,u,r,$,{$$slots:p={},$$scope:T}=t,{initialWidth:v=300}=t,{expandedMinWidth:f=50}=t,{minimizedWidth:i=0}=t,{minimized:m=!1}=t,{class:b=""}=t,{showButton:M=!0}=t,{deadzone:h=0}=t,{columnLayoutThreshold:y=600}=t,{layoutMode:l}=t,n=!1,a=v,c=v,w=!1;function B(){if(u){if(l!==void 0)return e(4,w=l==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<y),w&&e(7,n=!1)}}return oe(B),s.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,b=o.class),"showButton"in o&&e(3,M=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,y=o.columnLayoutThreshold),"layoutMode"in o&&e(1,l=o.layoutMode),"$$scope"in o&&e(24,T=o.$$scope)},s.$$.update=()=>{3&s.$$.dirty&&(m?(e(1,l="row"),e(4,w=!1)):l!=="row"||m||(e(1,l=void 0),B())),18&s.$$.dirty&&l!==void 0&&(e(4,w=l==="column"),w&&e(7,n=!1)),589825&s.$$.dirty&&e(8,c=m?i:a)},[m,l,b,M,w,d,u,n,c,function(o){w||(e(7,n=!0),r=o.clientX,$=d.offsetWidth,o.preventDefault())},function(o){if(!n||!d||w)return;const N=o.clientX-r,q=u.clientWidth-200,R=$+N;R<f?R<f-h?e(0,m=!0):(e(19,a=f),e(0,m=!1)):R>q?(e(19,a=q),e(0,m=!1)):(e(19,a=R),e(0,m=!1))},function(){e(7,n=!1),e(19,a=Math.max(a,f))},function(){e(0,m=!m)},B,v,f,i,h,y,a,p,function(o){I[o?"unshift":"push"](()=>{d=o,e(5,d)})},function(o){I[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>l===void 0&&B(),T]}class ge extends U{constructor(t){super(),Y(this,t,fe,he,Z,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{ge as D,ie as r};

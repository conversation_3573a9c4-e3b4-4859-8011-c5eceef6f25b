var So=Object.defineProperty;var ki=r=>{throw TypeError(r)};var wo=(r,t,e)=>t in r?So(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var l=(r,t,e)=>wo(r,typeof t!="symbol"?t+"":t,e),pn=(r,t,e)=>t.has(r)||ki("Cannot "+e);var f=(r,t,e)=>(pn(r,t,"read from private field"),e?e.call(r):t.get(r)),B=(r,t,e)=>t.has(r)?ki("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e),R=(r,t,e,s)=>(pn(r,t,"write to private field"),s?s.call(r,e):t.set(r,e),e),A=(r,t,e)=>(pn(r,t,"access private method"),e);var Us=(r,t,e,s)=>({set _(n){R(r,t,n,e)},get _(){return f(r,t,s)}});import{B as Wt,C as Xn,A as ht,an as ut,aw as Fi,ax as En,S as Te,i as ke,s as Fe,af as fe,D as tt,V as J,E,c as X,a2 as Gt,P as Ye,e as L,f as et,F as T,ac as Cr,Q as Me,u as $,q as mt,t as C,r as _t,h as O,G as k,T as Zn,aj as We,Y as ct,a5 as ts,a6 as Mo,a7 as Io,N as re,n as Z,a4 as de,ak as xr,Z as Pt,a as Tn,b as Eo,H as To,w as ko,x as Fo,y as Ao,d as Ai,z as Oo,g as Lo,j as Oi,am as gn,at as os,X as as,ai as No,ae as Jn,a8 as Sr,al as Do,aa as wr,ay as Ro,U as Uo}from"./SpinnerAugment-CL9SZpf8.js";import"./design-system-init-SyQ8NwYv.js";import{S as qo,W as lt,h as _s,D as mn,I as jo,e as Bs,g as Po}from"./IconButtonAugment-C4xMcLhX.js";import{A as Ho}from"./async-messaging-CtwQrvzD.js";import{i as At,a as kn,b as es,c as at,d as ys,e as qs,S as ne,f as qe,g as sn,h as zo,j as Mr,C as Go,E as Wo,D as Vo,k as Bo,l as Ko,s as _n,m as Yn,n as Ks,o as Qs,p as ti,q as Xs,r as ei,t as Zs,A as Qo,u as Js,v as yn,w as Xo,x as Zo,y as vs,z as Ir,U as Er,B as Tr,F as kr,G as Jo}from"./chat-flags-model-LXed7yM_.js";import{f as wt,i as Li,F as Fr,j as Ni,k as vn,l as Di,C as Fn,s as Yo,m as ta,z as ea,B as Ri,D as $n,b as Ui,c as qi}from"./index-BAWb-tvr.js";import{C as sa}from"./types-CGlLNakm.js";import{C as gt,a as Mt,b as bn,I as js,P as he,E as na,M as Ar}from"./message-broker-SEbJxN6J.js";import{f as ia,i as Or}from"./file-paths-BPg3etNg.js";import{K as Ve,C as ra,F as oa,a as Lr}from"./folder-opened-B3jucdqG.js";import{B as Ie}from"./ButtonAugment-iwbEjzvh.js";import{a as aa,T as ca}from"./CardAugment-bwPj7Y67.js";import{A as ji,R as la,B as ua,P as ha,T as da,a as Nr,b as fa,C as pa,c as ga,G as ma,d as _a,M as An,e as Dr,K as ya,f as va}from"./Keybindings-DsK3Rz9L.js";import{F as Ot}from"./Filespan-C3pDA31_.js";import{M as cs}from"./MaterialIcon-Bh8QWD0w.js";import{a as $a,M as ba,b as Ca,C as xa}from"./index-iuo-Ho0S.js";import"./index-BskWw2a8.js";import"./BaseTextInput-BAWt2_LS.js";import"./CalloutAugment-C-hloZHD.js";import"./exclamation-triangle-5FhabZKw.js";import"./pen-to-square-DY0HDzb8.js";import"./augment-logo-CDzRYJ1a.js";var Pi=NaN,Sa="[object Symbol]",wa=/^\s+|\s+$/g,Ma=/^[-+]0x[0-9a-f]+$/i,Ia=/^0b[01]+$/i,Ea=/^0o[0-7]+$/i,Ta=parseInt,ka=typeof Wt=="object"&&Wt&&Wt.Object===Object&&Wt,Fa=typeof self=="object"&&self&&self.Object===Object&&self,Aa=ka||Fa||Function("return this")(),Oa=Object.prototype.toString,La=Math.max,Na=Math.min,Cn=function(){return Aa.Date.now()};function On(r){var t=typeof r;return!!r&&(t=="object"||t=="function")}function Hi(r){if(typeof r=="number")return r;if(function(s){return typeof s=="symbol"||function(n){return!!n&&typeof n=="object"}(s)&&Oa.call(s)==Sa}(r))return Pi;if(On(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=On(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=r.replace(wa,"");var e=Ia.test(r);return e||Ea.test(r)?Ta(r.slice(2),e?2:8):Ma.test(r)?Pi:+r}const Ln=Xn(function(r,t,e){var s,n,i,o,a,c,u=0,h=!1,d=!1,p=!0;if(typeof r!="function")throw new TypeError("Expected a function");function g(_){var w=s,F=n;return s=n=void 0,u=_,o=r.apply(F,w)}function b(_){var w=_-c;return c===void 0||w>=t||w<0||d&&_-u>=i}function S(){var _=Cn();if(b(_))return x(_);a=setTimeout(S,function(w){var F=t-(w-c);return d?Na(F,i-(w-u)):F}(_))}function x(_){return a=void 0,p&&s?g(_):(s=n=void 0,o)}function y(){var _=Cn(),w=b(_);if(s=arguments,n=this,c=_,w){if(a===void 0)return function(F){return u=F,a=setTimeout(S,t),h?g(F):o}(c);if(d)return a=setTimeout(S,t),g(c)}return a===void 0&&(a=setTimeout(S,t)),o}return t=Hi(t)||0,On(e)&&(h=!!e.leading,i=(d="maxWait"in e)?La(Hi(e.maxWait)||0,t):i,p="trailing"in e?!!e.trailing:p),y.cancel=function(){a!==void 0&&clearTimeout(a),u=0,s=c=n=a=void 0},y.flush=function(){return a===void 0?o:x(Cn())},y});var Nn={exports:{}};(function(r,t){var e="__lodash_hash_undefined__",s=1,n=2,i=9007199254740991,o="[object Arguments]",a="[object Array]",c="[object AsyncFunction]",u="[object Boolean]",h="[object Date]",d="[object Error]",p="[object Function]",g="[object GeneratorFunction]",b="[object Map]",S="[object Number]",x="[object Null]",y="[object Object]",_="[object Promise]",w="[object Proxy]",F="[object RegExp]",D="[object Set]",W="[object String]",H="[object Symbol]",V="[object Undefined]",P="[object WeakMap]",Ct="[object ArrayBuffer]",dt="[object DataView]",Et=/^\[object .+?Constructor\]$/,K=/^(?:0|[1-9]\d*)$/,z={};z["[object Float32Array]"]=z["[object Float64Array]"]=z["[object Int8Array]"]=z["[object Int16Array]"]=z["[object Int32Array]"]=z["[object Uint8Array]"]=z["[object Uint8ClampedArray]"]=z["[object Uint16Array]"]=z["[object Uint32Array]"]=!0,z[o]=z[a]=z[Ct]=z[u]=z[dt]=z[h]=z[d]=z[p]=z[b]=z[S]=z[y]=z[F]=z[D]=z[W]=z[P]=!1;var Ht=typeof Wt=="object"&&Wt&&Wt.Object===Object&&Wt,Ae=typeof self=="object"&&self&&self.Object===Object&&self,Lt=Ht||Ae||Function("return this")(),q=t&&!t.nodeType&&t,Oe=q&&r&&!r.nodeType&&r,Be=Oe&&Oe.exports===q,ls=Be&&Ht.process,Cs=function(){try{return ls&&ls.binding&&ls.binding("util")}catch{}}(),oi=Cs&&Cs.isTypedArray;function to(m,v){for(var M=-1,N=m==null?0:m.length;++M<N;)if(v(m[M],M,m))return!0;return!1}function eo(m){var v=-1,M=Array(m.size);return m.forEach(function(N,st){M[++v]=[st,N]}),M}function so(m){var v=-1,M=Array(m.size);return m.forEach(function(N){M[++v]=N}),M}var ai,ci,li,no=Array.prototype,io=Function.prototype,xs=Object.prototype,nn=Lt["__core-js_shared__"],ui=io.toString,ee=xs.hasOwnProperty,hi=(ai=/[^.]+$/.exec(nn&&nn.keys&&nn.keys.IE_PROTO||""))?"Symbol(src)_1."+ai:"",di=xs.toString,ro=RegExp("^"+ui.call(ee).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),fi=Be?Lt.Buffer:void 0,Ss=Lt.Symbol,pi=Lt.Uint8Array,gi=xs.propertyIsEnumerable,oo=no.splice,Le=Ss?Ss.toStringTag:void 0,mi=Object.getOwnPropertySymbols,ao=fi?fi.isBuffer:void 0,co=(ci=Object.keys,li=Object,function(m){return ci(li(m))}),rn=Ke(Lt,"DataView"),us=Ke(Lt,"Map"),on=Ke(Lt,"Promise"),an=Ke(Lt,"Set"),cn=Ke(Lt,"WeakMap"),hs=Ke(Object,"create"),lo=Re(rn),uo=Re(us),ho=Re(on),fo=Re(an),po=Re(cn),_i=Ss?Ss.prototype:void 0,ln=_i?_i.valueOf:void 0;function Ne(m){var v=-1,M=m==null?0:m.length;for(this.clear();++v<M;){var N=m[v];this.set(N[0],N[1])}}function oe(m){var v=-1,M=m==null?0:m.length;for(this.clear();++v<M;){var N=m[v];this.set(N[0],N[1])}}function De(m){var v=-1,M=m==null?0:m.length;for(this.clear();++v<M;){var N=m[v];this.set(N[0],N[1])}}function ws(m){var v=-1,M=m==null?0:m.length;for(this.__data__=new De;++v<M;)this.add(m[v])}function ge(m){var v=this.__data__=new oe(m);this.size=v.size}function go(m,v){var M=Es(m),N=!M&&$o(m),st=!M&&!N&&un(m),G=!M&&!N&&!st&&Mi(m),ot=M||N||st||G,ft=ot?function(vt,se){for(var ae=-1,Tt=Array(vt);++ae<vt;)Tt[ae]=se(ae);return Tt}(m.length,String):[],Vt=ft.length;for(var yt in m)!ee.call(m,yt)||ot&&(yt=="length"||st&&(yt=="offset"||yt=="parent")||G&&(yt=="buffer"||yt=="byteLength"||yt=="byteOffset")||vo(yt,Vt))||ft.push(yt);return ft}function Ms(m,v){for(var M=m.length;M--;)if(Ci(m[M][0],v))return M;return-1}function ds(m){return m==null?m===void 0?V:x:Le&&Le in Object(m)?function(v){var M=ee.call(v,Le),N=v[Le];try{v[Le]=void 0;var st=!0}catch{}var G=di.call(v);return st&&(M?v[Le]=N:delete v[Le]),G}(m):function(v){return di.call(v)}(m)}function yi(m){return fs(m)&&ds(m)==o}function vi(m,v,M,N,st){return m===v||(m==null||v==null||!fs(m)&&!fs(v)?m!=m&&v!=v:function(G,ot,ft,Vt,yt,vt){var se=Es(G),ae=Es(ot),Tt=se?a:me(G),ce=ae?a:me(ot),Qe=(Tt=Tt==o?y:Tt)==y,Ts=(ce=ce==o?y:ce)==y,Xe=Tt==ce;if(Xe&&un(G)){if(!un(ot))return!1;se=!0,Qe=!1}if(Xe&&!Qe)return vt||(vt=new ge),se||Mi(G)?$i(G,ot,ft,Vt,yt,vt):function(Y,Q,ks,_e,hn,zt,le){switch(ks){case dt:if(Y.byteLength!=Q.byteLength||Y.byteOffset!=Q.byteOffset)return!1;Y=Y.buffer,Q=Q.buffer;case Ct:return!(Y.byteLength!=Q.byteLength||!zt(new pi(Y),new pi(Q)));case u:case h:case S:return Ci(+Y,+Q);case d:return Y.name==Q.name&&Y.message==Q.message;case F:case W:return Y==Q+"";case b:var ye=eo;case D:var gs=_e&s;if(ye||(ye=so),Y.size!=Q.size&&!gs)return!1;var Fs=le.get(Y);if(Fs)return Fs==Q;_e|=n,le.set(Y,Q);var dn=$i(ye(Y),ye(Q),_e,hn,zt,le);return le.delete(Y),dn;case H:if(ln)return ln.call(Y)==ln.call(Q)}return!1}(G,ot,Tt,ft,Vt,yt,vt);if(!(ft&s)){var ps=Qe&&ee.call(G,"__wrapped__"),Ii=Ts&&ee.call(ot,"__wrapped__");if(ps||Ii){var Co=ps?G.value():G,xo=Ii?ot.value():ot;return vt||(vt=new ge),yt(Co,xo,ft,Vt,vt)}}return Xe?(vt||(vt=new ge),function(Y,Q,ks,_e,hn,zt){var le=ks&s,ye=bi(Y),gs=ye.length,Fs=bi(Q),dn=Fs.length;if(gs!=dn&&!le)return!1;for(var As=gs;As--;){var Ue=ye[As];if(!(le?Ue in Q:ee.call(Q,Ue)))return!1}var Ei=zt.get(Y);if(Ei&&zt.get(Q))return Ei==Q;var Os=!0;zt.set(Y,Q),zt.set(Q,Y);for(var fn=le;++As<gs;){var Ls=Y[Ue=ye[As]],Ns=Q[Ue];if(_e)var Ti=le?_e(Ns,Ls,Ue,Q,Y,zt):_e(Ls,Ns,Ue,Y,Q,zt);if(!(Ti===void 0?Ls===Ns||hn(Ls,Ns,ks,_e,zt):Ti)){Os=!1;break}fn||(fn=Ue=="constructor")}if(Os&&!fn){var Ds=Y.constructor,Rs=Q.constructor;Ds==Rs||!("constructor"in Y)||!("constructor"in Q)||typeof Ds=="function"&&Ds instanceof Ds&&typeof Rs=="function"&&Rs instanceof Rs||(Os=!1)}return zt.delete(Y),zt.delete(Q),Os}(G,ot,ft,Vt,yt,vt)):!1}(m,v,M,N,vi,st))}function mo(m){return!(!wi(m)||function(v){return!!hi&&hi in v}(m))&&(xi(m)?ro:Et).test(Re(m))}function _o(m){if(M=(v=m)&&v.constructor,N=typeof M=="function"&&M.prototype||xs,v!==N)return co(m);var v,M,N,st=[];for(var G in Object(m))ee.call(m,G)&&G!="constructor"&&st.push(G);return st}function $i(m,v,M,N,st,G){var ot=M&s,ft=m.length,Vt=v.length;if(ft!=Vt&&!(ot&&Vt>ft))return!1;var yt=G.get(m);if(yt&&G.get(v))return yt==v;var vt=-1,se=!0,ae=M&n?new ws:void 0;for(G.set(m,v),G.set(v,m);++vt<ft;){var Tt=m[vt],ce=v[vt];if(N)var Qe=ot?N(ce,Tt,vt,v,m,G):N(Tt,ce,vt,m,v,G);if(Qe!==void 0){if(Qe)continue;se=!1;break}if(ae){if(!to(v,function(Ts,Xe){if(ps=Xe,!ae.has(ps)&&(Tt===Ts||st(Tt,Ts,M,N,G)))return ae.push(Xe);var ps})){se=!1;break}}else if(Tt!==ce&&!st(Tt,ce,M,N,G)){se=!1;break}}return G.delete(m),G.delete(v),se}function bi(m){return function(v,M,N){var st=M(v);return Es(v)?st:function(G,ot){for(var ft=-1,Vt=ot.length,yt=G.length;++ft<Vt;)G[yt+ft]=ot[ft];return G}(st,N(v))}(m,bo,yo)}function Is(m,v){var M,N,st=m.__data__;return((N=typeof(M=v))=="string"||N=="number"||N=="symbol"||N=="boolean"?M!=="__proto__":M===null)?st[typeof v=="string"?"string":"hash"]:st.map}function Ke(m,v){var M=function(N,st){return N==null?void 0:N[st]}(m,v);return mo(M)?M:void 0}Ne.prototype.clear=function(){this.__data__=hs?hs(null):{},this.size=0},Ne.prototype.delete=function(m){var v=this.has(m)&&delete this.__data__[m];return this.size-=v?1:0,v},Ne.prototype.get=function(m){var v=this.__data__;if(hs){var M=v[m];return M===e?void 0:M}return ee.call(v,m)?v[m]:void 0},Ne.prototype.has=function(m){var v=this.__data__;return hs?v[m]!==void 0:ee.call(v,m)},Ne.prototype.set=function(m,v){var M=this.__data__;return this.size+=this.has(m)?0:1,M[m]=hs&&v===void 0?e:v,this},oe.prototype.clear=function(){this.__data__=[],this.size=0},oe.prototype.delete=function(m){var v=this.__data__,M=Ms(v,m);return!(M<0)&&(M==v.length-1?v.pop():oo.call(v,M,1),--this.size,!0)},oe.prototype.get=function(m){var v=this.__data__,M=Ms(v,m);return M<0?void 0:v[M][1]},oe.prototype.has=function(m){return Ms(this.__data__,m)>-1},oe.prototype.set=function(m,v){var M=this.__data__,N=Ms(M,m);return N<0?(++this.size,M.push([m,v])):M[N][1]=v,this},De.prototype.clear=function(){this.size=0,this.__data__={hash:new Ne,map:new(us||oe),string:new Ne}},De.prototype.delete=function(m){var v=Is(this,m).delete(m);return this.size-=v?1:0,v},De.prototype.get=function(m){return Is(this,m).get(m)},De.prototype.has=function(m){return Is(this,m).has(m)},De.prototype.set=function(m,v){var M=Is(this,m),N=M.size;return M.set(m,v),this.size+=M.size==N?0:1,this},ws.prototype.add=ws.prototype.push=function(m){return this.__data__.set(m,e),this},ws.prototype.has=function(m){return this.__data__.has(m)},ge.prototype.clear=function(){this.__data__=new oe,this.size=0},ge.prototype.delete=function(m){var v=this.__data__,M=v.delete(m);return this.size=v.size,M},ge.prototype.get=function(m){return this.__data__.get(m)},ge.prototype.has=function(m){return this.__data__.has(m)},ge.prototype.set=function(m,v){var M=this.__data__;if(M instanceof oe){var N=M.__data__;if(!us||N.length<199)return N.push([m,v]),this.size=++M.size,this;M=this.__data__=new De(N)}return M.set(m,v),this.size=M.size,this};var yo=mi?function(m){return m==null?[]:(m=Object(m),function(v,M){for(var N=-1,st=v==null?0:v.length,G=0,ot=[];++N<st;){var ft=v[N];M(ft,N,v)&&(ot[G++]=ft)}return ot}(mi(m),function(v){return gi.call(m,v)}))}:function(){return[]},me=ds;function vo(m,v){return!!(v=v??i)&&(typeof m=="number"||K.test(m))&&m>-1&&m%1==0&&m<v}function Re(m){if(m!=null){try{return ui.call(m)}catch{}try{return m+""}catch{}}return""}function Ci(m,v){return m===v||m!=m&&v!=v}(rn&&me(new rn(new ArrayBuffer(1)))!=dt||us&&me(new us)!=b||on&&me(on.resolve())!=_||an&&me(new an)!=D||cn&&me(new cn)!=P)&&(me=function(m){var v=ds(m),M=v==y?m.constructor:void 0,N=M?Re(M):"";if(N)switch(N){case lo:return dt;case uo:return b;case ho:return _;case fo:return D;case po:return P}return v});var $o=yi(function(){return arguments}())?yi:function(m){return fs(m)&&ee.call(m,"callee")&&!gi.call(m,"callee")},Es=Array.isArray,un=ao||function(){return!1};function xi(m){if(!wi(m))return!1;var v=ds(m);return v==p||v==g||v==c||v==w}function Si(m){return typeof m=="number"&&m>-1&&m%1==0&&m<=i}function wi(m){var v=typeof m;return m!=null&&(v=="object"||v=="function")}function fs(m){return m!=null&&typeof m=="object"}var Mi=oi?function(m){return function(v){return m(v)}}(oi):function(m){return fs(m)&&Si(m.length)&&!!z[ds(m)]};function bo(m){return(v=m)!=null&&Si(v.length)&&!xi(v)?go(m):_o(m);var v}r.exports=function(m,v){return vi(m,v)}})(Nn,Nn.exports);const Da=Xn(Nn.exports);function Rr(r){return function(t){return"unitOfCodeWork"in t&&!function(e){return e.children.length>0&&"childIds"in e}(t)}(r)?[r]:r.children.flatMap(Rr)}function Ra(r,t,e=1e3){let s=null,n=0;const i=ht(t),o=()=>{const a=(()=>{const c=Date.now();if(s!==null&&c-n<e)return s;const u=r();return s=u,n=c,u})();i.set(a)};return{subscribe:i.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Ur=(r=>(r[r.unset=0]="unset",r[r.positive=1]="positive",r[r.negative=2]="negative",r))(Ur||{});function Ua(r){var e;if(!r)return js.IMAGE_FORMAT_UNSPECIFIED;switch((e=r.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return js.JPEG;case"png":return js.PNG;default:return js.IMAGE_FORMAT_UNSPECIFIED}}function qa(r,t,e){var n,i;if(r.phase!==wt.cancelled&&r.phase!==wt.completed&&r.phase!==wt.error)return;let s;return(n=r.result)!=null&&n.contentNodes?(s=function(o,a){return o.map(c=>c.type===Li.ContentText?{type:bn.CONTENT_TEXT,text_content:c.text_content}:c.type===Li.ContentImage&&c.image_content&&a?{type:bn.CONTENT_IMAGE,image_content:{image_data:c.image_content.image_data,format:Ua(c.image_content.media_type)}}:{type:bn.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(r.result.contentNodes,e),{content:"",is_error:r.result.isError,request_id:r.result.requestId,tool_use_id:t,content_nodes:s}):((i=r.result)==null?void 0:i.text)!==void 0?{content:r.result.text,is_error:r.result.isError,request_id:r.result.requestId,tool_use_id:t}:void 0}function ja(r=[]){const t=function(e=[]){let s;for(const n of e){if(n.type===gt.TOOL_USE)return n;n.type===gt.TOOL_USE_START&&(s=n)}return s}(r);return t&&t.type===gt.TOOL_USE?r.filter(e=>e.type!==gt.TOOL_USE_START):r}const Nt="__NEW_AGENT__",zi=r=>At(r)&&!!r.request_message;function He(r){var t;return((t=r.extraData)==null?void 0:t.isAgentConversation)===!0}var bt=(r=>(r[r.active=0]="active",r[r.inactive=1]="inactive",r))(bt||{});const xn="temp-fe";class rt{constructor(t,e,s,n){l(this,"_state");l(this,"_subscribers",new Set);l(this,"_focusModel",new Fr);l(this,"_onSendExchangeListeners",[]);l(this,"_onNewConversationListeners",[]);l(this,"_onHistoryDeleteListeners",[]);l(this,"_onBeforeChangeConversationListeners",[]);l(this,"_totalCharactersCacheThrottleMs",1e3);l(this,"_totalCharactersStore");l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setConversation",(t,e=!0,s=!0)=>{const n=t.id!==this._state.id;n&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,a])=>{if(a.requestId&&a.toolUseId){const{requestId:c,toolUseId:u}=Ni(o);return c===a.requestId&&u===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",vn(a)),[o,a]}return[o,{...a,...Ni(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&n&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const i=rt.isEmpty(t);if(n&&i){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(At)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),n&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});l(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});l(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});l(this,"setName",t=>{this.update({name:t})});l(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});l(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});l(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[vn(t)]:t}})});l(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:wt.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[vn({requestId:t,toolUseId:e})]||{phase:wt.new});l(this,"getLastToolUseId",()=>{var s,n;const t=this.lastExchange;if(!t)return;const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===gt.TOOL_USE))??[]).at(-1);return e?(n=e.tool_use)==null?void 0:n.tool_use_id:void 0});l(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:wt.unknown};const e=function(n=[]){let i;for(const o of n){if(o.type===gt.TOOL_USE)return o;o.type===gt.TOOL_USE_START&&(i=o)}return i}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:wt.unknown}});l(this,"addExchange",(t,e)=>{const s=this._state.chatHistory;let n,i;n=e===void 0?[...s,t]:e===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,e),t,...s.slice(e)],At(t)&&(i=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Ur.unset,feedbackNote:""}}:void 0),this.update({chatHistory:n,...i?{feedbackStates:i}:{},lastUrl:void 0})});l(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});l(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});l(this,"updateExchangeById",(t,e,s=!1)=>{var a;const n=this.exchangeWithRequestId(e);if(n===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(n.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=ja([...n.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==n.stop_reason&&n.stop_reason&&t.stop_reason===sa.REASON_UNSPECIFIED&&(t.stop_reason=n.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...n.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const i=(a=(t.structured_output_nodes||[]).find(c=>c.type===gt.MAIN_TEXT_FINISHED))==null?void 0:a.content;i&&i!==t.response_text&&(t.response_text=i);let o=this._state.isShareable||es({...n,...t});return this.update({chatHistory:this.chatHistory.map(c=>c.request_id===e?{...c,...t}:c),isShareable:o}),!0});l(this,"clearMessagesFromHistory",t=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:e})});l(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});l(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),n=s.map(o=>o.request_id).filter(o=>o!==void 0),i=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:n,toolUseIds:i}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(a=>a(o))})});l(this,"clearMessageFromHistory",t=>{const e=this.chatHistory.find(n=>n.request_id===t),s=e?this._collectToolUseIdsFromMessages([e]):[];this.update({chatHistory:this.chatHistory.filter(n=>n.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});l(this,"_collectToolUseIdsFromMessages",t=>{var s;const e=[];for(const n of t)if(At(n)&&n.structured_output_nodes)for(const i of n.structured_output_nodes)i.type===gt.TOOL_USE&&((s=i.tool_use)!=null&&s.tool_use_id)&&e.push(i.tool_use.tool_use_id);return e});l(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});l(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});l(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});l(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:at.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));l(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});l(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);l(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});l(this,"historySummaryVersion",1);l(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:ne.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});l(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));l(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});l(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});l(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(n=>t.has(n.id)||n.recentFile||n.selection||n.sourceFolder),s=this._specialContextInputModel.recentItems.filter(n=>!(t.has(n.id)||n.recentFile||n.selection||n.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});l(this,"saveDraftExchange",(t,e)=>{var o,a,c;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),n=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!n)return;const i=(c=this.draftExchange)==null?void 0:c.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:i,status:at.draft}})});l(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});l(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!He(this)){const n=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&n&&this.updateConversationTitle()}}).finally(()=>{var s;He(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Di.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});l(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:at.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});l(this,"sendInstructionExchange",async(t,e)=>{let s=`${xn}-${crypto.randomUUID()}`;const n={status:at.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:ne.unseen,timestamp:new Date().toISOString()};this.addExchange(n);for await(const i of this._extensionClient.sendInstructionMessage(n,e)){if(!this.updateExchangeById(i,s,!0))return;s=i.request_id||s}});l(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});l(this,"checkAndGenerateAgentTitle",()=>{var e;if(!(!He(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>zi(s))).length===1&&!((e=this.extraData)!=null&&e.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});l(this,"sendSummaryExchange",()=>{const t={status:at.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:qe.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});l(this,"generateCommitMessage",async()=>{let t=`${xn}-${crypto.randomUUID()}`;const e={status:at.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:ne.unseen,chatItemType:qe.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});l(this,"sendExchange",async(t,e=!1,s)=>{var a;this.updateLastInteraction();let n=`${xn}-${crypto.randomUUID()}`,i=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&rt.isNew(this._state)){const c=crypto.randomUUID(),u=this._state.id;try{await this._extensionClient.migrateConversationId(u,c)}catch(h){console.error("Failed to migrate conversation checkpoints:",h)}this._state={...this._state,id:c},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(c),this._subscribers.forEach(h=>h(this))}t=Wi(t);let o={status:at.sent,request_id:n,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:i,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:ne.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(c=>c(o)),this._chatFlagModel.useHistorySummary&&await this.maybeAddHistorySummaryNode()&&this._clearStaleHistorySummaryNodes(),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},n,!1);for await(const c of this.sendUserMessage(n,o,e,s)){if(((a=this.exchangeWithRequestId(n))==null?void 0:a.status)!==at.sent||!this.updateExchangeById(c,n,!0))return;n=c.request_id||n}});l(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:at.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Fn.chatUseSuggestedQuestion)});l(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});l(this,"recoverExchange",async t=>{var n;if(!t.request_id||t.status!==at.sent)return;let e=t.request_id;const s=(n=t.structured_output_nodes)==null?void 0:n.filter(i=>i.type===gt.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const i of this.getChatStream(t)){if(!this.updateExchangeById(i,e,!0))return;e=i.request_id||e}});l(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{At(e)&&this._loadContextFromExchange(e)})});l(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});l(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{At(e)&&this._unloadContextFromExchange(e)})});l(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});l(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});l(this,"_jsonToStructuredRequest",t=>{const e=[],s=i=>{var a;const o=e.at(-1);if((o==null?void 0:o.type)===Mt.TEXT){const c=((a=o.text_node)==null?void 0:a.content)??"",u={...o,text_node:{content:c+i}};e[e.length-1]=u}else e.push({id:e.length,type:Mt.TEXT,text_node:{content:i}})},n=i=>{var o,a,c,u,h;if(i.type==="doc"||i.type==="paragraph")for(const d of i.content??[])n(d);else if(i.type==="hardBreak")s(`
`);else if(i.type==="text")s(i.text??"");else if(i.type==="file"){if(typeof((o=i.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(a=i.attrs)==null?void 0:a.src);if(i.attrs.isLoading)return;const d=(c=i.attrs)==null?void 0:c.title,p=ia(d);Or(d)?e.push({id:e.length,type:Mt.IMAGE_ID,image_id_node:{image_id:i.attrs.src,format:p}}):e.push({id:e.length,type:Mt.FILE_ID,file_id_node:{file_id:i.attrs.src,file_name:d}})}else if(i.type==="mention"){const d=(u=i.attrs)==null?void 0:u.data;d&&sn(d)?e.push({id:e.length,type:Mt.TEXT,text_node:{content:zo(this._chatFlagModel,d.personality.type)}}):d&&Mr(d)?e.push({id:e.length,type:Mt.TEXT,text_node:{content:ta.getTaskOrchestratorPrompt(d.task)}}):s(`@\`${(d==null?void 0:d.name)??(d==null?void 0:d.id)}\``)}else if(i.type==="askMode"){const d=(h=i.attrs)==null?void 0:h.prompt;d&&e.push({id:e.length,type:Mt.TEXT,text_node:{content:d}})}};return n(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=n,this._state={...rt.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return Ra(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,n)=>s+n,0))||0)<=4?he.PROTOTYPER:he.DEFAULT}catch(e){return console.error("Error determining persona type:",e),he.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:he.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(At);return e&&e.request_message?rt.toSentenceCase(e.request_message):He(t)?"New Agent":"New Chat"}static isNew(t){return t.id===Nt}static isEmpty(t){var n;const e=t.chatHistory.filter(i=>At(i)),s=t.chatHistory.filter(i=>kn(i));return e.length===0&&s.length===0&&!((n=t.draftExchange)!=null&&n.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?rt.lastMessageTimestamp(t):e==="lastInteractedAt"?rt.lastInteractedAt(t):rt.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(At))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!rt.isEmpty(t)||rt.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const n of this._onBeforeChangeConversationListeners){const i=n(t,s);i!==void 0&&(s=i)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return rt.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??he.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return rt.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return rt.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=n=>Array.isArray(n)?n.some(e):!!n&&(n.type==="file"||!(!n.content||!Array.isArray(n.content))&&n.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(At)??null}get lastExchange(){return this.chatHistory.findLast(At)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>At(t)&&t.status===at.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>es(t)||ys(t)||qs(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=t.findLastIndex(n=>qs(n)&&n.summaryVersion===this.historySummaryVersion);this._chatFlagModel.useHistorySummary&&e>0&&(console.info("Using history summary node found at index %d",e),t=t.slice(e));const s=[];for(const n of t)if(es(n))s.push(Gi(n));else if(ys(n)&&n.fromTimestamp!==void 0&&n.toTimestamp!==void 0){if(n.revertTarget){const i=Pa(n,1),o={request_message:"",response_text:"",request_id:n.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};s.push(o)}}else this._chatFlagModel.useHistorySummary&&qs(n)&&n.summaryVersion===this.historySummaryVersion&&s.push(Gi(n));return s}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===at.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,n="";const i=await this._addIdeStateNode(Wi({...t,request_id:e,status:at.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,i,!0))o.response_text&&(n+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:n,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}_resolveUnresolvedToolUses(t,e,s){var h,d,p;if(t.length===0)return[t,e];const n=t[t.length-1],i=((h=n.response_nodes)==null?void 0:h.filter(g=>g.type===gt.TOOL_USE))??[];if(i.length===0)return[t,e];const o=new Set;(d=e.structured_request_nodes)==null||d.forEach(g=>{var b;g.type===Mt.TOOL_RESULT&&((b=g.tool_result_node)!=null&&b.tool_use_id)&&o.add(g.tool_result_node.tool_use_id)});const a=i.filter(g=>{var S;const b=(S=g.tool_use)==null?void 0:S.tool_use_id;return b&&!o.has(b)});if(a.length===0)return[t,e];const c=a.map((g,b)=>{const S=g.tool_use.tool_use_id;return function(x,y,_,w){const F=qa(y,x,w);let D;if(F!==void 0)D=F;else{let W;switch(y.phase){case wt.runnable:W="Tool was cancelled before running.";break;case wt.new:W="Cancelled by user.";break;case wt.checkingSafety:W="Tool was cancelled during safety check.";break;case wt.running:W="Tool was cancelled while running.";break;case wt.cancelling:W="Tool cancellation was interrupted.";break;case wt.cancelled:W="Cancelled by user.";break;case wt.error:W="Tool execution failed.";break;case wt.completed:W="Tool completed but result was unavailable.";break;case wt.unknown:default:W="Cancelled by user.",y.phase!==wt.unknown&&console.error(`Unexpected tool state phase: ${y.phase}`)}D={tool_use_id:x,content:W,is_error:!0}}return{id:_,type:Mt.TOOL_RESULT,tool_result_node:D}}(S,this.getToolUseState(n.request_id,S),Dn(e.structured_request_nodes??[])+b+1,this._chatFlagModel.enableDebugFeatures)});if((p=e.structured_request_nodes)==null?void 0:p.some(g=>g.type===Mt.TOOL_RESULT))return[t,{...e,structured_request_nodes:[...e.structured_request_nodes??[],...c]}];{const g={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:c,structured_output_nodes:[],status:at.success,hidden:!0};return s||this.addExchangeBeforeLast(g),[t.concat(this._convertHistoryToExchanges([g])),e]}}async*sendUserMessage(t,e,s,n){var d;const i=this._specialContextInputModel.chatActiveContext;let o;if(e.chatHistory!==void 0)o=e.chatHistory;else{let p=this.successfulMessages;if(e.chatItemType===qe.summaryTitle){const g=p.findIndex(b=>b.chatItemType!==qe.agentOnboarding&&zi(b));g!==-1&&(p=p.slice(g))}o=this._convertHistoryToExchanges(p)}this._chatFlagModel.enableDebugFeatures&&([o,e]=this._resolveUnresolvedToolUses(o,e,s));let a=this.personaType;if(e.structured_request_nodes){const p=e.structured_request_nodes.find(g=>g.type===Mt.CHANGE_PERSONALITY);p&&p.change_personality_node&&(a=p.change_personality_node.personality_type)}const c={text:e.request_message,chatHistory:o,silent:s,modelId:e.model_id,context:i,userSpecifiedFiles:i.userSpecifiedFiles,externalSourceIds:(d=i.externalSources)==null?void 0:d.map(p=>p.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:a,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:n},u=this._createStreamStateHandlers(t,c,{flags:this._chatFlagModel}),h=this._extensionClient.startChatStreamWithRetry(t,c,{flags:this._chatFlagModel});for await(const p of h){let g=p;t=p.request_id||t;for(const b of u)g=b.handleChunk(g)??g;yield g}for(const p of u)yield*p.handleComplete();this.updateExchangeById({structured_request_nodes:e.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(n=>n.type!==Mt.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(n){console.error("Failed to add IDE state to exchange:",n)}return e?(s=[...s,{id:Dn(s)+1,type:Mt.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}async maybeAddHistorySummaryNode(){var _,w,F;const t=this._chatFlagModel.historySummaryPrompt;if(!t||t.trim()==="")return!1;const e=this._convertHistoryToExchanges(this.chatHistory),[s,n]=Yo(e,this._chatFlagModel.historySummaryLowerChars,this._chatFlagModel.historySummaryMaxChars);if(s.length===0)return!1;const i=JSON.stringify(e).length,o=JSON.stringify(s).length,a=JSON.stringify(n).length,c={totalHistoryCharCount:i,totalHistoryExchangeCount:e.length,headCharCount:o,headExchangeCount:s.length,headLastRequestId:((_=s.at(-1))==null?void 0:_.request_id)??"",tailCharCount:a,tailExchangeCount:n.length,tailLastRequestId:((w=n.at(-1))==null?void 0:w.request_id)??"",summaryCharCount:0,summarizationDurationMs:0};let u=((F=s.at(-1))==null?void 0:F.response_nodes)??[],h=u.filter(D=>D.type===gt.TOOL_USE);h.length>0&&(s.at(-1).response_nodes=u.filter(D=>D.type!==gt.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",s.length);const d=Date.now(),{responseText:p,requestId:g}=await this.sendSilentExchange({request_message:t,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:s}),b=Date.now();c.summaryCharCount=p.length,c.summarizationDurationMs=b-d,this._extensionClient.reportAgentRequestEvent({eventName:Di.chatHistorySummarization,conversationId:this.id,requestId:g??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length,eventData:{chatHistorySummarizationData:c}});const S={chatItemType:qe.historySummary,summaryVersion:this.historySummaryVersion,request_id:g,request_message:t,response_text:p,structured_output_nodes:[{id:h.map(D=>D.id).reduce((D,W)=>Math.max(D,W),-1)+1,type:gt.RAW_RESPONSE,content:p},...h],status:at.success,seen_state:ne.seen,timestamp:new Date().toISOString()},x=this.chatHistory.findLastIndex(D=>D.request_id===s.at(-1).request_id)+1;console.info("Adding a history summary node at index %d",x);const y=[...this._state.chatHistory];return y.splice(x,0,S),this.update({chatHistory:y}),!0}_clearStaleHistorySummaryNodes(){this.update({chatHistory:this.chatHistory.filter(t=>!qs(t)||t.summaryVersion===this.historySummaryVersion)})}}function Pa(r,t){const e=(ys(r),r.fromTimestamp),s=(ys(r),r.toTimestamp),n=ys(r)&&r.revertTarget!==void 0;return{id:t,type:Mt.CHECKPOINT_REF,checkpoint_ref_node:{request_id:r.request_id||"",from_timestamp:e,to_timestamp:s,source:n?na.CHECKPOINT_REVERT:void 0}}}function Gi(r){const t=(r.structured_output_nodes??[]).filter(e=>e.type===gt.RAW_RESPONSE||e.type===gt.TOOL_USE||e.type===gt.TOOL_USE_START).map(e=>e.type===gt.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:gt.TOOL_USE}:e);return{request_message:r.request_message,response_text:r.response_text??"",request_id:r.request_id||"",request_nodes:r.structured_request_nodes??[],response_nodes:t}}function Dn(r){return r.length>0?Math.max(...r.map(t=>t.id)):0}function Wi(r){var t;if(r.request_message.length>0&&!((t=r.structured_request_nodes)!=null&&t.some(e=>e.type===Mt.TEXT))){let e=r.structured_request_nodes??[];return e=[...e,{id:Dn(e)+1,type:Mt.TEXT,text_node:{content:r.request_message}}],{...r,structured_request_nodes:e}}return r}class Ha{constructor(t=!0,e=setTimeout){l(this,"_notify",new Set);l(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});l(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});l(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});l(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,n){if(!t&&!n)return()=>{};const i={timeout:t,notify:e,once:s,date:n};return this._notify.add(i),this._schedule(i),()=>{this._clearTimeout(i),this._notify.delete(i)}}}class za{constructor(t=0,e=0,s=new Ha,n=ht("busy"),i=ht(!1)){l(this,"unsubNotify");l(this,"unsubMessage");l(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});l(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=n,this.focusAfterIdle=i,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var zs=(r=>(r.send="send",r.addTask="addTask",r))(zs||{});class Ga{constructor(){l(this,"_mode",ht(zs.send));l(this,"_currentMode",zs.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(zs).includes(t)&&this._mode.set(t)}}const Ps=ht("idle");class si{constructor(t,e,s,n={}){l(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});l(this,"extensionClient");l(this,"_chatFlagsModel");l(this,"_currConversationModel");l(this,"_chatModeModel");l(this,"_sendModeModel");l(this,"_flagsLoaded",ht(!1));l(this,"subscribers",new Set);l(this,"idleMessageModel",new za);l(this,"isPanelCollapsed");l(this,"agentExecutionMode");l(this,"sortConversationsBy");l(this,"displayedAnnouncements");l(this,"onLoaded",async()=>{var s,n;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&(t.enableBackgroundAgents||t.enableNewThreadsList);this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Vo,bigSyncThreshold:t.bigSyncThreshold??Bo,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Ko,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??qo.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:(t.enableBackgroundAgents??!1)||(t.enableNewThreadsList??!1),enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryMaxChars:t.historySummaryMaxChars??0,historySummaryLowerChars:t.historySummaryLowerChars??0,historySummaryPrompt:t.historySummaryPrompt??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??""}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),e&&this.onDoUseNewDraftFunctionalityChanged(),this._flagsLoaded.set(!0),(n=(s=this.options).onLoaded)==null||n.call(s),this.notifySubscribers()});l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==_n&&this.currentConversationId!==_n||(delete this._state.conversations[_n],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===Nt||rt.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});l(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const n=s.chatHistory.some(i=>es(i));t[e]={...s,isShareable:n}}this._state.conversations=t});l(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[n,i]of Object.entries(e))i.isPinned&&s.add(n);this.setState(this._state),this.notifySubscribers()});l(this,"saveImmediate",()=>{this._setStateWithDehydration(this._state)});l(this,"setState",Ln(t=>{this._setStateWithDehydration(t)},1e3,{maxWait:15e3}));l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});l(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));l(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[Nt];if(this.currentConversationId&&this.currentConversationId!==Nt&&this._state.conversations[this.currentConversationId]&&rt.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:Nt};this._state.conversations[Nt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=Nt,this._currConversationModel.setConversation(e)}});l(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:qe.educateFeatures,request_id:crypto.randomUUID(),seen_state:ne.seen})});l(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});l(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let n;if(this.flags.doUseNewDraftFunctionality){t===void 0&&(t=Nt);const o=this._state.conversations[t];n=o?await this._hydrateConversation(o):rt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===Nt&&(n.id=Nt),s!=null&&s.newTaskUuid&&(n.rootTaskUuid=s.newTaskUuid)}else if(t===void 0)this.deleteInvalidConversations(He(this._currConversationModel)?"agent":"chat"),n=rt.create({personaType:await this._currConversationModel.decidePersonaType()});else{const o=this._state.conversations[t];n=o?await this._hydrateConversation(o):rt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid})}const i=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(n,!i,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});l(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[Nt]});l(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});l(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});l(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;Ps.set("copying");const s=e==null?void 0:e.chatHistory,n=s.reduce((a,c)=>(es(c)&&a.push({request_id:c.request_id||"",request_message:c.request_message,response_text:c.response_text||""}),a),[]);if(n.length===0)throw new Error("No chat history to share");const i=rt.getDisplayName(e),o=await this.extensionClient.saveChat(t,n,i);if(o.data){let a=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});l(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void Ps.set("idle");navigator.clipboard.writeText(e),Ps.set("copied")}catch{Ps.set("failed")}});l(this,"deleteConversations",async(t,e=void 0,s=[],n)=>{const i=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${i>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);this.deleteConversationIds(o)}if(s.length>0&&n)for(const o of s)try{await n.deleteAgent(o,!0)}catch(a){console.error(`Failed to delete remote agent ${o}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});l(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});l(this,"deleteConversationIds",async t=>{var n,i,o;const e=[],s=[];for(const a of t){const c=((n=this._state.conversations[a])==null?void 0:n.requestIds)??[];e.push(...c);const u=((i=this._state.conversations[a])==null?void 0:i.toolUseStates)??{};for(const d of Object.keys(u)){const{toolUseId:p}=u[d];p&&s.push(p)}const h=this._state.conversations[a];if(h){for(const d of h.chatHistory)if(At(d)&&d.structured_output_nodes)for(const p of d.structured_output_nodes)p.type===gt.TOOL_USE&&((o=p.tool_use)!=null&&o.tool_use_id)&&s.push(p.tool_use.tool_use_id)}}for(const a of Object.values(this._state.conversations))if(t.has(a.id)){for(const u of a.chatHistory)At(u)&&this.deleteImagesInExchange(u);const c=a.draftExchange;c&&this.deleteImagesInExchange(c)}for(const a of t){try{await this.extensionClient.deleteConversationExchanges(a)}catch(c){console.error(`Failed to delete exchanges for conversation ${a}:`,c)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(a)}catch(c){console.error(`Failed to delete tool use states for conversation ${a}:`,c)}}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([a])=>!t.has(a)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});l(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});l(this,"findImagesInJson",t=>{const e=[],s=n=>{var i,o;if(n.type==="file"&&((i=n.attrs)!=null&&i.src)){const a=(o=n.attrs)==null?void 0:o.src;Or(a)&&e.push(n.attrs.src)}else if((n.type==="doc"||n.type==="paragraph")&&n.content)for(const a of n.content)s(a)};return s(t),e});l(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===Mt.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));l(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});l(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});l(this,"smartPaste",(t,e,s,n)=>{const i=this._currConversationModel.historyTo(t,!0).filter(o=>es(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:i,targetFile:s??void 0,options:n})});l(this,"saveImage",async t=>await this.extensionClient.saveImage(t));l(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));l(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));l(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=n,this._chatFlagsModel=new Go(n.initialFlags),this.extensionClient=new Wo(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new rt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Ga,this.initialize(n.initialConversation);const i=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ht(i),this.agentExecutionMode=ht(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=ht(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ht(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get flagsLoaded(){return this._flagsLoaded}async _setStateWithDehydration(t){const e={};if(t.conversations)for(const[s,n]of Object.entries(t.conversations))try{e[s]=await this._dehydrateConversation(n)}catch(i){console.warn(`Failed to dehydrate conversation ${s}:`,i),e[s]=n}this._host.setState({...t,conversations:e,isPanelCollapsed:ut(this.isPanelCollapsed),agentExecutionMode:ut(this.agentExecutionMode),sortConversationsBy:ut(this.sortConversationsBy),displayedAnnouncements:ut(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const n=t||this._state.sortConversationsBy||"lastMessageTimestamp";let i=Object.values(this._state.conversations);return s&&(i=i.filter(s)),i.sort((o,a)=>{const c=rt.getTime(o,n).getTime(),u=rt.getTime(a,n).getTime();return e==="asc"?c-u:u-c})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===Nt)return!1;const n=!rt.isValid(this.conversations[s]),i=He(this.conversations[s]);return n&&(t==="agent"&&i||t==="chat"&&!i||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===lt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}async _dehydrateConversation(t){let e=t;return this.flags.enableExchangeStorage&&(e=await this._dehydrateExchanges(e)),this.flags.enableToolUseStateStorage&&(e=await this._dehydrateToolUseStates(e)),e}async _dehydrateExchanges(t){const e=[],s=[];for(const n of t.chatHistory)if(At(n)){const i=n,o=i.request_id||crypto.randomUUID(),a={request_message:i.request_message,response_text:i.response_text||"",request_id:o,request_nodes:i.structured_request_nodes,response_nodes:i.structured_output_nodes,rich_text_json_repr:i.rich_text_json_repr,mentioned_items:i.mentioned_items,uuid:o,conversationId:t.id,status:i.status===at.success?"success":i.status===at.failed?"failed":"sent",timestamp:i.timestamp||new Date().toISOString(),seen_state:i.seen_state===ne.seen?"seen":"unseen"};e.push(a);const c={chatItemType:qe.exchangePointer,exchangeUuid:o,timestamp:i.timestamp,request_message:i.request_message,status:i.status,hasResponse:!!i.response_text,isStreaming:i.status===at.sent,seen_state:i.seen_state};s.push(c)}else s.push(n);if(e.length>0)try{await this.extensionClient.saveExchanges(t.id,e)}catch{return t}return{...t,chatHistory:s}}async _dehydrateToolUseStates(t){if(!this.flags.enableToolUseStateStorage||!t.toolUseStates||Object.keys(t.toolUseStates).length===0)return t;try{return await this.extensionClient.saveToolUseStates(t.id,t.toolUseStates),{...t,toolUseStates:{}}}catch(e){return console.warn(`Failed to store tool use states for conversation ${t.id}:`,e),t}}async _hydrateConversation(t){let e=t;return this.flags.enableExchangeStorage&&(e=await this._hydrateExchanges(e)),this.flags.enableToolUseStateStorage&&(e=await this._hydrateToolUseStates(e)),e}async _hydrateExchanges(t){const e=[];for(const i of t.chatHistory)kn(i)&&e.push(i.exchangeUuid);let s=new Map;if(e.length>0)try{s=new Map((await this.extensionClient.loadExchanges(t.id,e)).map(i=>[i.uuid,i]))}catch(i){console.warn(`Failed to load exchanges for conversation ${t.id}:`,i)}const n=t.chatHistory.map(i=>{if(kn(i)){const o=s.get(i.exchangeUuid);return o?{chatItemType:void 0,timestamp:o.timestamp,request_message:o.request_message,response_text:o.response_text||"",status:o.status==="success"?at.success:o.status==="failed"?at.failed:at.sent,seen_state:o.seen_state==="seen"?ne.seen:ne.unseen,request_id:o.request_id,structured_request_nodes:o.request_nodes,structured_output_nodes:o.response_nodes,rich_text_json_repr:o.rich_text_json_repr,mentioned_items:o.mentioned_items}:{chatItemType:void 0,timestamp:i.timestamp,request_message:i.request_message||"",response_text:"",status:i.status||at.sent,seen_state:i.seen_state||ne.unseen,request_id:i.request_id}}return i});return{...t,chatHistory:n}}async _hydrateToolUseStates(t){if(!this.flags.enableToolUseStateStorage)return t;try{const e=await this.extensionClient.loadConversationToolUseStates(t.id);return{...t,toolUseStates:e}}catch(e){return console.warn(`Failed to load tool use states for conversation ${t.id}:`,e),t}}}l(si,"NEW_AGENT_KEY",Nt);function Vi(r,t){let e,s,n=t;const i=()=>n.editor.getModifiedEditor(),o=()=>{const{afterLineNumber:a}=n,c=i();if(a===void 0)return void c.changeViewZones(h=>{e&&c&&s&&h.removeZone(s)});const u={...n,afterLineNumber:a,domNode:r,suppressMouseDown:!0};c==null||c.changeViewZones(h=>{e&&s&&h.removeZone(s),s=h.addZone(u),e=u})};return o(),{update:a=>{n=a,o()},destroy:()=>{const a=i();a.changeViewZones(c=>{if(e&&a&&s)try{c.removeZone(s)}catch(u){if(u instanceof Error){if(u.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${u}`)}})}}}var Ee=(r=>(r.edit="edit",r.instruction="instruction",r))(Ee||{}),Rn=(r=>(r[r.instructionDrawer=0]="instructionDrawer",r[r.chunkActionPanel=1]="chunkActionPanel",r))(Rn||{});const Ze=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Bi=new Set,Un=typeof process=="object"&&process?process:{},qr=(r,t,e,s)=>{typeof Un.emitWarning=="function"?Un.emitWarning(r,t,e,s):console.error(`[${e}] ${t}: ${r}`)};let Ys=globalThis.AbortController,Ki=globalThis.AbortSignal;var vr;if(Ys===void 0){Ki=class{constructor(){l(this,"onabort");l(this,"_onabort",[]);l(this,"reason");l(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},Ys=class{constructor(){l(this,"signal",new Ki);t()}abort(e){var s,n;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const i of this.signal._onabort)i(e);(n=(s=this.signal).onabort)==null||n.call(s,e)}}};let r=((vr=Un.env)==null?void 0:vr.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{r&&(r=!1,qr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ve=r=>r&&r===Math.floor(r)&&r>0&&isFinite(r),jr=r=>ve(r)?r<=Math.pow(2,8)?Uint8Array:r<=Math.pow(2,16)?Uint16Array:r<=Math.pow(2,32)?Uint32Array:r<=Number.MAX_SAFE_INTEGER?Gs:null:null;class Gs extends Array{constructor(t){super(t),this.fill(0)}}var ss;const Pe=class Pe{constructor(t,e){l(this,"heap");l(this,"length");if(!f(Pe,ss))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=jr(t);if(!e)return[];R(Pe,ss,!0);const s=new Pe(t,e);return R(Pe,ss,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};ss=new WeakMap,B(Pe,ss,!1);let qn=Pe;var $r,br,Bt,Dt,Kt,Qt,ns,is,$t,Xt,pt,it,j,kt,Rt,It,xt,Zt,St,Jt,Yt,Ut,te,we,Ft,I,Pn,ze,ue,$s,qt,Pr,Ge,rs,bs,$e,be,Hn,Ws,Vs,nt,zn,ms,Ce,Gn;const ri=class ri{constructor(t){B(this,I);B(this,Bt);B(this,Dt);B(this,Kt);B(this,Qt);B(this,ns);B(this,is);l(this,"ttl");l(this,"ttlResolution");l(this,"ttlAutopurge");l(this,"updateAgeOnGet");l(this,"updateAgeOnHas");l(this,"allowStale");l(this,"noDisposeOnSet");l(this,"noUpdateTTL");l(this,"maxEntrySize");l(this,"sizeCalculation");l(this,"noDeleteOnFetchRejection");l(this,"noDeleteOnStaleGet");l(this,"allowStaleOnFetchAbort");l(this,"allowStaleOnFetchRejection");l(this,"ignoreFetchAbort");B(this,$t);B(this,Xt);B(this,pt);B(this,it);B(this,j);B(this,kt);B(this,Rt);B(this,It);B(this,xt);B(this,Zt);B(this,St);B(this,Jt);B(this,Yt);B(this,Ut);B(this,te);B(this,we);B(this,Ft);B(this,ze,()=>{});B(this,ue,()=>{});B(this,$s,()=>{});B(this,qt,()=>!1);B(this,Ge,t=>{});B(this,rs,(t,e,s)=>{});B(this,bs,(t,e,s,n)=>{if(s||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});l(this,$r,"LRUCache");const{max:e=0,ttl:s,ttlResolution:n=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:c,dispose:u,disposeAfter:h,noDisposeOnSet:d,noUpdateTTL:p,maxSize:g=0,maxEntrySize:b=0,sizeCalculation:S,fetchMethod:x,memoMethod:y,noDeleteOnFetchRejection:_,noDeleteOnStaleGet:w,allowStaleOnFetchRejection:F,allowStaleOnFetchAbort:D,ignoreFetchAbort:W}=t;if(e!==0&&!ve(e))throw new TypeError("max option must be a nonnegative integer");const H=e?jr(e):Array;if(!H)throw new Error("invalid max value: "+e);if(R(this,Bt,e),R(this,Dt,g),this.maxEntrySize=b||f(this,Dt),this.sizeCalculation=S,this.sizeCalculation){if(!f(this,Dt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(y!==void 0&&typeof y!="function")throw new TypeError("memoMethod must be a function if defined");if(R(this,is,y),x!==void 0&&typeof x!="function")throw new TypeError("fetchMethod must be a function if specified");if(R(this,ns,x),R(this,we,!!x),R(this,pt,new Map),R(this,it,new Array(e).fill(void 0)),R(this,j,new Array(e).fill(void 0)),R(this,kt,new H(e)),R(this,Rt,new H(e)),R(this,It,0),R(this,xt,0),R(this,Zt,qn.create(e)),R(this,$t,0),R(this,Xt,0),typeof u=="function"&&R(this,Kt,u),typeof h=="function"?(R(this,Qt,h),R(this,St,[])):(R(this,Qt,void 0),R(this,St,void 0)),R(this,te,!!f(this,Kt)),R(this,Ft,!!f(this,Qt)),this.noDisposeOnSet=!!d,this.noUpdateTTL=!!p,this.noDeleteOnFetchRejection=!!_,this.allowStaleOnFetchRejection=!!F,this.allowStaleOnFetchAbort=!!D,this.ignoreFetchAbort=!!W,this.maxEntrySize!==0){if(f(this,Dt)!==0&&!ve(f(this,Dt)))throw new TypeError("maxSize must be a positive integer if specified");if(!ve(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");A(this,I,Pr).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!w,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=ve(n)||n===0?n:1,this.ttlAutopurge=!!i,this.ttl=s||0,this.ttl){if(!ve(this.ttl))throw new TypeError("ttl must be a positive integer if specified");A(this,I,Pn).call(this)}if(f(this,Bt)===0&&this.ttl===0&&f(this,Dt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!f(this,Bt)&&!f(this,Dt)){const V="LRU_CACHE_UNBOUNDED";(P=>!Bi.has(P))(V)&&(Bi.add(V),qr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",V,ri))}}static unsafeExposeInternals(t){return{starts:f(t,Yt),ttls:f(t,Ut),sizes:f(t,Jt),keyMap:f(t,pt),keyList:f(t,it),valList:f(t,j),next:f(t,kt),prev:f(t,Rt),get head(){return f(t,It)},get tail(){return f(t,xt)},free:f(t,Zt),isBackgroundFetch:e=>{var s;return A(s=t,I,nt).call(s,e)},backgroundFetch:(e,s,n,i)=>{var o;return A(o=t,I,Vs).call(o,e,s,n,i)},moveToTail:e=>{var s;return A(s=t,I,ms).call(s,e)},indexes:e=>{var s;return A(s=t,I,$e).call(s,e)},rindexes:e=>{var s;return A(s=t,I,be).call(s,e)},isStale:e=>{var s;return f(s=t,qt).call(s,e)}}}get max(){return f(this,Bt)}get maxSize(){return f(this,Dt)}get calculatedSize(){return f(this,Xt)}get size(){return f(this,$t)}get fetchMethod(){return f(this,ns)}get memoMethod(){return f(this,is)}get dispose(){return f(this,Kt)}get disposeAfter(){return f(this,Qt)}getRemainingTTL(t){return f(this,pt).has(t)?1/0:0}*entries(){for(const t of A(this,I,$e).call(this))f(this,j)[t]===void 0||f(this,it)[t]===void 0||A(this,I,nt).call(this,f(this,j)[t])||(yield[f(this,it)[t],f(this,j)[t]])}*rentries(){for(const t of A(this,I,be).call(this))f(this,j)[t]===void 0||f(this,it)[t]===void 0||A(this,I,nt).call(this,f(this,j)[t])||(yield[f(this,it)[t],f(this,j)[t]])}*keys(){for(const t of A(this,I,$e).call(this)){const e=f(this,it)[t];e===void 0||A(this,I,nt).call(this,f(this,j)[t])||(yield e)}}*rkeys(){for(const t of A(this,I,be).call(this)){const e=f(this,it)[t];e===void 0||A(this,I,nt).call(this,f(this,j)[t])||(yield e)}}*values(){for(const t of A(this,I,$e).call(this))f(this,j)[t]===void 0||A(this,I,nt).call(this,f(this,j)[t])||(yield f(this,j)[t])}*rvalues(){for(const t of A(this,I,be).call(this))f(this,j)[t]===void 0||A(this,I,nt).call(this,f(this,j)[t])||(yield f(this,j)[t])}[(br=Symbol.iterator,$r=Symbol.toStringTag,br)](){return this.entries()}find(t,e={}){for(const s of A(this,I,$e).call(this)){const n=f(this,j)[s],i=A(this,I,nt).call(this,n)?n.__staleWhileFetching:n;if(i!==void 0&&t(i,f(this,it)[s],this))return this.get(f(this,it)[s],e)}}forEach(t,e=this){for(const s of A(this,I,$e).call(this)){const n=f(this,j)[s],i=A(this,I,nt).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,f(this,it)[s],this)}}rforEach(t,e=this){for(const s of A(this,I,be).call(this)){const n=f(this,j)[s],i=A(this,I,nt).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,f(this,it)[s],this)}}purgeStale(){let t=!1;for(const e of A(this,I,be).call(this,{allowStale:!0}))f(this,qt).call(this,e)&&(A(this,I,Ce).call(this,f(this,it)[e],"expire"),t=!0);return t}info(t){const e=f(this,pt).get(t);if(e===void 0)return;const s=f(this,j)[e],n=A(this,I,nt).call(this,s)?s.__staleWhileFetching:s;if(n===void 0)return;const i={value:n};if(f(this,Ut)&&f(this,Yt)){const o=f(this,Ut)[e],a=f(this,Yt)[e];if(o&&a){const c=o-(Ze.now()-a);i.ttl=c,i.start=Date.now()}}return f(this,Jt)&&(i.size=f(this,Jt)[e]),i}dump(){const t=[];for(const e of A(this,I,$e).call(this,{allowStale:!0})){const s=f(this,it)[e],n=f(this,j)[e],i=A(this,I,nt).call(this,n)?n.__staleWhileFetching:n;if(i===void 0||s===void 0)continue;const o={value:i};if(f(this,Ut)&&f(this,Yt)){o.ttl=f(this,Ut)[e];const a=Ze.now()-f(this,Yt)[e];o.start=Math.floor(Date.now()-a)}f(this,Jt)&&(o.size=f(this,Jt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const n=Date.now()-s.start;s.start=Ze.now()-n}this.set(e,s.value,s)}}set(t,e,s={}){var p,g,b,S,x;if(e===void 0)return this.delete(t),this;const{ttl:n=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:c}=s;let{noUpdateTTL:u=this.noUpdateTTL}=s;const h=f(this,bs).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&h>this.maxEntrySize)return c&&(c.set="miss",c.maxEntrySizeExceeded=!0),A(this,I,Ce).call(this,t,"set"),this;let d=f(this,$t)===0?void 0:f(this,pt).get(t);if(d===void 0)d=f(this,$t)===0?f(this,xt):f(this,Zt).length!==0?f(this,Zt).pop():f(this,$t)===f(this,Bt)?A(this,I,Ws).call(this,!1):f(this,$t),f(this,it)[d]=t,f(this,j)[d]=e,f(this,pt).set(t,d),f(this,kt)[f(this,xt)]=d,f(this,Rt)[d]=f(this,xt),R(this,xt,d),Us(this,$t)._++,f(this,rs).call(this,d,h,c),c&&(c.set="add"),u=!1;else{A(this,I,ms).call(this,d);const y=f(this,j)[d];if(e!==y){if(f(this,we)&&A(this,I,nt).call(this,y)){y.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:_}=y;_===void 0||o||(f(this,te)&&((p=f(this,Kt))==null||p.call(this,_,t,"set")),f(this,Ft)&&((g=f(this,St))==null||g.push([_,t,"set"])))}else o||(f(this,te)&&((b=f(this,Kt))==null||b.call(this,y,t,"set")),f(this,Ft)&&((S=f(this,St))==null||S.push([y,t,"set"])));if(f(this,Ge).call(this,d),f(this,rs).call(this,d,h,c),f(this,j)[d]=e,c){c.set="replace";const _=y&&A(this,I,nt).call(this,y)?y.__staleWhileFetching:y;_!==void 0&&(c.oldValue=_)}}else c&&(c.set="update")}if(n===0||f(this,Ut)||A(this,I,Pn).call(this),f(this,Ut)&&(u||f(this,$s).call(this,d,n,i),c&&f(this,ue).call(this,c,d)),!o&&f(this,Ft)&&f(this,St)){const y=f(this,St);let _;for(;_=y==null?void 0:y.shift();)(x=f(this,Qt))==null||x.call(this,..._)}return this}pop(){var t;try{for(;f(this,$t);){const e=f(this,j)[f(this,It)];if(A(this,I,Ws).call(this,!0),A(this,I,nt).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(f(this,Ft)&&f(this,St)){const e=f(this,St);let s;for(;s=e==null?void 0:e.shift();)(t=f(this,Qt))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:n}=e,i=f(this,pt).get(t);if(i!==void 0){const o=f(this,j)[i];if(A(this,I,nt).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!f(this,qt).call(this,i))return s&&f(this,ze).call(this,i),n&&(n.has="hit",f(this,ue).call(this,n,i)),!0;n&&(n.has="stale",f(this,ue).call(this,n,i))}else n&&(n.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,n=f(this,pt).get(t);if(n===void 0||!s&&f(this,qt).call(this,n))return;const i=f(this,j)[n];return A(this,I,nt).call(this,i)?i.__staleWhileFetching:i}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:c=0,sizeCalculation:u=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:d=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:p=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:b=this.allowStaleOnFetchAbort,context:S,forceRefresh:x=!1,status:y,signal:_}=e;if(!f(this,we))return y&&(y.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,status:y});const w={allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:a,size:c,sizeCalculation:u,noUpdateTTL:h,noDeleteOnFetchRejection:d,allowStaleOnFetchRejection:p,allowStaleOnFetchAbort:b,ignoreFetchAbort:g,status:y,signal:_};let F=f(this,pt).get(t);if(F===void 0){y&&(y.fetch="miss");const D=A(this,I,Vs).call(this,t,F,w,S);return D.__returned=D}{const D=f(this,j)[F];if(A(this,I,nt).call(this,D)){const P=s&&D.__staleWhileFetching!==void 0;return y&&(y.fetch="inflight",P&&(y.returnedStale=!0)),P?D.__staleWhileFetching:D.__returned=D}const W=f(this,qt).call(this,F);if(!x&&!W)return y&&(y.fetch="hit"),A(this,I,ms).call(this,F),n&&f(this,ze).call(this,F),y&&f(this,ue).call(this,y,F),D;const H=A(this,I,Vs).call(this,t,F,w,S),V=H.__staleWhileFetching!==void 0&&s;return y&&(y.fetch=W?"stale":"refresh",V&&W&&(y.returnedStale=!0)),V?H.__staleWhileFetching:H.__returned=H}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=f(this,is);if(!s)throw new Error("no memoMethod provided to constructor");const{context:n,forceRefresh:i,...o}=e,a=this.get(t,o);if(!i&&a!==void 0)return a;const c=s(t,a,{options:o,context:n});return this.set(t,c,o),c}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=e,a=f(this,pt).get(t);if(a!==void 0){const c=f(this,j)[a],u=A(this,I,nt).call(this,c);return o&&f(this,ue).call(this,o,a),f(this,qt).call(this,a)?(o&&(o.get="stale"),u?(o&&s&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?c.__staleWhileFetching:void 0):(i||A(this,I,Ce).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?c:void 0)):(o&&(o.get="hit"),u?c.__staleWhileFetching:(A(this,I,ms).call(this,a),n&&f(this,ze).call(this,a),c))}o&&(o.get="miss")}delete(t){return A(this,I,Ce).call(this,t,"delete")}clear(){return A(this,I,Gn).call(this,"delete")}};Bt=new WeakMap,Dt=new WeakMap,Kt=new WeakMap,Qt=new WeakMap,ns=new WeakMap,is=new WeakMap,$t=new WeakMap,Xt=new WeakMap,pt=new WeakMap,it=new WeakMap,j=new WeakMap,kt=new WeakMap,Rt=new WeakMap,It=new WeakMap,xt=new WeakMap,Zt=new WeakMap,St=new WeakMap,Jt=new WeakMap,Yt=new WeakMap,Ut=new WeakMap,te=new WeakMap,we=new WeakMap,Ft=new WeakMap,I=new WeakSet,Pn=function(){const t=new Gs(f(this,Bt)),e=new Gs(f(this,Bt));R(this,Ut,t),R(this,Yt,e),R(this,$s,(i,o,a=Ze.now())=>{if(e[i]=o!==0?a:0,t[i]=o,o!==0&&this.ttlAutopurge){const c=setTimeout(()=>{f(this,qt).call(this,i)&&A(this,I,Ce).call(this,f(this,it)[i],"expire")},o+1);c.unref&&c.unref()}}),R(this,ze,i=>{e[i]=t[i]!==0?Ze.now():0}),R(this,ue,(i,o)=>{if(t[o]){const a=t[o],c=e[o];if(!a||!c)return;i.ttl=a,i.start=c,i.now=s||n();const u=i.now-c;i.remainingTTL=a-u}});let s=0;const n=()=>{const i=Ze.now();if(this.ttlResolution>0){s=i;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return i};this.getRemainingTTL=i=>{const o=f(this,pt).get(i);if(o===void 0)return 0;const a=t[o],c=e[o];return!a||!c?1/0:a-((s||n())-c)},R(this,qt,i=>{const o=e[i],a=t[i];return!!a&&!!o&&(s||n())-o>a})},ze=new WeakMap,ue=new WeakMap,$s=new WeakMap,qt=new WeakMap,Pr=function(){const t=new Gs(f(this,Bt));R(this,Xt,0),R(this,Jt,t),R(this,Ge,e=>{R(this,Xt,f(this,Xt)-t[e]),t[e]=0}),R(this,bs,(e,s,n,i)=>{if(A(this,I,nt).call(this,s))return 0;if(!ve(n)){if(!i)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof i!="function")throw new TypeError("sizeCalculation must be a function");if(n=i(s,e),!ve(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return n}),R(this,rs,(e,s,n)=>{if(t[e]=s,f(this,Dt)){const i=f(this,Dt)-t[e];for(;f(this,Xt)>i;)A(this,I,Ws).call(this,!0)}R(this,Xt,f(this,Xt)+t[e]),n&&(n.entrySize=s,n.totalCalculatedSize=f(this,Xt))})},Ge=new WeakMap,rs=new WeakMap,bs=new WeakMap,$e=function*({allowStale:t=this.allowStale}={}){if(f(this,$t))for(let e=f(this,xt);A(this,I,Hn).call(this,e)&&(!t&&f(this,qt).call(this,e)||(yield e),e!==f(this,It));)e=f(this,Rt)[e]},be=function*({allowStale:t=this.allowStale}={}){if(f(this,$t))for(let e=f(this,It);A(this,I,Hn).call(this,e)&&(!t&&f(this,qt).call(this,e)||(yield e),e!==f(this,xt));)e=f(this,kt)[e]},Hn=function(t){return t!==void 0&&f(this,pt).get(f(this,it)[t])===t},Ws=function(t){var i,o;const e=f(this,It),s=f(this,it)[e],n=f(this,j)[e];return f(this,we)&&A(this,I,nt).call(this,n)?n.__abortController.abort(new Error("evicted")):(f(this,te)||f(this,Ft))&&(f(this,te)&&((i=f(this,Kt))==null||i.call(this,n,s,"evict")),f(this,Ft)&&((o=f(this,St))==null||o.push([n,s,"evict"]))),f(this,Ge).call(this,e),t&&(f(this,it)[e]=void 0,f(this,j)[e]=void 0,f(this,Zt).push(e)),f(this,$t)===1?(R(this,It,R(this,xt,0)),f(this,Zt).length=0):R(this,It,f(this,kt)[e]),f(this,pt).delete(s),Us(this,$t)._--,e},Vs=function(t,e,s,n){const i=e===void 0?void 0:f(this,j)[e];if(A(this,I,nt).call(this,i))return i;const o=new Ys,{signal:a}=s;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const c={signal:o.signal,options:s,context:n},u=(g,b=!1)=>{const{aborted:S}=o.signal,x=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(S&&!b?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,x&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),S&&!x&&!b)return h(o.signal.reason);const y=d;return f(this,j)[e]===d&&(g===void 0?y.__staleWhileFetching?f(this,j)[e]=y.__staleWhileFetching:A(this,I,Ce).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,c.options))),g},h=g=>{const{aborted:b}=o.signal,S=b&&s.allowStaleOnFetchAbort,x=S||s.allowStaleOnFetchRejection,y=x||s.noDeleteOnFetchRejection,_=d;if(f(this,j)[e]===d&&(!y||_.__staleWhileFetching===void 0?A(this,I,Ce).call(this,t,"fetch"):S||(f(this,j)[e]=_.__staleWhileFetching)),x)return s.status&&_.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),_.__staleWhileFetching;if(_.__returned===_)throw g};s.status&&(s.status.fetchDispatched=!0);const d=new Promise((g,b)=>{var x;const S=(x=f(this,ns))==null?void 0:x.call(this,t,i,c);S&&S instanceof Promise&&S.then(y=>g(y===void 0?void 0:y),b),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=y=>u(y,!0)))})}).then(u,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),h(g))),p=Object.assign(d,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return e===void 0?(this.set(t,p,{...c.options,status:void 0}),e=f(this,pt).get(t)):f(this,j)[e]=p,p},nt=function(t){if(!f(this,we))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof Ys},zn=function(t,e){f(this,Rt)[e]=t,f(this,kt)[t]=e},ms=function(t){t!==f(this,xt)&&(t===f(this,It)?R(this,It,f(this,kt)[t]):A(this,I,zn).call(this,f(this,Rt)[t],f(this,kt)[t]),A(this,I,zn).call(this,f(this,xt),t),R(this,xt,t))},Ce=function(t,e){var n,i,o,a;let s=!1;if(f(this,$t)!==0){const c=f(this,pt).get(t);if(c!==void 0)if(s=!0,f(this,$t)===1)A(this,I,Gn).call(this,e);else{f(this,Ge).call(this,c);const u=f(this,j)[c];if(A(this,I,nt).call(this,u)?u.__abortController.abort(new Error("deleted")):(f(this,te)||f(this,Ft))&&(f(this,te)&&((n=f(this,Kt))==null||n.call(this,u,t,e)),f(this,Ft)&&((i=f(this,St))==null||i.push([u,t,e]))),f(this,pt).delete(t),f(this,it)[c]=void 0,f(this,j)[c]=void 0,c===f(this,xt))R(this,xt,f(this,Rt)[c]);else if(c===f(this,It))R(this,It,f(this,kt)[c]);else{const h=f(this,Rt)[c];f(this,kt)[h]=f(this,kt)[c];const d=f(this,kt)[c];f(this,Rt)[d]=f(this,Rt)[c]}Us(this,$t)._--,f(this,Zt).push(c)}}if(f(this,Ft)&&((o=f(this,St))!=null&&o.length)){const c=f(this,St);let u;for(;u=c==null?void 0:c.shift();)(a=f(this,Qt))==null||a.call(this,...u)}return s},Gn=function(t){var e,s,n;for(const i of A(this,I,be).call(this,{allowStale:!0})){const o=f(this,j)[i];if(A(this,I,nt).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=f(this,it)[i];f(this,te)&&((e=f(this,Kt))==null||e.call(this,o,a,t)),f(this,Ft)&&((s=f(this,St))==null||s.push([o,a,t]))}}if(f(this,pt).clear(),f(this,j).fill(void 0),f(this,it).fill(void 0),f(this,Ut)&&f(this,Yt)&&(f(this,Ut).fill(0),f(this,Yt).fill(0)),f(this,Jt)&&f(this,Jt).fill(0),R(this,It,0),R(this,xt,0),f(this,Zt).length=0,R(this,Xt,0),R(this,$t,0),f(this,Ft)&&f(this,St)){const i=f(this,St);let o;for(;o=i==null?void 0:i.shift();)(n=f(this,Qt))==null||n.call(this,...o)}};let jn=ri;class Hr{constructor(){l(this,"_syncStatus",{status:ea.done,foldersProgress:[]});l(this,"_syncEnabledState",Ri.initializing);l(this,"_workspaceGuidelines",[]);l(this,"_openUserGuidelinesInput",!1);l(this,"_userGuidelines");l(this,"_contextStore",new Wa);l(this,"_prevOpenFiles",[]);l(this,"_disableContext",!1);l(this,"_enableAgentMemories",!1);l(this,"subscribers",new Set);l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case lt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case lt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case lt.fileRangesSelected:this.updateSelections(e.data);break;case lt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case lt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case lt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});l(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:bt.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});l(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});l(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});l(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});l(this,"addFile",t=>{this.addFiles([t])});l(this,"addFiles",t=>{this.updateFiles(t,[])});l(this,"removeFile",t=>{this.removeFiles([t])});l(this,"removeFiles",t=>{this.updateFiles([],t)});l(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});l(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});l(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...yn(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});l(this,"updateRules",(t,e)=>{const s=o=>({rule:o,...Xo(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});l(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});l(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});l(this,"setCurrentlyOpenFiles",t=>{const e=t.map(n=>({recentFile:n,...yn(n)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,n=>n.id),s.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.recentFile&&(i.file=i.recentFile,delete i.recentFile)}),e.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.file&&(i.recentFile=i.file,delete i.file)}),this.notifySubscribers()});l(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});l(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,n=t.overLimit||((e==null?void 0:e.overLimit)??!1),i={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:bt.active,referenceCount:1,showWarning:n,rulesAndGuidelinesState:e};this._contextStore.update([i],s,o=>{var a;return o.id+String((a=o.userGuidelines)==null?void 0:a.overLimit)}),this.notifySubscribers()});l(this,"onGuidelinesStateUpdate",t=>{var n;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const i=e||{overLimit:!1,contents:"",lengthLimit:((n=t.rulesAndGuidelines)==null?void 0:n.lengthLimit)??2e3};this.updateUserGuidelines(i,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(i=>i.sourceFolder))});l(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(n=>n.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));l(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});l(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});l(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});l(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Qs),s=t.map(n=>({selection:n,...yn(n)}));this._contextStore.update([],e,n=>n.id),this._contextStore.update(s,[],n=>n.id),this.notifySubscribers()});l(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});l(this,"markInactive",t=>{this.markItemsInactive([t])});l(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,bt.inactive)}),this.notifySubscribers()});l(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});l(this,"markActive",t=>{this.markItemsActive([t])});l(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,bt.active)}),this.notifySubscribers()});l(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});l(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});l(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Yn(t)&&!Ks(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Ks)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Qs)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(ti)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Xs)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(ei)}get userGuidelines(){return this._contextStore.values.filter(Zs)}get agentMemories(){return[{...Qo,status:this._enableAgentMemories?bt.active:bt.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Js(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===bt.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===bt.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===bt.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===bt.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===bt.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===bt.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var c;if(this.syncEnabledState===Ri.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(u=>u.progress!==void 0);if(t.length===0)return;const e=t.reduce((u,h)=>{var d;return u+(((d=h==null?void 0:h.progress)==null?void 0:d.trackedFiles)??0)},0),s=t.reduce((u,h)=>{var d;return u+(((d=h==null?void 0:h.progress)==null?void 0:d.backlogSize)??0)},0),n=Math.max(e,0),i=Math.min(Math.max(s,0),n),o=n-i,a=[];for(const u of t)(c=u==null?void 0:u.progress)!=null&&c.newlyTracked&&a.push(u.folderRoot);return{status:this._syncStatus.status,totalFiles:n,syncedCount:o,backlogSize:i,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Xs(t)||Zs(t)||sn(t)||Js(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===bt.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===bt.inactive)}get isContextDisabled(){return this._disableContext}}class Wa{constructor(){l(this,"_cache",new jn({max:1e3}));l(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));l(this,"clear",()=>{this._cache.clear()});l(this,"update",(t,e,s)=>{t.forEach(n=>this.addInPlace(n,s)),e.forEach(n=>this.removeInPlace(n,s))});l(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});l(this,"addInPlace",(t,e)=>{const s=e(t),n=t.referenceCount??1,i=this._cache.get(s),o=t.status??(i==null?void 0:i.status)??bt.active;i?(i.referenceCount+=n,i.status=o,i.pinned=t.pinned??i.pinned,i.showWarning=t.showWarning??i.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in i&&(i.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in i&&(i.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:n,status:o})});l(this,"removeInPlace",(t,e)=>{const s=e(t),n=this._cache.get(s);n&&(n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(s))});l(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});l(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});l(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});l(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});l(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===bt.active?bt.inactive:bt.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Va{constructor(t,e,s){l(this,"_originalModel");l(this,"_modifiedModel");l(this,"_fullEdits",[]);l(this,"_currEdit");l(this,"_currOriginalEdit");l(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(e=>{this._modifiedModel.applyEdits([e])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});l(this,"finish",()=>this._completeCurrEdit());l(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);l(this,"_completeCurrEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};if(!t)return e;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const s=this._nextModifiedInsertPosition(),n=t.stagedEndLine-t.stagedStartLine,i={range:new this._monaco.Range(s.lineNumber,0,s.lineNumber+n,0),text:""};e.modified.push(i),this._modifiedModel.applyEdits([i]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return e});l(this,"_startNewEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},e.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),e});l(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const e=this._nextModifiedInsertPosition(),s={...this._currEdit,text:t.newText,range:new this._monaco.Range(e.lineNumber,e.column,e.lineNumber,e.column)};return this._modifiedModel.applyEdits([s]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[s]:[]}});l(this,"_nextModifiedInsertPosition",()=>{var e;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((e=this._currEdit.text)==null?void 0:e.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=e,this._monaco=s,this._originalModel=this._monaco.editor.createModel(e),this._modifiedModel=this._monaco.editor.createModel(e)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class Ba{constructor(t,e,s){l(this,"_asyncMsgSender");l(this,"_editor");l(this,"_chatModel");l(this,"_focusModel",new Fr);l(this,"_hasScrolledOnInit",!1);l(this,"_markHasScrolledOnInit",Ln(()=>{this._hasScrolledOnInit=!0},200));l(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});l(this,"_subscribers",new Set);l(this,"_disposables",[]);l(this,"_rootChunk");l(this,"_keybindings",ht({}));l(this,"_requestId",ht(void 0));l(this,"requestId",this._requestId);l(this,"_disableResolution",ht(!1));l(this,"disableResolution",Fi(this._disableResolution));l(this,"_disableApply",ht(!1));l(this,"disableApply",Fi(this._disableApply));l(this,"_currStream");l(this,"_isLoadingDiffChunks",ht(!1));l(this,"_selectionLines",ht(void 0));l(this,"_mode",ht(Ee.edit));l(this,"initializeEditor",t=>{var e,s,n,i,o,a,c,u,h,d,p,g;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new si(new Ar(_s),_s,new Hr),(s=(e=this._monaco.editor).registerCommand)==null||s.call(e,"acceptFocusedChunk",this.acceptFocusedChunk),(i=(n=this._monaco.editor).registerCommand)==null||i.call(n,"rejectFocusedChunk",this.rejectFocusedChunk),(a=(o=this._monaco.editor).registerCommand)==null||a.call(o,"acceptAllChunks",this.acceptAllChunks),(u=(c=this._monaco.editor).registerCommand)==null||u.call(c,"rejectAllChunks",this.rejectAllChunks),(d=(h=this._monaco.editor).registerCommand)==null||d.call(h,"focusNextChunk",this.focusNextChunk),(g=(p=this._monaco.editor).registerCommand)==null||g.call(p,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(b=>this.notifySubscribers())}),this.initialize()});l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});l(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});l(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const e=this.leaves[0];this.revealChunk(e)}this.notifyDiffViewUpdated(),this.notifySubscribers()});l(this,"onMouseMoveModified",t=>{var n,i,o,a,c,u;if(((n=t.target.position)==null?void 0:n.lineNumber)===void 0||this.leaves===void 0)return;const e=this.editorOffset,s=(i=t.target.position)==null?void 0:i.lineNumber;for(let h=0;h<this.leaves.length;h++){const d=this.leaves[h],p=(o=d.unitOfCodeWork.lineChanges)==null?void 0:o.lineChanges[0].modifiedStart,g=(a=d.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedEnd,b=(c=d.unitOfCodeWork.lineChanges)==null?void 0:c.lineChanges[0].originalStart,S=(u=d.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].originalEnd;if(p!==void 0&&g!==void 0&&b!==void 0&&S!==void 0){if(p!==g||s!==p){if(p<=s&&s<g){this.setCurrFocusedChunkIdx(h,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const x=this._editor.getOriginalEditor(),y=x.getOption(this._monaco.editor.EditorOption.lineHeight),_=x.getScrolledVisiblePosition({lineNumber:b,column:0}),w=x.getScrolledVisiblePosition({lineNumber:S+1,column:0});if(_===null||w===null)continue;const F=_.top-y/2+e,D=w.top-y/2+e;if(t.event.posy>=F&&t.event.posy<=D){this.setCurrFocusedChunkIdx(h,!1);break}break}}}});l(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:lt.diffViewWindowFocusChange,data:t})});l(this,"setCurrFocusedChunkIdx",(t,e=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),e&&this.revealCurrFocusedChunk(),this.notifySubscribers())});l(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});l(this,"revealChunk",t=>{var n;const e=(n=t.unitOfCodeWork.lineChanges)==null?void 0:n.lineChanges[0],s=e==null?void 0:e.modifiedStart;s!==void 0&&this._editor.revealLineNearTop(s-1)});l(this,"renderCentralOverlayWidget",t=>{const e=()=>({editor:this._editor,id:"central-overlay-widget"}),s=function(n,i,o){let a,c=i;const u=()=>c.editor.getModifiedEditor(),h=()=>{const d=u();if(!d)return;const p={getDomNode:()=>n,getId:()=>c.id,getPosition:()=>({preference:o.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};a&&d.removeOverlayWidget(a),d.addOverlayWidget(p),a=p};return h(),{update:d=>{c=d,h()},destroy:()=>{const d=u();d&&a&&d.removeOverlayWidget(a)}}}(t,e(),{monaco:this._monaco});return{update:()=>{s.update(e())},destroy:s.destroy}});l(this,"renderInstructionsDrawerViewZone",(t,e)=>{let s=!1,n=e;const i=e.autoFocus??!0,o=h=>{i&&!s&&(this._editor.revealLineNearTop(h),s=!0)},a=h=>({...h,ordinal:Rn.instructionDrawer,editor:this._editor,afterLineNumber:h.line}),c=Vi(t,a(e)),u=[];return i&&u.push(this._editor.onDidUpdateDiff(()=>{o(n.line)})),{update:h=>{const d={...n,...h};Da(d,n)||(c.update(a(d)),n=d,o(d.line))},destroy:()=>{c.destroy(),u.forEach(h=>h.dispose())}}});l(this,"renderActionsViewZone",(t,e)=>{const s=i=>{var a;let o;return o=i.chunk?(a=i.chunk.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedStart:1,{...i,ordinal:Rn.chunkActionPanel,editor:this._editor,afterLineNumber:o?o-1:void 0}},n=Vi(t,s(e));return{update:i=>{n.update(s(i))},destroy:n.destroy}});l(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});l(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});l(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});l(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});l(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});l(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});l(this,"initialize",async()=>{var c;const t=await this._asyncMsgSender.send({type:lt.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:e,instruction:s,keybindings:n,editable:i}=t.data;this._editor.updateOptions({readOnly:!i});const o=ut(this._keybindings);this._keybindings.set(n??o);const a=s==null?void 0:s.selection;a&&(a.start.line===a.end.line&&a.start.character===a.end.character&&this._mode.set(Ee.instruction),ut(this.selectionLines)===void 0&&this._selectionLines.set({start:a.start.line,end:a.end.line})),this.updateModels(e.originalCode??"",e.modifiedCode??"",{rootPath:e.repoRoot,relPath:e.pathName}),(c=this._currStream)==null||c.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});l(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:lt.disposeDiffView})});l(this,"_tryFetchStream",async()=>{var e,s,n;const t=this._asyncMsgSender.stream({type:lt.diffViewFetchPendingStream},15e3,6e4);for await(const i of t)switch(i.type){case lt.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(i.data.requestId);const o=this._editor.getOriginalEditor().getValue();this._currStream=new Va(i.data.streamId,o,this._monaco),this._syncStreamToModels();break}case lt.diffViewDiffStreamEnded:if(((e=this._currStream)==null?void 0:e.id)!==i.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case lt.diffViewDiffStreamChunk:{if(((s=this._currStream)==null?void 0:s.id)!==i.data.streamId)return;const o=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!o)return this.setLoading(!1),void this._cleanupStream();const a=(n=this._currStream)==null?void 0:n.onReceiveChunk(i);a&&(this._applyDeltaDiff(a),ut(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});l(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case lt.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case lt.diffViewAcceptAllChunks:this.acceptAllChunks();break;case lt.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case lt.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case lt.diffViewFocusPrevChunk:this.focusPrevChunk();break;case lt.diffViewFocusNextChunk:this.focusNextChunk()}});l(this,"_applyDeltaDiff",t=>{const e=this._editor.getOriginalEditor().getModel(),s=this._editor.getModifiedEditor().getModel();e&&s&&(e.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(n=>{e.pushEditOperations([],[n],()=>[])}),t.modified.forEach(n=>{s.pushEditOperations([],[n],()=>[])}))});l(this,"_cleanupStream",()=>{var t;if(this._currStream){const e=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(e),this._currStream=void 0,this._resetScrollOnInit()}});l(this,"_syncStreamToModels",()=>{var s,n;const t=(s=this._currStream)==null?void 0:s.originalValue,e=(n=this._currStream)==null?void 0:n.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),e&&e!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(e)});l(this,"acceptChunk",async t=>{ut(this._disableApply)||this.acceptChunks([t])});l(this,"acceptChunks",async(t,e=!1)=>{ut(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,mn.accept,e),await En(),this.areModelsEqual()&&!ut(this.isLoading)&&this.disposeDiffViewPanel())});l(this,"areModelsEqual",()=>{var s,n;const t=(s=this._editor.getModel())==null?void 0:s.original,e=(n=this._editor.getModel())==null?void 0:n.modified;return(t==null?void 0:t.getValue())===(e==null?void 0:e.getValue())});l(this,"rejectChunk",async t=>{this.rejectChunks([t])});l(this,"rejectChunks",async(t,e=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,mn.reject,e),await En(),this.areModelsEqual()&&!ut(this.isLoading)&&this.disposeDiffViewPanel()});l(this,"notifyDiffViewUpdated",Ln(()=>{this.notifyResolvedChunks([],mn.accept)},1e3));l(this,"notifyResolvedChunks",async(t,e,s=!1)=>{var i;const n=(i=this._editor.getModel())==null?void 0:i.original.uri.path;n&&await this._asyncMsgSender.send({type:lt.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:n,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(o=>o.unitOfCodeWork),resolveType:e,shouldApplyToAll:s}},2e3)});l(this,"executeDiffChunks",(t,e)=>{var h,d,p;if(ut(this._disableResolution)||e&&ut(this._disableApply))return;const s=(h=this._editor.getModel())==null?void 0:h.original,n=(d=this._editor.getModel())==null?void 0:d.modified;if(!s||!n||this._currStream!==void 0)return;const i=[],o=[];for(const g of t){const b=(p=g.unitOfCodeWork.lineChanges)==null?void 0:p.lineChanges[0];if(!b||g.unitOfCodeWork.originalCode===void 0||g.unitOfCodeWork.modifiedCode===void 0)continue;let S={startLineNumber:b.originalStart,startColumn:1,endLineNumber:b.originalEnd,endColumn:1},x={startLineNumber:b.modifiedStart,startColumn:1,endLineNumber:b.modifiedEnd,endColumn:1};const y=e?g.unitOfCodeWork.modifiedCode:g.unitOfCodeWork.originalCode;y!==void 0&&(i.push({range:S,text:y}),o.push({range:x,text:y}))}s.pushEditOperations([],i,()=>[]),n.pushEditOperations([],o,()=>[]);const a=this._focusModel.nextIdx({nowrap:!0});if(a===void 0)return;const c=a===this._focusModel.focusedItemIdx?a-1:a,u=this._focusModel.items[c];u&&this.revealChunk(u)});l(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});l(this,"handleInstructionSubmit",t=>{const e=this._editor.getModifiedEditor(),s=this.getSelectedCodeDetails(e);if(!s)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,s)});l(this,"updateModels",(t,e,s)=>{var o,a;const n=(a=(o=this._editor.getModel())==null?void 0:o.original)==null?void 0:a.uri,i=(s&&this._monaco.Uri.file(s.relPath))??n;if(i)if((n==null?void 0:n.fsPath)!==i.fsPath||(n==null?void 0:n.authority)!==i.authority){const c=i.with({fragment:crypto.randomUUID()}),u=i.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,c),modified:this._monaco.editor.createModel(e??"",void 0,u)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==e&&this.getModifiedEditor().setValue(e??"");else console.warn("No URI found for diff view. Not updating models.")});l(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=s,this._asyncMsgSender=new Ho(n=>_s.postMessage(n)),this.initializeEditor(e)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Rr(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var i,o;const t=[],e=this._editor.getLineChanges(),s=(i=this._editor.getModel())==null?void 0:i.original,n=(o=this._editor.getModel())==null?void 0:o.modified;if(e&&s&&n){for(const a of e){const c=Qi({startLineNumber:a.originalStartLineNumber,startColumn:1,endLineNumber:a.originalEndLineNumber,endColumn:1}),u=Qi({startLineNumber:a.modifiedStartLineNumber,startColumn:1,endLineNumber:a.modifiedEndLineNumber,endColumn:1}),h=Ka(this._editor,c,u);t.push(h)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(a=>a.id)}}}getSelectedCodeDetails(t){const e=t.getModel();if(!e)return null;const s=e.getLanguageId(),n=1,i=1,o={lineNumber:e.getLineCount(),column:e.getLineMaxColumn(e.getLineCount())},a=ut(this._selectionLines);if(!a)throw new Error("No selection lines found");const c=Math.min(a.end+1,o.lineNumber),u=new this._monaco.Range(a.start+1,1,c,e.getLineMaxColumn(c));let h=e.getValueInRange(u);c<e.getLineCount()&&(h+=e.getEOL());const d=new this._monaco.Range(n,i,u.startLineNumber,u.startColumn),p=Math.min(u.endLineNumber+1,o.lineNumber),g=new this._monaco.Range(p,1,o.lineNumber,o.column);return{selectedCode:h,prefix:e.getValueInRange(d),suffix:e.getValueInRange(g),path:e.uri.path,language:s,prefixBegin:d.startLineNumber-1,suffixEnd:g.endLineNumber-1}}}function Ka(r,t,e){var i,o;const s=(i=r.getModel())==null?void 0:i.original,n=(o=r.getModel())==null?void 0:o.modified;if(!s||!n)throw new Error("No models found");return function(a,c,u,h){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:a,modifiedCode:c,lineChanges:{lineChanges:[{originalStart:u.startLineNumber,originalEnd:u.endLineNumber,modifiedStart:h.startLineNumber,modifiedEnd:h.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(s.getValueInRange(t),n.getValueInRange(e),t,e)}function Qi(r){return r.endLineNumber===0?{startLineNumber:r.startLineNumber+1,startColumn:1,endLineNumber:r.startLineNumber+1,endColumn:1}:{startLineNumber:r.startLineNumber,startColumn:1,endLineNumber:r.endLineNumber+1,endColumn:1}}function Xi(r){let t,e;return t=new Ie({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Qa]},$$scope:{ctx:r}}}),t.$on("click",function(){fe(r[4])&&r[4].apply(this,arguments)}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){r=s;const i={};132096&n&&(i.$$scope={dirty:n,ctx:r}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Qa(r){let t,e,s;return t=new Ve({props:{keybinding:r[10].acceptFocusedChunk}}),{c(){E(t.$$.fragment),e=ct(`
        Accept`)},m(n,i){T(t,n,i),L(n,e,i),s=!0},p(n,i){const o={};1024&i&&(o.keybinding=n[10].acceptFocusedChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){C(t.$$.fragment,n),s=!1},d(n){n&&O(e),k(t,n)}}}function Xa(r){let t,e,s;return t=new Ve({props:{keybinding:r[10].rejectFocusedChunk}}),{c(){E(t.$$.fragment),e=ct(`
      Reject`)},m(n,i){T(t,n,i),L(n,e,i),s=!0},p(n,i){const o={};1024&i&&(o.keybinding=n[10].rejectFocusedChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){C(t.$$.fragment,n),s=!1},d(n){n&&O(e),k(t,n)}}}function Za(r){let t,e,s,n,i,o,a,c,u,h,d=!r[3]&&Xi(r);return a=new Ie({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Xa]},$$scope:{ctx:r}}}),a.$on("click",function(){fe(r[5])&&r[5].apply(this,arguments)}),{c(){t=tt("div"),s=J(),n=tt("div"),i=tt("div"),d&&d.c(),o=J(),E(a.$$.fragment),X(t,"class","svelte-zm1705"),Gt(t,"c-chunk-diff-border--focused",!!r[7]&&r[1]),X(i,"class","c-button-container svelte-zm1705"),Gt(i,"c-button-container--focused",r[1]),Gt(i,"c-button-container--transparent",r[9]),X(n,"class","c-chunk-action-panel-anchor svelte-zm1705"),Ye(n,"top",r[8]+"px"),Gt(n,"c-chunk-action-panel-anchor--left",r[0]==="left"),Gt(n,"c-chunk-action-panel-anchor--right",r[0]==="right"),Gt(n,"c-chunk-action-panel-anchor--focused",r[1])},m(p,g){L(p,t,g),L(p,s,g),L(p,n,g),et(n,i),d&&d.m(i,null),et(i,o),T(a,i,null),c=!0,u||(h=[Cr(e=r[6].renderActionsViewZone(t,{chunk:r[7],heightInPx:r[2],onDomNodeTop:r[12]})),Me(n,"mouseenter",r[13]),Me(n,"mousemove",r[13]),Me(n,"mouseleave",r[13])],u=!0)},p(p,[g]){r=p,e&&fe(e.update)&&132&g&&e.update.call(null,{chunk:r[7],heightInPx:r[2],onDomNodeTop:r[12]}),(!c||130&g)&&Gt(t,"c-chunk-diff-border--focused",!!r[7]&&r[1]),r[3]?d&&(mt(),C(d,1,1,()=>{d=null}),_t()):d?(d.p(r,g),8&g&&$(d,1)):(d=Xi(r),d.c(),$(d,1),d.m(i,o));const b={};132096&g&&(b.$$scope={dirty:g,ctx:r}),a.$set(b),(!c||2&g)&&Gt(i,"c-button-container--focused",r[1]),(!c||512&g)&&Gt(i,"c-button-container--transparent",r[9]),(!c||256&g)&&Ye(n,"top",r[8]+"px"),(!c||1&g)&&Gt(n,"c-chunk-action-panel-anchor--left",r[0]==="left"),(!c||1&g)&&Gt(n,"c-chunk-action-panel-anchor--right",r[0]==="right"),(!c||2&g)&&Gt(n,"c-chunk-action-panel-anchor--focused",r[1])},i(p){c||($(d),$(a.$$.fragment,p),c=!0)},o(p){C(d),C(a.$$.fragment,p),c=!1},d(p){p&&(O(t),O(s),O(n)),d&&d.d(),k(a),u=!1,Zn(h)}}}function Ja(r,t,e){let s,{align:n="right"}=t,{isFocused:i}=t,{heightInPx:o=1}=t,{disableApply:a=!1}=t,{onAccept:c}=t,{onReject:u}=t,{diffViewModel:h}=t,{leaf:d}=t;const p=h.keybindings;We(r,p,y=>e(10,s=y));let g=0,b,S=!1;function x(){b&&(clearTimeout(b),b=void 0),e(9,S=!1)}return r.$$set=y=>{"align"in y&&e(0,n=y.align),"isFocused"in y&&e(1,i=y.isFocused),"heightInPx"in y&&e(2,o=y.heightInPx),"disableApply"in y&&e(3,a=y.disableApply),"onAccept"in y&&e(4,c=y.onAccept),"onReject"in y&&e(5,u=y.onReject),"diffViewModel"in y&&e(6,h=y.diffViewModel),"leaf"in y&&e(7,d=y.leaf)},[n,i,o,a,c,u,h,d,g,S,s,p,y=>{e(8,g=y)},function(y){y.target.closest(".c-button-container")?x():y.type==="mouseenter"||y.type==="mousemove"?(x(),b=setTimeout(()=>{e(9,S=!0)},400)):y.type==="mouseleave"&&x()}]}class Ya extends Te{constructor(t){super(),ke(this,t,Ja,Za,Fe,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function Zi(r){let t,e,s;function n(o){r[18](o)}let i={onOpenChange:r[16],content:r[3],triggerOn:[aa.Hover],$$slots:{default:[ec]},$$scope:{ctx:r}};return r[4]!==void 0&&(i.requestClose=r[4]),t=new ca({props:i}),ts.push(()=>Mo(t,"requestClose",n)),{c(){E(t.$$.fragment)},m(o,a){T(t,o,a),s=!0},p(o,a){const c={};8&a&&(c.content=o[3]),1048576&a&&(c.$$scope={dirty:a,ctx:o}),!e&&16&a&&(e=!0,c.requestClose=o[4],Io(()=>e=!1)),t.$set(c)},i(o){s||($(t.$$.fragment,o),s=!0)},o(o){C(t.$$.fragment,o),s=!1},d(o){k(t,o)}}}function tc(r){let t,e;return t=new ra({}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function ec(r){let t,e;return t=new jo({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[tc]},$$scope:{ctx:r}}}),t.$on("click",r[17]),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1048576&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function sc(r){let t;return{c(){t=tt("span"),t.textContent="No changes",X(t,"class","c-diff-page-counter svelte-4zjwll")},m(e,s){L(e,t,s)},p:Z,i:Z,o:Z,d(e){e&&O(t)}}}function nc(r){var u,h;let t,e,s,n,i,o,a,c=((h=(u=r[1])==null?void 0:u.leaves)==null?void 0:h.length)+"";return o=new xr({props:{size:1,loading:r[10]}}),{c(){t=tt("span"),e=ct(r[2]),s=ct(" of "),n=ct(c),i=J(),E(o.$$.fragment),X(t,"class","c-diff-page-counter svelte-4zjwll")},m(d,p){L(d,t,p),et(t,e),et(t,s),et(t,n),et(t,i),T(o,t,null),a=!0},p(d,p){var b,S;(!a||4&p)&&Pt(e,d[2]),(!a||2&p)&&c!==(c=((S=(b=d[1])==null?void 0:b.leaves)==null?void 0:S.length)+"")&&Pt(n,c);const g={};1024&p&&(g.loading=d[10]),o.$set(g)},i(d){a||($(o.$$.fragment,d),a=!0)},o(d){C(o.$$.fragment,d),a=!1},d(d){d&&O(t),k(o)}}}function ic(r){let t,e,s,n;return s=new xr({props:{size:1,loading:r[10]}}),{c(){t=tt("span"),e=ct(`Generating changes
        `),E(s.$$.fragment),X(t,"class","c-diff-page-counter svelte-4zjwll")},m(i,o){L(i,t,o),et(t,e),T(s,t,null),n=!0},p(i,o){const a={};1024&o&&(a.loading=i[10]),s.$set(a)},i(i){n||($(s.$$.fragment,i),n=!0)},o(i){C(s.$$.fragment,i),n=!1},d(i){i&&O(t),k(s)}}}function Ji(r){let t,e,s,n,i,o;t=new Ie({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[rc]},$$scope:{ctx:r}}}),t.$on("click",function(){fe(r[0].focusPrevChunk)&&r[0].focusPrevChunk.apply(this,arguments)}),s=new Ie({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[oc]},$$scope:{ctx:r}}}),s.$on("click",function(){fe(r[0].focusNextChunk)&&r[0].focusNextChunk.apply(this,arguments)});let a=!r[12]&&Yi(r);return{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment),n=J(),a&&a.c(),i=re()},m(c,u){T(t,c,u),L(c,e,u),T(s,c,u),L(c,n,u),a&&a.m(c,u),L(c,i,u),o=!0},p(c,u){r=c;const h={};1050624&u&&(h.$$scope={dirty:u,ctx:r}),t.$set(h);const d={};1050624&u&&(d.$$scope={dirty:u,ctx:r}),s.$set(d),r[12]?a&&(mt(),C(a,1,1,()=>{a=null}),_t()):a?(a.p(r,u),4096&u&&$(a,1)):(a=Yi(r),a.c(),$(a,1),a.m(i.parentNode,i))},i(c){o||($(t.$$.fragment,c),$(s.$$.fragment,c),$(a),o=!0)},o(c){C(t.$$.fragment,c),C(s.$$.fragment,c),C(a),o=!1},d(c){c&&(O(e),O(n),O(i)),k(t,c),k(s,c),a&&a.d(c)}}}function rc(r){let t,e,s;return t=new Ve({props:{keybinding:r[11].focusPrevChunk}}),{c(){E(t.$$.fragment),e=ct(`
        Back`)},m(n,i){T(t,n,i),L(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].focusPrevChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){C(t.$$.fragment,n),s=!1},d(n){n&&O(e),k(t,n)}}}function oc(r){let t,e,s;return t=new Ve({props:{keybinding:r[11].focusNextChunk}}),{c(){E(t.$$.fragment),e=ct(`
        Next`)},m(n,i){T(t,n,i),L(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].focusNextChunk),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){C(t.$$.fragment,n),s=!1},d(n){n&&O(e),k(t,n)}}}function Yi(r){let t,e,s,n=!r[13]&&tr(r);return e=new Ie({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[cc]},$$scope:{ctx:r}}}),e.$on("click",function(){fe(r[0].rejectAllChunks)&&r[0].rejectAllChunks.apply(this,arguments)}),{c(){n&&n.c(),t=J(),E(e.$$.fragment)},m(i,o){n&&n.m(i,o),L(i,t,o),T(e,i,o),s=!0},p(i,o){(r=i)[13]?n&&(mt(),C(n,1,1,()=>{n=null}),_t()):n?(n.p(r,o),8192&o&&$(n,1)):(n=tr(r),n.c(),$(n,1),n.m(t.parentNode,t));const a={};1050624&o&&(a.$$scope={dirty:o,ctx:r}),e.$set(a)},i(i){s||($(n),$(e.$$.fragment,i),s=!0)},o(i){C(n),C(e.$$.fragment,i),s=!1},d(i){i&&O(t),n&&n.d(i),k(e,i)}}}function tr(r){let t,e;return t=new Ie({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[ac]},$$scope:{ctx:r}}}),t.$on("click",function(){fe(r[0].acceptAllChunks)&&r[0].acceptAllChunks.apply(this,arguments)}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){r=s;const i={};1050624&n&&(i.$$scope={dirty:n,ctx:r}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function ac(r){let t,e,s;return t=new Ve({props:{keybinding:r[11].acceptAllChunks}}),{c(){E(t.$$.fragment),e=ct(`
            Accept All`)},m(n,i){T(t,n,i),L(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].acceptAllChunks),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){C(t.$$.fragment,n),s=!1},d(n){n&&O(e),k(t,n)}}}function cc(r){let t,e,s;return t=new Ve({props:{keybinding:r[11].rejectAllChunks}}),{c(){E(t.$$.fragment),e=ct(`
          Reject All`)},m(n,i){T(t,n,i),L(n,e,i),s=!0},p(n,i){const o={};2048&i&&(o.keybinding=n[11].rejectAllChunks),t.$set(o)},i(n){s||($(t.$$.fragment,n),s=!0)},o(n){C(t.$$.fragment,n),s=!1},d(n){n&&O(e),k(t,n)}}}function lc(r){let t,e,s,n,i,o,a,c=r[9]&&Zi(r);const u=[ic,nc,sc],h=[];function d(g,b){return!g[5]&&g[10]?0:g[5]?1:2}n=d(r),i=h[n]=u[n](r);let p=r[5]&&Ji(r);return{c(){t=tt("div"),e=tt("div"),c&&c.c(),s=J(),i.c(),o=J(),p&&p.c(),X(e,"class","c-button-container svelte-4zjwll"),X(t,"class","c-top-action-panel-anchor svelte-4zjwll")},m(g,b){L(g,t,b),et(t,e),c&&c.m(e,null),et(e,s),h[n].m(e,null),et(e,o),p&&p.m(e,null),a=!0},p(g,[b]){g[9]?c?(c.p(g,b),512&b&&$(c,1)):(c=Zi(g),c.c(),$(c,1),c.m(e,s)):c&&(mt(),C(c,1,1,()=>{c=null}),_t());let S=n;n=d(g),n===S?h[n].p(g,b):(mt(),C(h[S],1,1,()=>{h[S]=null}),_t(),i=h[n],i?i.p(g,b):(i=h[n]=u[n](g),i.c()),$(i,1),i.m(e,o)),g[5]?p?(p.p(g,b),32&b&&$(p,1)):(p=Ji(g),p.c(),$(p,1),p.m(e,null)):p&&(mt(),C(p,1,1,()=>{p=null}),_t())},i(g){a||($(c),$(i),$(p),a=!0)},o(g){C(c),C(i),C(p),a=!1},d(g){g&&O(t),c&&c.d(),h[n].d(),p&&p.d()}}}function uc(r,t,e){let s,n,i,o,a,c,u,h,d,p,g=Z,b=()=>(g(),g=de(_,P=>e(1,c=P)),_),S=Z,x=Z,y=Z;r.$$.on_destroy.push(()=>g()),r.$$.on_destroy.push(()=>S()),r.$$.on_destroy.push(()=>x()),r.$$.on_destroy.push(()=>y());let{diffViewModel:_}=t;b();const w=_.keybindings;We(r,w,P=>e(11,h=P));const F=_.requestId;We(r,F,P=>e(9,a=P));let D,W="x",H="Copy request ID",V=()=>{};return r.$$set=P=>{"diffViewModel"in P&&b(e(0,_=P.diffViewModel))},r.$$.update=()=>{var P;2&r.$$.dirty&&(e(8,s=c.disableResolution),x(),x=de(s,Ct=>e(12,d=Ct))),2&r.$$.dirty&&(e(7,n=c.disableApply),y(),y=de(n,Ct=>e(13,p=Ct))),2&r.$$.dirty&&(c.currFocusedChunkIdx!==void 0?e(2,W=(c.currFocusedChunkIdx+1).toString()):e(2,W="x")),2&r.$$.dirty&&(e(6,i=c.isLoading),S(),S=de(i,Ct=>e(10,u=Ct))),2&r.$$.dirty&&e(5,o=!!((P=c.leaves)!=null&&P.length))},[_,c,W,H,V,o,i,n,s,a,u,h,d,p,w,F,function(P){P||(clearTimeout(D),D=void 0,e(3,H="Copy request ID"))},async function(){a&&(await navigator.clipboard.writeText(a),e(3,H="Copied!"),clearTimeout(D),D=setTimeout(V,1500))},function(P){V=P,e(4,V)}]}class hc extends Te{constructor(t){super(),ke(this,t,uc,lc,Fe,{diffViewModel:0})}}function dc(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=Tn(n,s[i]);return{c(){t=Eo("svg"),e=new To(!0),this.h()},l(i){t=ko(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Fo(t);e=Ao(o,!0),o.forEach(O),this.h()},h(){e.a=null,Ai(t,n)},m(i,o){Oo(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',t)},p(i,[o]){Ai(t,n=Lo(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:Z,o:Z,d(i){i&&O(t)}}}function fc(r,t,e){return r.$$set=s=>{e(0,t=Tn(Tn({},t),Oi(s)))},[t=Oi(t)]}class pc extends Te{constructor(t){super(),ke(this,t,fc,dc,Fe,{})}}var zr="Expected a function",er=NaN,gc="[object Symbol]",mc=/^\s+|\s+$/g,_c=/^[-+]0x[0-9a-f]+$/i,yc=/^0b[01]+$/i,vc=/^0o[0-7]+$/i,$c=parseInt,bc=typeof Wt=="object"&&Wt&&Wt.Object===Object&&Wt,Cc=typeof self=="object"&&self&&self.Object===Object&&self,xc=bc||Cc||Function("return this")(),Sc=Object.prototype.toString,wc=Math.max,Mc=Math.min,Sn=function(){return xc.Date.now()};function Ic(r,t,e){var s,n,i,o,a,c,u=0,h=!1,d=!1,p=!0;if(typeof r!="function")throw new TypeError(zr);function g(_){var w=s,F=n;return s=n=void 0,u=_,o=r.apply(F,w)}function b(_){var w=_-c;return c===void 0||w>=t||w<0||d&&_-u>=i}function S(){var _=Sn();if(b(_))return x(_);a=setTimeout(S,function(w){var F=t-(w-c);return d?Mc(F,i-(w-u)):F}(_))}function x(_){return a=void 0,p&&s?g(_):(s=n=void 0,o)}function y(){var _=Sn(),w=b(_);if(s=arguments,n=this,c=_,w){if(a===void 0)return function(F){return u=F,a=setTimeout(S,t),h?g(F):o}(c);if(d)return a=setTimeout(S,t),g(c)}return a===void 0&&(a=setTimeout(S,t)),o}return t=sr(t)||0,tn(e)&&(h=!!e.leading,i=(d="maxWait"in e)?wc(sr(e.maxWait)||0,t):i,p="trailing"in e?!!e.trailing:p),y.cancel=function(){a!==void 0&&clearTimeout(a),u=0,s=c=n=a=void 0},y.flush=function(){return a===void 0?o:x(Sn())},y}function tn(r){var t=typeof r;return!!r&&(t=="object"||t=="function")}function sr(r){if(typeof r=="number")return r;if(function(s){return typeof s=="symbol"||function(n){return!!n&&typeof n=="object"}(s)&&Sc.call(s)==gc}(r))return er;if(tn(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=tn(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=r.replace(mc,"");var e=yc.test(r);return e||vc.test(r)?$c(r.slice(2),e?2:8):_c.test(r)?er:+r}const Ec=Xn(function(r,t,e){var s=!0,n=!0;if(typeof r!="function")throw new TypeError(zr);return tn(e)&&(s="leading"in e?!!e.leading:s,n="trailing"in e?!!e.trailing:n),Ic(r,t,{leading:s,maxWait:t,trailing:n})});function pe(r){return Array.isArray?Array.isArray(r):Vr(r)==="[object Array]"}const Tc=1/0;function kc(r){return r==null?"":function(t){if(typeof t=="string")return t;let e=t+"";return e=="0"&&1/t==-Tc?"-0":e}(r)}function ie(r){return typeof r=="string"}function Gr(r){return typeof r=="number"}function Fc(r){return r===!0||r===!1||function(t){return Wr(t)&&t!==null}(r)&&Vr(r)=="[object Boolean]"}function Wr(r){return typeof r=="object"}function jt(r){return r!=null}function wn(r){return!r.trim().length}function Vr(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}const Ac=r=>`Missing ${r} property in key`,Oc=r=>`Property 'weight' in key '${r}' must be a positive integer`,nr=Object.prototype.hasOwnProperty;class Lc{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach(s=>{let n=Br(s);this._keys.push(n),this._keyMap[n.id]=n,e+=n.weight}),this._keys.forEach(s=>{s.weight/=e})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Br(r){let t=null,e=null,s=null,n=1,i=null;if(ie(r)||pe(r))s=r,t=ir(r),e=Wn(r);else{if(!nr.call(r,"name"))throw new Error(Ac("name"));const o=r.name;if(s=o,nr.call(r,"weight")&&(n=r.weight,n<=0))throw new Error(Oc(o));t=ir(o),e=Wn(o),i=r.getFn}return{path:t,id:e,weight:n,src:s,getFn:i}}function ir(r){return pe(r)?r:r.split(".")}function Wn(r){return pe(r)?r.join("."):r}var U={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(r,t)=>r.score===t.score?r.idx<t.idx?-1:1:r.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(r,t){let e=[],s=!1;const n=(i,o,a)=>{if(jt(i))if(o[a]){const c=i[o[a]];if(!jt(c))return;if(a===o.length-1&&(ie(c)||Gr(c)||Fc(c)))e.push(kc(c));else if(pe(c)){s=!0;for(let u=0,h=c.length;u<h;u+=1)n(c[u],o,a+1)}else o.length&&n(c,o,a+1)}else e.push(i)};return n(r,ie(t)?t.split("."):t,0),s?e:e[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const Nc=/[^ ]+/g;class ni{constructor({getFn:t=U.getFn,fieldNormWeight:e=U.fieldNormWeight}={}){this.norm=function(s=1,n=3){const i=new Map,o=Math.pow(10,n);return{get(a){const c=a.match(Nc).length;if(i.has(c))return i.get(c);const u=1/Math.pow(c,.5*s),h=parseFloat(Math.round(u*o)/o);return i.set(c,h),h},clear(){i.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((e,s)=>{this._keysMap[e.id]=s})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,ie(this.docs[0])?this.docs.forEach((t,e)=>{this._addString(t,e)}):this.docs.forEach((t,e)=>{this._addObject(t,e)}),this.norm.clear())}add(t){const e=this.size();ie(t)?this._addString(t,e):this._addObject(t,e)}removeAt(t){this.records.splice(t,1);for(let e=t,s=this.size();e<s;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!jt(t)||wn(t))return;let s={v:t,i:e,n:this.norm.get(t)};this.records.push(s)}_addObject(t,e){let s={i:e,$:{}};this.keys.forEach((n,i)=>{let o=n.getFn?n.getFn(t):this.getFn(t,n.path);if(jt(o)){if(pe(o)){let a=[];const c=[{nestedArrIndex:-1,value:o}];for(;c.length;){const{nestedArrIndex:u,value:h}=c.pop();if(jt(h))if(ie(h)&&!wn(h)){let d={v:h,i:u,n:this.norm.get(h)};a.push(d)}else pe(h)&&h.forEach((d,p)=>{c.push({nestedArrIndex:p,value:d})})}s.$[i]=a}else if(ie(o)&&!wn(o)){let a={v:o,n:this.norm.get(o)};s.$[i]=a}}}),this.records.push(s)}toJSON(){return{keys:this.keys,records:this.records}}}function Kr(r,t,{getFn:e=U.getFn,fieldNormWeight:s=U.fieldNormWeight}={}){const n=new ni({getFn:e,fieldNormWeight:s});return n.setKeys(r.map(Br)),n.setSources(t),n.create(),n}function Hs(r,{errors:t=0,currentLocation:e=0,expectedLocation:s=0,distance:n=U.distance,ignoreLocation:i=U.ignoreLocation}={}){const o=t/r.length;if(i)return o;const a=Math.abs(s-e);return n?o+a/n:a?1:o}const je=32;function Dc(r,t,e,{location:s=U.location,distance:n=U.distance,threshold:i=U.threshold,findAllMatches:o=U.findAllMatches,minMatchCharLength:a=U.minMatchCharLength,includeMatches:c=U.includeMatches,ignoreLocation:u=U.ignoreLocation}={}){if(t.length>je)throw new Error(`Pattern length exceeds max of ${je}.`);const h=t.length,d=r.length,p=Math.max(0,Math.min(s,d));let g=i,b=p;const S=a>1||c,x=S?Array(d):[];let y;for(;(y=r.indexOf(t,b))>-1;){let H=Hs(t,{currentLocation:y,expectedLocation:p,distance:n,ignoreLocation:u});if(g=Math.min(H,g),b=y+h,S){let V=0;for(;V<h;)x[y+V]=1,V+=1}}b=-1;let _=[],w=1,F=h+d;const D=1<<h-1;for(let H=0;H<h;H+=1){let V=0,P=F;for(;V<P;)Hs(t,{errors:H,currentLocation:p+P,expectedLocation:p,distance:n,ignoreLocation:u})<=g?V=P:F=P,P=Math.floor((F-V)/2+V);F=P;let Ct=Math.max(1,p-P+1),dt=o?d:Math.min(p+P,d)+h,Et=Array(dt+2);Et[dt+1]=(1<<H)-1;for(let K=dt;K>=Ct;K-=1){let z=K-1,Ht=e[r.charAt(z)];if(S&&(x[z]=+!!Ht),Et[K]=(Et[K+1]<<1|1)&Ht,H&&(Et[K]|=(_[K+1]|_[K])<<1|1|_[K+1]),Et[K]&D&&(w=Hs(t,{errors:H,currentLocation:z,expectedLocation:p,distance:n,ignoreLocation:u}),w<=g)){if(g=w,b=z,b<=p)break;Ct=Math.max(1,2*p-b)}}if(Hs(t,{errors:H+1,currentLocation:p,expectedLocation:p,distance:n,ignoreLocation:u})>g)break;_=Et}const W={isMatch:b>=0,score:Math.max(.001,w)};if(S){const H=function(V=[],P=U.minMatchCharLength){let Ct=[],dt=-1,Et=-1,K=0;for(let z=V.length;K<z;K+=1){let Ht=V[K];Ht&&dt===-1?dt=K:Ht||dt===-1||(Et=K-1,Et-dt+1>=P&&Ct.push([dt,Et]),dt=-1)}return V[K-1]&&K-dt>=P&&Ct.push([dt,K-1]),Ct}(x,a);H.length?c&&(W.indices=H):W.isMatch=!1}return W}function Rc(r){let t={};for(let e=0,s=r.length;e<s;e+=1){const n=r.charAt(e);t[n]=(t[n]||0)|1<<s-e-1}return t}class Qr{constructor(t,{location:e=U.location,threshold:s=U.threshold,distance:n=U.distance,includeMatches:i=U.includeMatches,findAllMatches:o=U.findAllMatches,minMatchCharLength:a=U.minMatchCharLength,isCaseSensitive:c=U.isCaseSensitive,ignoreLocation:u=U.ignoreLocation}={}){if(this.options={location:e,threshold:s,distance:n,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:c,ignoreLocation:u},this.pattern=c?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const h=(p,g)=>{this.chunks.push({pattern:p,alphabet:Rc(p),startIndex:g})},d=this.pattern.length;if(d>je){let p=0;const g=d%je,b=d-g;for(;p<b;)h(this.pattern.substr(p,je),p),p+=je;if(g){const S=d-je;h(this.pattern.substr(S),S)}}else h(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,includeMatches:s}=this.options;if(e||(t=t.toLowerCase()),this.pattern===t){let b={isMatch:!0,score:0};return s&&(b.indices=[[0,t.length-1]]),b}const{location:n,distance:i,threshold:o,findAllMatches:a,minMatchCharLength:c,ignoreLocation:u}=this.options;let h=[],d=0,p=!1;this.chunks.forEach(({pattern:b,alphabet:S,startIndex:x})=>{const{isMatch:y,score:_,indices:w}=Dc(t,b,S,{location:n+x,distance:i,threshold:o,findAllMatches:a,minMatchCharLength:c,includeMatches:s,ignoreLocation:u});y&&(p=!0),d+=_,y&&w&&(h=[...h,...w])});let g={isMatch:p,score:p?d/this.chunks.length:1};return p&&s&&(g.indices=h),g}}class xe{constructor(t){this.pattern=t}static isMultiMatch(t){return rr(t,this.multiRegex)}static isSingleMatch(t){return rr(t,this.singleRegex)}search(){}}function rr(r,t){const e=r.match(t);return e?e[1]:null}class Xr extends xe{constructor(t,{location:e=U.location,threshold:s=U.threshold,distance:n=U.distance,includeMatches:i=U.includeMatches,findAllMatches:o=U.findAllMatches,minMatchCharLength:a=U.minMatchCharLength,isCaseSensitive:c=U.isCaseSensitive,ignoreLocation:u=U.ignoreLocation}={}){super(t),this._bitapSearch=new Qr(t,{location:e,threshold:s,distance:n,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:c,ignoreLocation:u})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class Zr extends xe{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let e,s=0;const n=[],i=this.pattern.length;for(;(e=t.indexOf(this.pattern,s))>-1;)s=e+i,n.push([e,s-1]);const o=!!n.length;return{isMatch:o,score:o?0:1,indices:n}}}const Vn=[class extends xe{constructor(r){super(r)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(r){const t=r===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},Zr,class extends xe{constructor(r){super(r)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(r){const t=r.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends xe{constructor(r){super(r)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(r){const t=!r.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},class extends xe{constructor(r){super(r)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(r){const t=!r.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},class extends xe{constructor(r){super(r)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(r){const t=r.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[r.length-this.pattern.length,r.length-1]}}},class extends xe{constructor(r){super(r)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(r){const t=r.indexOf(this.pattern)===-1;return{isMatch:t,score:t?0:1,indices:[0,r.length-1]}}},Xr],or=Vn.length,Uc=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,qc=new Set([Xr.type,Zr.type]);class jc{constructor(t,{isCaseSensitive:e=U.isCaseSensitive,includeMatches:s=U.includeMatches,minMatchCharLength:n=U.minMatchCharLength,ignoreLocation:i=U.ignoreLocation,findAllMatches:o=U.findAllMatches,location:a=U.location,threshold:c=U.threshold,distance:u=U.distance}={}){this.query=null,this.options={isCaseSensitive:e,includeMatches:s,minMatchCharLength:n,findAllMatches:o,ignoreLocation:i,location:a,threshold:c,distance:u},this.pattern=e?t:t.toLowerCase(),this.query=function(h,d={}){return h.split("|").map(p=>{let g=p.trim().split(Uc).filter(S=>S&&!!S.trim()),b=[];for(let S=0,x=g.length;S<x;S+=1){const y=g[S];let _=!1,w=-1;for(;!_&&++w<or;){const F=Vn[w];let D=F.isMultiMatch(y);D&&(b.push(new F(D,d)),_=!0)}if(!_)for(w=-1;++w<or;){const F=Vn[w];let D=F.isSingleMatch(y);if(D){b.push(new F(D,d));break}}}return b})}(this.pattern,this.options)}static condition(t,e){return e.useExtendedSearch}searchIn(t){const e=this.query;if(!e)return{isMatch:!1,score:1};const{includeMatches:s,isCaseSensitive:n}=this.options;t=n?t:t.toLowerCase();let i=0,o=[],a=0;for(let c=0,u=e.length;c<u;c+=1){const h=e[c];o.length=0,i=0;for(let d=0,p=h.length;d<p;d+=1){const g=h[d],{isMatch:b,indices:S,score:x}=g.search(t);if(!b){a=0,i=0,o.length=0;break}if(i+=1,a+=x,s){const y=g.constructor.type;qc.has(y)?o=[...o,...S]:o.push(S)}}if(i){let d={isMatch:!0,score:a/i};return s&&(d.indices=o),d}}return{isMatch:!1,score:1}}}const Bn=[];function Kn(r,t){for(let e=0,s=Bn.length;e<s;e+=1){let n=Bn[e];if(n.condition(r,t))return new n(r,t)}return new Qr(r,t)}const ii="$and",Pc="$or",ar="$path",Hc="$val",Mn=r=>!(!r[ii]&&!r[Pc]),cr=r=>({[ii]:Object.keys(r).map(t=>({[t]:r[t]}))});function Jr(r,t,{auto:e=!0}={}){const s=n=>{let i=Object.keys(n);const o=(c=>!!c[ar])(n);if(!o&&i.length>1&&!Mn(n))return s(cr(n));if((c=>!pe(c)&&Wr(c)&&!Mn(c))(n)){const c=o?n[ar]:i[0],u=o?n[Hc]:n[c];if(!ie(u))throw new Error((d=>`Invalid value for key ${d}`)(c));const h={keyId:Wn(c),pattern:u};return e&&(h.searcher=Kn(u,t)),h}let a={children:[],operator:i[0]};return i.forEach(c=>{const u=n[c];pe(u)&&u.forEach(h=>{a.children.push(s(h))})}),a};return Mn(r)||(r=cr(r)),s(r)}function zc(r,t){const e=r.matches;t.matches=[],jt(e)&&e.forEach(s=>{if(!jt(s.indices)||!s.indices.length)return;const{indices:n,value:i}=s;let o={indices:n,value:i};s.key&&(o.key=s.key.src),s.idx>-1&&(o.refIndex=s.idx),t.matches.push(o)})}function Gc(r,t){t.score=r.score}class Je{constructor(t,e={},s){this.options={...U,...e},this.options.useExtendedSearch,this._keyStore=new Lc(this.options.keys),this.setCollection(t,s)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof ni))throw new Error("Incorrect 'index' type");this._myIndex=e||Kr(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){jt(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const e=[];for(let s=0,n=this._docs.length;s<n;s+=1){const i=this._docs[s];t(i,s)&&(this.removeAt(s),s-=1,n-=1,e.push(i))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:e=-1}={}){const{includeMatches:s,includeScore:n,shouldSort:i,sortFn:o,ignoreFieldNorm:a}=this.options;let c=ie(t)?ie(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(u,{ignoreFieldNorm:h=U.ignoreFieldNorm}){u.forEach(d=>{let p=1;d.matches.forEach(({key:g,norm:b,score:S})=>{const x=g?g.weight:null;p*=Math.pow(S===0&&x?Number.EPSILON:S,(x||1)*(h?1:b))}),d.score=p})}(c,{ignoreFieldNorm:a}),i&&c.sort(o),Gr(e)&&e>-1&&(c=c.slice(0,e)),function(u,h,{includeMatches:d=U.includeMatches,includeScore:p=U.includeScore}={}){const g=[];return d&&g.push(zc),p&&g.push(Gc),u.map(b=>{const{idx:S}=b,x={item:h[S],refIndex:S};return g.length&&g.forEach(y=>{y(b,x)}),x})}(c,this._docs,{includeMatches:s,includeScore:n})}_searchStringList(t){const e=Kn(t,this.options),{records:s}=this._myIndex,n=[];return s.forEach(({v:i,i:o,n:a})=>{if(!jt(i))return;const{isMatch:c,score:u,indices:h}=e.searchIn(i);c&&n.push({item:i,idx:o,matches:[{score:u,value:i,norm:a,indices:h}]})}),n}_searchLogical(t){const e=Jr(t,this.options),s=(a,c,u)=>{if(!a.children){const{keyId:d,searcher:p}=a,g=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(c,d),searcher:p});return g&&g.length?[{idx:u,item:c,matches:g}]:[]}const h=[];for(let d=0,p=a.children.length;d<p;d+=1){const g=a.children[d],b=s(g,c,u);if(b.length)h.push(...b);else if(a.operator===ii)return[]}return h},n=this._myIndex.records,i={},o=[];return n.forEach(({$:a,i:c})=>{if(jt(a)){let u=s(e,a,c);u.length&&(i[c]||(i[c]={idx:c,item:a,matches:[]},o.push(i[c])),u.forEach(({matches:h})=>{i[c].matches.push(...h)}))}}),o}_searchObjectList(t){const e=Kn(t,this.options),{keys:s,records:n}=this._myIndex,i=[];return n.forEach(({$:o,i:a})=>{if(!jt(o))return;let c=[];s.forEach((u,h)=>{c.push(...this._findMatches({key:u,value:o[h],searcher:e}))}),c.length&&i.push({idx:a,item:o,matches:c})}),i}_findMatches({key:t,value:e,searcher:s}){if(!jt(e))return[];let n=[];if(pe(e))e.forEach(({v:i,i:o,n:a})=>{if(!jt(i))return;const{isMatch:c,score:u,indices:h}=s.searchIn(i);c&&n.push({score:u,key:t,value:i,idx:o,norm:a,indices:h})});else{const{v:i,n:o}=e,{isMatch:a,score:c,indices:u}=s.searchIn(i);a&&n.push({score:c,key:t,value:i,norm:o,indices:u})}return n}}Je.version="7.0.0",Je.createIndex=Kr,Je.parseIndex=function(r,{getFn:t=U.getFn,fieldNormWeight:e=U.fieldNormWeight}={}){const{keys:s,records:n}=r,i=new ni({getFn:t,fieldNormWeight:e});return i.setKeys(s),i.setIndexRecords(n),i},Je.config=U,Je.parseQuery=Jr,function(...r){Bn.push(...r)}(jc);const Se=class Se{constructor(t,e){l(this,"_disposers",[]);l(this,"_allMentionables",ht([]));l(this,"_breadcrumbIds",ht([]));l(this,"_userQuery",ht(""));l(this,"_active",ht(!1));l(this,"_allGroups",gn([this._active,this._allMentionables],([t,e])=>t?Zo(e):[]));l(this,"_currentGroup",gn([this._breadcrumbIds,this._allGroups],([t,e])=>{if(t.length===0)return;const s=t[t.length-1];return e.find(n=>vs(n)&&n.id===s)}));l(this,"dispose",()=>{for(const t of this._disposers)t()});l(this,"openDropdown",()=>{this._active.set(!0)});l(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});l(this,"toggleDropdown",()=>ut(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));l(this,"pushBreadcrumb",t=>{ut(this._active)&&this._breadcrumbIds.update(e=>[...e,t.id])});l(this,"popBreadcrumb",()=>{ut(this._active)&&this._breadcrumbIds.update(t=>t.slice(0,-1))});l(this,"selectMentionable",t=>{var n;const e=this._chatModel.extensionClient,s=this._chatModel.specialContextInputModel;return vs(t)&&t.type==="breadcrumb"?(this.pushBreadcrumb(t),!0):t.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):Ir(t)?(s.markAllActive(),this.closeDropdown(),e.reportWebviewClientEvent(Fn.chatRestoreDefaultContext),!0):t.clearContext?(s.markAllInactive(),this.closeDropdown(),e.reportWebviewClientEvent(Fn.chatClearContext),!0):t.userGuidelines?(e.openSettingsPage("guidelines"),this.closeDropdown(),!0):((n=this._insertMentionNode)==null||n.call(this,t),this.closeDropdown(),!0)});l(this,"_displayItems",gn([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([t,e,s,n,i])=>{if(!t)return[];if(e.length>0&&n)return[{...n,type:"breadcrumb-back"},...n.group.items.slice(0,Se.SINGLE_GROUP_MAX_ITEMS).map(o=>({...o,type:"item"}))];if(s.length>0){const o=Wc(ut(this._userQuery)).map(a=>({...a,type:"item"}));return i.flatMap(a=>[{...a,type:"breadcrumb"},...a.group.items.slice(0,Se.MULTI_GROUP_MAX_ITEMS).map(c=>({...c,type:"item"}))]).concat(o)}return[{...Er,type:"item"},...i.map(o=>({...o,type:"breadcrumb"})),{...Tr,type:"item"},{...kr,type:"item"}]}));l(this,"_refreshSeqNum",0);l(this,"_refreshMentionables",Ec(async()=>{if(!ut(this._active))return;this._refreshSeqNum++;const t=this._refreshSeqNum,e=this._chatModel.currentConversationModel&&He(this._chatModel.currentConversationModel),s=ut(this._userQuery),n=await this._chatModel.extensionClient.getSuggestions(s,e);t===this._refreshSeqNum&&this._allMentionables.set(Yr({query:s,mentionables:n}))},Se.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=t,this._insertMentionNode=e,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};l(Se,"REFRESH_THROTTLE_MS",600),l(Se,"SINGLE_GROUP_MAX_ITEMS",12),l(Se,"MULTI_GROUP_MAX_ITEMS",6);let Qn=Se;const Yr=({query:r,mentionables:t,returnAllIfNoResults:e=!0,threshold:s=1})=>{if(r.length<=1)return t;const n=new Je(t,{keys:["label"],threshold:s,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(r);return n.length===0&&e?t:n.map(i=>i.item)},Wc=r=>Yr({query:r,mentionables:[Er,Tr,kr],returnAllIfNoResults:!1,threshold:.6});function en(r){switch(r){case he.DEFAULT:return ji;case he.PROTOTYPER:return ha;case he.BRAINSTORM:return ua;case he.REVIEWER:return la;default:return ji}}function Vc(r){let t,e,s,n=r[0].label+"";return{c(){t=tt("span"),e=tt("span"),s=ct(n),X(e,"class","c-mentionable-group-label__text right"),X(t,"class","c-mentionable-group-label")},m(i,o){L(i,t,o),et(t,e),et(e,s)},p(i,o){1&o&&n!==(n=i[0].label+"")&&Pt(s,n)},i:Z,o:Z,d(i){i&&O(t)}}}function Bc(r){let t,e;return t=new da({props:{$$slots:{text:[nl],leftIcon:[sl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};17&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Kc(r){let t,e=r[0].label+"";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=s[0].label+"")&&Pt(t,e)},i:Z,o:Z,d(s){s&&O(t)}}}function Qc(r){let t,e;return t=new Ot({props:{filepath:r[0].rule.path,$$slots:{leftIcon:[il]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].rule.path),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Xc(r){let t,e;return t=new Ot({props:{filepath:r[0].recentFile.pathName,$$slots:{leftIcon:[rl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].recentFile.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Zc(r){let t,e;return t=new Ot({props:{filepath:r[0].selection.pathName,$$slots:{leftIcon:[ol]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].selection.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Jc(r){let t,e;return t=new Ot({props:{filepath:r[0].sourceFolder.folderRoot,$$slots:{leftIcon:[al]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].sourceFolder.folderRoot),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Yc(r){let t,e;return t=new Ot({props:{filepath:r[0].externalSource.name,$$slots:{leftIcon:[cl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].externalSource.name),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function tl(r){let t,e;return t=new Ot({props:{filepath:r[0].folder.pathName,$$slots:{leftIcon:[ll]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].folder.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function el(r){let t,e;return t=new Ot({props:{filepath:r[0].file.pathName,$$slots:{leftIcon:[ul]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.filepath=s[0].file.pathName),16&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function sl(r){let t,e,s;var n=en(r[0].personality.type);return n&&(e=os(n,{})),{c(){t=tt("span"),e&&E(e.$$.fragment),X(t,"slot","leftIcon"),X(t,"class","c-context-menu-item__icon svelte-1a2w9oo")},m(i,o){L(i,t,o),e&&T(e,t,null),s=!0},p(i,o){if(1&o&&n!==(n=en(i[0].personality.type))){if(e){mt();const a=e;C(a.$$.fragment,1,0,()=>{k(a,1)}),_t()}n?(e=os(n,{}),E(e.$$.fragment),$(e.$$.fragment,1),T(e,t,null)):e=null}},i(i){s||(e&&$(e.$$.fragment,i),s=!0)},o(i){e&&C(e.$$.fragment,i),s=!1},d(i){i&&O(t),e&&k(e)}}}function nl(r){let t,e,s=r[0].label+"";return{c(){t=tt("span"),e=ct(s),X(t,"slot","text")},m(n,i){L(n,t,i),et(t,e)},p(n,i){1&i&&s!==(s=n[0].label+"")&&Pt(e,s)},d(n){n&&O(t)}}}function il(r){let t,e;return t=new Nr({props:{slot:"leftIcon",iconName:"rule"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function rl(r){let t,e;return t=new cs({props:{slot:"leftIcon",iconName:"description"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function ol(r){let t,e;return t=new cs({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function al(r){let t,e;return t=new cs({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function cl(r){let t,e;return t=new cs({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function ll(r){let t,e;return t=new cs({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function ul(r){let t,e;return t=new cs({props:{slot:"leftIcon",iconName:"description"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function hl(r){let t,e,s,n,i,o,a,c,u,h,d,p,g,b;const S=[el,tl,Yc,Jc,Zc,Xc,Qc,Kc,Bc,Vc],x=[];function y(_,w){return 1&w&&(t=null),1&w&&(e=null),1&w&&(s=null),1&w&&(n=null),1&w&&(i=null),1&w&&(o=null),1&w&&(a=null),1&w&&(c=null),1&w&&(u=null),1&w&&(h=null),t==null&&(t=!!Yn(_[0])),t?0:(e==null&&(e=!!ti(_[0])),e?1:(s==null&&(s=!!ei(_[0])),s?2:(n==null&&(n=!!Xs(_[0])),n?3:(i==null&&(i=!!Qs(_[0])),i?4:(o==null&&(o=!!Ks(_[0])),o?5:(a==null&&(a=!!Js(_[0])),a?6:(c==null&&(c=!!vs(_[0])),c?7:(u==null&&(u=!!sn(_[0])),u?8:(h==null&&(h=!!(Ir(_[0])||Jo(_[0])||Zs(_[0]))),h?9:-1)))))))))}return~(d=y(r,-1))&&(p=x[d]=S[d](r)),{c(){p&&p.c(),g=re()},m(_,w){~d&&x[d].m(_,w),L(_,g,w),b=!0},p(_,w){let F=d;d=y(_,w),d===F?~d&&x[d].p(_,w):(p&&(mt(),C(x[F],1,1,()=>{x[F]=null}),_t()),~d?(p=x[d],p?p.p(_,w):(p=x[d]=S[d](_),p.c()),$(p,1),p.m(g.parentNode,g)):p=null)},i(_){b||($(p),b=!0)},o(_){C(p),b=!1},d(_){_&&O(g),~d&&x[d].d(_)}}}function dl(r){let t,e,s;var n=r[3];function i(o,a){return{props:{highlight:o[2],onSelect:o[1],$$slots:{default:[hl]},$$scope:{ctx:o}}}}return n&&(t=os(n,i(r))),{c(){t&&E(t.$$.fragment),e=re()},m(o,a){t&&T(t,o,a),L(o,e,a),s=!0},p(o,[a]){if(8&a&&n!==(n=o[3])){if(t){mt();const c=t;C(c.$$.fragment,1,0,()=>{k(c,1)}),_t()}n?(t=os(n,i(o)),E(t.$$.fragment),$(t.$$.fragment,1),T(t,e.parentNode,e)):t=null}else if(n){const c={};4&a&&(c.highlight=o[2]),2&a&&(c.onSelect=o[1]),17&a&&(c.$$scope={dirty:a,ctx:o}),t.$set(c)}},i(o){s||(t&&$(t.$$.fragment,o),s=!0)},o(o){t&&C(t.$$.fragment,o),s=!1},d(o){o&&O(e),t&&k(t,o)}}}function fl(r,t,e){let s,{item:n}=t,{onSelect:i}=t,{highlight:o}=t;return r.$$set=a=>{"item"in a&&e(0,n=a.item),"onSelect"in a&&e(1,i=a.onSelect),"highlight"in a&&e(2,o=a.highlight)},r.$$.update=()=>{1&r.$$.dirty&&(n.type==="breadcrumb-back"?e(3,s=$n.BreadcrumbBackItem):n.type==="breadcrumb"&&vs(n)?e(3,s=$n.BreadcrumbItem):n.type!=="item"||vs(n)||e(3,s=$n.Item))},[n,i,o,s]}class pl extends Te{constructor(t){super(),ke(this,t,fl,dl,Fe,{item:0,onSelect:1,highlight:2})}}function gl(r){let t,e=r[0].label+"";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=s[0].label+"")&&Pt(t,e)},i:Z,o:Z,d(s){s&&O(t)}}}function ml(r){let t,e,s,n;return t=new Nr({}),s=new Ot({props:{filepath:`${Ui}/${qi}/${r[0].rule.path}`}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(i,o){T(t,i,o),L(i,e,o),T(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=`${Ui}/${qi}/${i[0].rule.path}`),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),C(s.$$.fragment,i),n=!1},d(i){i&&O(e),k(t,i),k(s,i)}}}function _l(r){let t,e,s,n,i=r[0].task.taskTree.description&&r[0].task.taskTree.description.trim();e=new as({props:{size:2,weight:"bold",$$slots:{default:[Ml]},$$scope:{ctx:r}}});let o=i&&lr(r);return{c(){t=tt("div"),E(e.$$.fragment),s=J(),o&&o.c(),X(t,"class","c-mention-hover-contents__task svelte-p7en3g")},m(a,c){L(a,t,c),T(e,t,null),et(t,s),o&&o.m(t,null),n=!0},p(a,c){const u={};3&c&&(u.$$scope={dirty:c,ctx:a}),e.$set(u),1&c&&(i=a[0].task.taskTree.description&&a[0].task.taskTree.description.trim()),i?o?(o.p(a,c),1&c&&$(o,1)):(o=lr(a),o.c(),$(o,1),o.m(t,null)):o&&(mt(),C(o,1,1,()=>{o=null}),_t())},i(a){n||($(e.$$.fragment,a),$(o),n=!0)},o(a){C(e.$$.fragment,a),C(o),n=!1},d(a){a&&O(t),k(e),o&&o.d()}}}function yl(r){let t,e,s,n,i,o,a,c;return e=new fa({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[El]},$$scope:{ctx:r}}}),i=new as({props:{size:2,weight:"medium",$$slots:{default:[Tl]},$$scope:{ctx:r}}}),a=new as({props:{size:1,$$slots:{default:[kl]},$$scope:{ctx:r}}}),{c(){t=tt("div"),E(e.$$.fragment),s=J(),n=tt("div"),E(i.$$.fragment),o=J(),E(a.$$.fragment),X(t,"class","c-mention-hover-contents__personality-icon svelte-p7en3g"),X(n,"class","c-mention-hover-contents__personality svelte-p7en3g")},m(u,h){L(u,t,h),T(e,t,null),L(u,s,h),L(u,n,h),T(i,n,null),et(n,o),T(a,n,null),c=!0},p(u,h){const d={};3&h&&(d.$$scope={dirty:h,ctx:u}),e.$set(d);const p={};3&h&&(p.$$scope={dirty:h,ctx:u}),i.$set(p);const g={};3&h&&(g.$$scope={dirty:h,ctx:u}),a.$set(g)},i(u){c||($(e.$$.fragment,u),$(i.$$.fragment,u),$(a.$$.fragment,u),c=!0)},o(u){C(e.$$.fragment,u),C(i.$$.fragment,u),C(a.$$.fragment,u),c=!1},d(u){u&&(O(t),O(s),O(n)),k(e),k(i),k(a)}}}function vl(r){var i,o;let t,e,s,n;return t=new pa({}),s=new Ot({props:{filepath:`${r[0].selection.pathName}:L${(i=r[0].selection.fullRange)==null?void 0:i.startLineNumber}-${(o=r[0].selection.fullRange)==null?void 0:o.endLineNumber}`}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(a,c){T(t,a,c),L(a,e,c),T(s,a,c),n=!0},p(a,c){var h,d;const u={};1&c&&(u.filepath=`${a[0].selection.pathName}:L${(h=a[0].selection.fullRange)==null?void 0:h.startLineNumber}-${(d=a[0].selection.fullRange)==null?void 0:d.endLineNumber}`),s.$set(u)},i(a){n||($(t.$$.fragment,a),$(s.$$.fragment,a),n=!0)},o(a){C(t.$$.fragment,a),C(s.$$.fragment,a),n=!1},d(a){a&&O(e),k(t,a),k(s,a)}}}function $l(r){var n;let t,e,s=(r[0].userGuidelines.overLimit||((n=r[0].rulesAndGuidelinesState)==null?void 0:n.overLimit))&&ur(r);return{c(){s&&s.c(),t=re()},m(i,o){s&&s.m(i,o),L(i,t,o),e=!0},p(i,o){var a;i[0].userGuidelines.overLimit||(a=i[0].rulesAndGuidelinesState)!=null&&a.overLimit?s?(s.p(i,o),1&o&&$(s,1)):(s=ur(i),s.c(),$(s,1),s.m(t.parentNode,t)):s&&(mt(),C(s,1,1,()=>{s=null}),_t())},i(i){e||($(s),e=!0)},o(i){C(s),e=!1},d(i){i&&O(t),s&&s.d(i)}}}function bl(r){let t,e,s,n,i,o,a,c;return s=new ga({}),i=new Ot({props:{class:"c-source-folder-item",filepath:r[0].sourceFolder.folderRoot}}),a=new ma({props:{class:"guidelines-filespan",sourceFolder:r[0].sourceFolder}}),{c(){t=tt("div"),e=tt("div"),E(s.$$.fragment),n=J(),E(i.$$.fragment),o=J(),E(a.$$.fragment),X(e,"class","l-source-folder-name svelte-p7en3g"),X(t,"class","l-mention-hover-contents__source-folder")},m(u,h){L(u,t,h),et(t,e),T(s,e,null),et(e,n),T(i,e,null),et(t,o),T(a,t,null),c=!0},p(u,h){const d={};1&h&&(d.filepath=u[0].sourceFolder.folderRoot),i.$set(d);const p={};1&h&&(p.sourceFolder=u[0].sourceFolder),a.$set(p)},i(u){c||($(s.$$.fragment,u),$(i.$$.fragment,u),$(a.$$.fragment,u),c=!0)},o(u){C(s.$$.fragment,u),C(i.$$.fragment,u),C(a.$$.fragment,u),c=!1},d(u){u&&O(t),k(s),k(i),k(a)}}}function Cl(r){let t,e,s,n;return t=new _a({}),s=new Ot({props:{filepath:r[0].externalSource.name}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(i,o){T(t,i,o),L(i,e,o),T(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].externalSource.name),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),C(s.$$.fragment,i),n=!1},d(i){i&&O(e),k(t,i),k(s,i)}}}function xl(r){let t,e,s,n;return t=new oa({}),s=new Ot({props:{filepath:r[0].folder.pathName}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(i,o){T(t,i,o),L(i,e,o),T(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].folder.pathName),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),C(s.$$.fragment,i),n=!1},d(i){i&&O(e),k(t,i),k(s,i)}}}function Sl(r){let t,e,s,n;return t=new Lr({}),s=new Ot({props:{filepath:r[0].recentFile.pathName}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(i,o){T(t,i,o),L(i,e,o),T(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].recentFile.pathName),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),C(s.$$.fragment,i),n=!1},d(i){i&&O(e),k(t,i),k(s,i)}}}function wl(r){let t,e,s,n;return t=new Lr({}),s=new Ot({props:{filepath:r[0].file.pathName}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(i,o){T(t,i,o),L(i,e,o),T(s,i,o),n=!0},p(i,o){const a={};1&o&&(a.filepath=i[0].file.pathName),s.$set(a)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),C(s.$$.fragment,i),n=!1},d(i){i&&O(e),k(t,i),k(s,i)}}}function Ml(r){let t,e=r[0].task.taskTree.name+"";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=s[0].task.taskTree.name+"")&&Pt(t,e)},d(s){s&&O(t)}}}function lr(r){let t,e;return t=new as({props:{size:1,$$slots:{default:[Il]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};3&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Il(r){let t,e=r[0].task.taskTree.description.trim().replace(/\s+/g," ")+"";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=s[0].task.taskTree.description.trim().replace(/\s+/g," ")+"")&&Pt(t,e)},d(s){s&&O(t)}}}function El(r){let t,e,s;var n=en(r[0].personality.type);return n&&(t=os(n,{})),{c(){t&&E(t.$$.fragment),e=re()},m(i,o){t&&T(t,i,o),L(i,e,o),s=!0},p(i,o){if(1&o&&n!==(n=en(i[0].personality.type))){if(t){mt();const a=t;C(a.$$.fragment,1,0,()=>{k(a,1)}),_t()}n?(t=os(n,{}),E(t.$$.fragment),$(t.$$.fragment,1),T(t,e.parentNode,e)):t=null}},i(i){s||(t&&$(t.$$.fragment,i),s=!0)},o(i){t&&C(t.$$.fragment,i),s=!1},d(i){i&&O(e),t&&k(t,i)}}}function Tl(r){let t,e=r[0].label+"";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=s[0].label+"")&&Pt(t,e)},d(s){s&&O(t)}}}function kl(r){let t,e=r[0].personality.description+"";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=s[0].personality.description+"")&&Pt(t,e)},d(s){s&&O(t)}}}function ur(r){let t,e,s,n;const i=[Al,Fl],o=[];function a(c,u){var h;return(h=c[0].rulesAndGuidelinesState)!=null&&h.overLimit?0:c[0].userGuidelines.overLimit?1:-1}return~(t=a(r))&&(e=o[t]=i[t](r)),{c(){e&&e.c(),s=re()},m(c,u){~t&&o[t].m(c,u),L(c,s,u),n=!0},p(c,u){let h=t;t=a(c),t===h?~t&&o[t].p(c,u):(e&&(mt(),C(o[h],1,1,()=>{o[h]=null}),_t()),~t?(e=o[t],e?e.p(c,u):(e=o[t]=i[t](c),e.c()),$(e,1),e.m(s.parentNode,s)):e=null)},i(c){n||($(e),n=!0)},o(c){C(e),n=!1},d(c){c&&O(s),~t&&o[t].d(c)}}}function Fl(r){let t,e;return t=new as({props:{size:1,$$slots:{default:[Ol]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};3&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Al(r){let t,e;return t=new as({props:{size:1,$$slots:{default:[Ll]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};3&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Ol(r){let t,e=`Guidelines exceeded length limit of ${r[0].userGuidelines.lengthLimit} characters`;return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=`Guidelines exceeded length limit of ${s[0].userGuidelines.lengthLimit} characters`)&&Pt(t,e)},d(s){s&&O(t)}}}function Ll(r){let t,e=`Rules and workspace guidelines (${r[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${r[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`;return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){1&n&&e!==(e=`Rules and workspace guidelines (${s[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${s[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`)&&Pt(t,e)},d(s){s&&O(t)}}}function Nl(r){let t,e,s,n,i,o,a,c,u,h,d,p,g,b;const S=[wl,Sl,xl,Cl,bl,$l,vl,yl,_l,ml,gl],x=[];function y(_,w){return 1&w&&(e=null),1&w&&(s=null),1&w&&(n=null),1&w&&(i=null),1&w&&(o=null),1&w&&(a=null),1&w&&(c=null),1&w&&(u=null),1&w&&(h=null),1&w&&(d=null),e==null&&(e=!(!_[0]||!Yn(_[0]))),e?0:(s==null&&(s=!(!_[0]||!Ks(_[0]))),s?1:(n==null&&(n=!(!_[0]||!ti(_[0]))),n?2:(i==null&&(i=!(!_[0]||!ei(_[0]))),i?3:(o==null&&(o=!(!_[0]||!Xs(_[0]))),o?4:(a==null&&(a=!(!_[0]||!Zs(_[0]))),a?5:(c==null&&(c=!(!_[0]||!Qs(_[0]))),c?6:(u==null&&(u=!(!_[0]||!sn(_[0]))),u?7:(h==null&&(h=!(!_[0]||!Mr(_[0]))),h?8:(d==null&&(d=!(!_[0]||!Js(_[0]))),d?9:10)))))))))}return p=y(r,-1),g=x[p]=S[p](r),{c(){t=tt("div"),g.c(),X(t,"class","c-mention-hover-contents svelte-p7en3g")},m(_,w){L(_,t,w),x[p].m(t,null),b=!0},p(_,[w]){let F=p;p=y(_,w),p===F?x[p].p(_,w):(mt(),C(x[F],1,1,()=>{x[F]=null}),_t(),g=x[p],g?g.p(_,w):(g=x[p]=S[p](_),g.c()),$(g,1),g.m(t,null))},i(_){b||($(g),b=!0)},o(_){C(g),b=!1},d(_){_&&O(t),x[p].d()}}}function Dl(r,t,e){let{option:s}=t;return r.$$set=n=>{"option"in n&&e(0,s=n.option)},[s]}class Rl extends Te{constructor(t){super(),ke(this,t,Dl,Nl,Fe,{option:0})}}function hr(r,t,e){const s=r.slice();return s[15]=t[e],s}function dr(r){let t,e;function s(){return r[8](r[15])}return t=new pl({props:{item:r[15],highlight:r[15]===r[14],onSelect:s}}),{c(){E(t.$$.fragment)},m(n,i){T(t,n,i),e=!0},p(n,i){r=n;const o={};4&i&&(o.item=r[15]),16388&i&&(o.highlight=r[15]===r[14]),4&i&&(o.onSelect=s),t.$set(o)},i(n){e||($(t.$$.fragment,n),e=!0)},o(n){C(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function Ul(r){let t,e,s=Bs(r[2]),n=[];for(let o=0;o<s.length;o+=1)n[o]=dr(hr(r,s,o));const i=o=>C(n[o],1,1,()=>{n[o]=null});return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=re()},m(o,a){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(o,a);L(o,t,a),e=!0},p(o,a){if(16420&a){let c;for(s=Bs(o[2]),c=0;c<s.length;c+=1){const u=hr(o,s,c);n[c]?(n[c].p(u,a),$(n[c],1)):(n[c]=dr(u),n[c].c(),$(n[c],1),n[c].m(t.parentNode,t))}for(mt(),c=s.length;c<n.length;c+=1)i(c);_t()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)$(n[a]);e=!0}},o(o){n=n.filter(Boolean);for(let a=0;a<n.length;a+=1)C(n[a]);e=!1},d(o){o&&O(t),Sr(n,o)}}}function ql(r){let t,e;return t=new Rl({props:{slot:"mentionable",option:r[13]}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p(s,n){const i={};8192&n&&(i.option=s[13]),t.$set(i)},i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function jl(r){let t,e,s,n;return t=new An.Menu.Root({props:{mentionables:r[2],onQueryUpdate:r[4],onSelectMentionable:r[5],$$slots:{default:[Ul,({activeItem:i})=>({14:i}),({activeItem:i})=>i?16384:0]},$$scope:{ctx:r}}}),s=new An.ChipTooltip({props:{$$slots:{mentionable:[ql,({mentionable:i})=>({13:i}),({mentionable:i})=>i?8192:0]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment)},m(i,o){T(t,i,o),L(i,e,o),T(s,i,o),n=!0},p(i,o){const a={};4&o&&(a.mentionables=i[2]),278532&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a);const c={};270336&o&&(c.$$scope={dirty:o,ctx:i}),s.$set(c)},i(i){n||($(t.$$.fragment,i),$(s.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),C(s.$$.fragment,i),n=!1},d(i){i&&O(e),k(t,i),k(s,i)}}}function Pl(r){let t,e,s={triggerCharacter:"@",onMentionItemsUpdated:r[0],$$slots:{default:[jl]},$$scope:{ctx:r}};return t=new An.Root({props:s}),r[9](t),{c(){E(t.$$.fragment)},m(n,i){T(t,n,i),e=!0},p(n,[i]){const o={};1&i&&(o.onMentionItemsUpdated=n[0]),262148&i&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||($(t.$$.fragment,n),e=!0)},o(n){C(t.$$.fragment,n),e=!1},d(n){r[9](null),k(t,n)}}}function Hl(r,t,e){let s,{requestEditorFocus:n}=t,{onMentionItemsUpdated:i}=t;const o=No("chatModel");if(!o)throw new Error("ChatModel not found in context");const a=new Qn(o,h),c=a.displayItems;let u;function h(p){return!!u&&(u.insertMention(p),a.closeDropdown(),!0)}function d(p){const g=a.selectMentionable(p);return n(),g}return We(r,c,p=>e(2,s=p)),Jn(()=>{a.dispose()}),r.$$set=p=>{"requestEditorFocus"in p&&e(6,n=p.requestEditorFocus),"onMentionItemsUpdated"in p&&e(0,i=p.onMentionItemsUpdated)},[i,u,s,c,function(p){p===void 0?a.closeDropdown():(a.openDropdown(),a.userQuery.set(p))},d,n,p=>h(p),p=>d(p),function(p){ts[p?"unshift":"push"](()=>{u=p,e(1,u)})}]}class zl extends Te{constructor(t){super(),ke(this,t,Hl,Pl,Fe,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}function fr(r){let t,e,s,n,i,o,a,c,u,h,d,p,g,b,S={focusOnInit:!0,$$slots:{default:[Gl]},$$scope:{ctx:r}};return o=new Dr.Root({props:S}),r[25](o),u=new Ie({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[Wl]},$$scope:{ctx:r}}}),u.$on("click",function(){fe(r[0].disposeDiffViewPanel)&&r[0].disposeDiffViewPanel.apply(this,arguments)}),d=new Ie({props:{id:"send",size:1,variant:"solid",color:"accent",title:r[3]===Ee.instruction?"Instruct Augment":"Edit with Augment",disabled:!r[4].trim()||r[11],$$slots:{iconRight:[Bl],default:[Vl]},$$scope:{ctx:r}}}),d.$on("click",r[14]),{c(){t=tt("div"),e=J(),s=tt("div"),n=tt("div"),i=tt("div"),E(o.$$.fragment),a=J(),c=tt("div"),E(u.$$.fragment),h=J(),E(d.$$.fragment),X(i,"class","l-input-area__input svelte-1cxscce"),X(c,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),X(n,"class","instruction-drawer-panel__contents svelte-1cxscce"),X(n,"tabindex","0"),X(n,"role","button"),X(s,"class","instruction-drawer-panel svelte-1cxscce"),Ye(s,"top",r[5]+"px"),Ye(s,"height",r[6]+"px")},m(x,y){L(x,t,y),L(x,e,y),L(x,s,y),et(s,n),et(n,i),T(o,i,null),r[26](i),et(n,a),et(n,c),T(u,c,null),et(c,h),T(d,c,null),p=!0,g||(b=[Cr(r[15].call(null,t)),Me(n,"click",r[17]),Me(n,"keydown",r[27])],g=!0)},p(x,y){r=x;const _={};1296&y[0]|256&y[1]&&(_.$$scope={dirty:y,ctx:r}),o.$set(_);const w={};256&y[1]&&(w.$$scope={dirty:y,ctx:r}),u.$set(w);const F={};8&y[0]&&(F.title=r[3]===Ee.instruction?"Instruct Augment":"Edit with Augment"),2064&y[0]&&(F.disabled=!r[4].trim()||r[11]),8&y[0]|256&y[1]&&(F.$$scope={dirty:y,ctx:r}),d.$set(F),(!p||32&y[0])&&Ye(s,"top",r[5]+"px"),(!p||64&y[0])&&Ye(s,"height",r[6]+"px")},i(x){p||($(o.$$.fragment,x),$(u.$$.fragment,x),$(d.$$.fragment,x),p=!0)},o(x){C(o.$$.fragment,x),C(u.$$.fragment,x),C(d.$$.fragment,x),p=!1},d(x){x&&(O(t),O(e),O(s)),r[25](null),k(o),r[26](null),k(u),k(d),g=!1,Zn(b)}}}function Gl(r){let t,e,s,n,i,o,a,c;t=new ya({props:{shortcuts:{Enter:r[23]}}});let u={requestEditorFocus:r[16],onMentionItemsUpdated:r[18]};return s=new zl({props:u}),r[24](s),i=new Dr.Content({props:{content:r[4],onContentChanged:r[19]}}),a=new va({props:{placeholder:r[10]}}),{c(){E(t.$$.fragment),e=J(),E(s.$$.fragment),n=J(),E(i.$$.fragment),o=J(),E(a.$$.fragment)},m(h,d){T(t,h,d),L(h,e,d),T(s,h,d),L(h,n,d),T(i,h,d),L(h,o,d),T(a,h,d),c=!0},p(h,d){s.$set({});const p={};16&d[0]&&(p.content=h[4]),i.$set(p);const g={};1024&d[0]&&(g.placeholder=h[10]),a.$set(g)},i(h){c||($(t.$$.fragment,h),$(s.$$.fragment,h),$(i.$$.fragment,h),$(a.$$.fragment,h),c=!0)},o(h){C(t.$$.fragment,h),C(s.$$.fragment,h),C(i.$$.fragment,h),C(a.$$.fragment,h),c=!1},d(h){h&&(O(e),O(n),O(o)),k(t,h),r[24](null),k(s,h),k(i,h),k(a,h)}}}function Wl(r){let t,e,s;return e=new Ve({props:{keybinding:"esc"}}),{c(){t=ct(`Close
          `),E(e.$$.fragment)},m(n,i){L(n,t,i),T(e,n,i),s=!0},p:Z,i(n){s||($(e.$$.fragment,n),s=!0)},o(n){C(e.$$.fragment,n),s=!1},d(n){n&&O(t),k(e,n)}}}function Vl(r){let t,e=r[3]===Ee.instruction?"Instruct":"Edit";return{c(){t=ct(e)},m(s,n){L(s,t,n)},p(s,n){8&n[0]&&e!==(e=s[3]===Ee.instruction?"Instruct":"Edit")&&Pt(t,e)},d(s){s&&O(t)}}}function Bl(r){let t,e;return t=new pc({props:{slot:"iconRight"}}),{c(){E(t.$$.fragment)},m(s,n){T(t,s,n),e=!0},p:Z,i(s){e||($(t.$$.fragment,s),e=!0)},o(s){C(t.$$.fragment,s),e=!1},d(s){k(t,s)}}}function Kl(r){let t,e,s=r[2]&&fr(r);return{c(){s&&s.c(),t=re()},m(n,i){s&&s.m(n,i),L(n,t,i),e=!0},p(n,i){n[2]?s?(s.p(n,i),4&i[0]&&$(s,1)):(s=fr(n),s.c(),$(s,1),s.m(t.parentNode,t)):s&&(mt(),C(s,1,1,()=>{s=null}),_t())},i(n){e||($(s),e=!0)},o(n){C(s),e=!1},d(n){n&&O(t),s&&s.d(n)}}}function Ql(r,t,e){let s,n,i,o,a,c,u=Z,h=()=>(u(),u=de(p,q=>e(22,o=q)),p),d=Z;r.$$.on_destroy.push(()=>u()),r.$$.on_destroy.push(()=>d());let{diffViewModel:p}=t;h();let{initialConversation:g}=t,{initialFlags:b}=t;const S=$a.getContext().monaco,x={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},y=new Ar(_s);let _=new Hr;y.registerConsumer(_);let w=new si(y,_s,_,{initialConversation:g,initialFlags:b});const F=w.currentConversationModel;let D,W;y.registerConsumer(w),function(q){Do("chatModel",q)}(w);let H,V="";const P=p.mode;We(r,P,q=>e(3,a=q));const Ct=p.selectionLines;function dt(){const q=p.getModifiedEditor(),Oe=ut(S);if(!q||!Oe||(H==null||H.clear(),!i))return;const Be=i.start,ls=i.end,Cs={range:new Oe.Range(Be+1,1,ls+1,1),options:x};H||(H=q.createDecorationsCollection()),H.set([Cs])}function Et(){return!!(V!=null&&V.trim())&&(p.handleInstructionSubmit(V),!0)}We(r,Ct,q=>e(2,i=q)),wr(async()=>{await En(),Lt(),e(5,Ht=p.editorOffset)}),Jn(()=>{D==null||D.destroy(),H==null||H.clear()});let K,z,Ht=0,Ae=57;const Lt=()=>K==null?void 0:K.forceFocus();return r.$$set=q=>{"diffViewModel"in q&&h(e(0,p=q.diffViewModel)),"initialConversation"in q&&e(20,g=q.initialConversation),"initialFlags"in q&&e(21,b=q.initialFlags)},r.$$.update=()=>{if(8&r.$$.dirty[0]&&e(10,s=(a===Ee.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&r.$$.dirty[0]&&(e(9,n=o.isLoading),d(),d=de(n,q=>e(11,c=q))),6&r.$$.dirty[0]&&W){if(i==null)e(6,Ae=0);else{const q=W.scrollHeight;e(6,Ae=Math.min(40+q,108))}D==null||D.update({heightInPx:Ae}),dt()}},[p,W,i,a,V,Ht,Ae,K,z,n,s,c,P,Ct,Et,function(q){if(q){const Oe=i?i.start:1;D=p.renderInstructionsDrawerViewZone(q,{line:Oe,heightInPx:Ae,onDomNodeTop:Be=>{e(5,Ht=p.editorOffset+Be)},autoFocus:!0}),dt()}},()=>K==null?void 0:K.requestFocus(),Lt,q=>{F.saveDraftMentions(q.current)},function(q){e(4,V=q.rawText)},g,b,o,()=>Et(),function(q){ts[q?"unshift":"push"](()=>{z=q,e(8,z)})},function(q){ts[q?"unshift":"push"](()=>{K=q,e(7,K)})},function(q){ts[q?"unshift":"push"](()=>{W=q,e(1,W)})},q=>{q.key==="Enter"&&(Lt(),q.stopPropagation(),q.preventDefault())}]}class Xl extends Te{constructor(t){super(),ke(this,t,Ql,Kl,Fe,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:In}=Po;function pr(r,t,e){const s=r.slice();return s[17]=t[e],s[19]=e,s}function gr(r){let t,e,s,n,i;return e=new hc({props:{diffViewModel:r[3]}}),n=new Xl({props:{diffViewModel:r[3]}}),{c(){t=tt("div"),E(e.$$.fragment),s=J(),E(n.$$.fragment),X(t,"class","sticky-top svelte-453n6i")},m(o,a){L(o,t,a),T(e,t,null),L(o,s,a),T(n,o,a),i=!0},p(o,a){const c={};8&a&&(c.diffViewModel=o[3]),e.$set(c);const u={};8&a&&(u.diffViewModel=o[3]),n.$set(u)},i(o){i||($(e.$$.fragment,o),$(n.$$.fragment,o),i=!0)},o(o){C(e.$$.fragment,o),C(n.$$.fragment,o),i=!1},d(o){o&&(O(t),O(s)),k(e),k(n,o)}}}function mr(r){let t,e,s=Bs(r[4]),n=[];for(let o=0;o<s.length;o+=1)n[o]=yr(pr(r,s,o));const i=o=>C(n[o],1,1,()=>{n[o]=null});return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=re()},m(o,a){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(o,a);L(o,t,a),e=!0},p(o,a){if(280&a){let c;for(s=Bs(o[4]),c=0;c<s.length;c+=1){const u=pr(o,s,c);n[c]?(n[c].p(u,a),$(n[c],1)):(n[c]=yr(u),n[c].c(),$(n[c],1),n[c].m(t.parentNode,t))}for(mt(),c=s.length;c<n.length;c+=1)i(c);_t()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)$(n[a]);e=!0}},o(o){n=n.filter(Boolean);for(let a=0;a<n.length;a+=1)C(n[a]);e=!1},d(o){o&&O(t),Sr(n,o)}}}function _r(r){var i;let t,e;function s(){return r[14](r[17])}function n(){return r[15](r[17])}return t=new Ya({props:{isFocused:((i=r[3])==null?void 0:i.currFocusedChunkIdx)===r[19],onAccept:s,onReject:n,diffViewModel:r[3],leaf:r[17],align:"right",disableApply:r[8]}}),{c(){E(t.$$.fragment)},m(o,a){T(t,o,a),e=!0},p(o,a){var u;r=o;const c={};8&a&&(c.isFocused=((u=r[3])==null?void 0:u.currFocusedChunkIdx)===r[19]),24&a&&(c.onAccept=s),24&a&&(c.onReject=n),8&a&&(c.diffViewModel=r[3]),16&a&&(c.leaf=r[17]),256&a&&(c.disableApply=r[8]),t.$set(c)},i(o){e||($(t.$$.fragment,o),e=!0)},o(o){C(t.$$.fragment,o),e=!1},d(o){k(t,o)}}}function yr(r){let t,e,s=r[17].unitOfCodeWork.modifiedCode!==r[17].unitOfCodeWork.originalCode&&_r(r);return{c(){s&&s.c(),t=re()},m(n,i){s&&s.m(n,i),L(n,t,i),e=!0},p(n,i){n[17].unitOfCodeWork.modifiedCode!==n[17].unitOfCodeWork.originalCode?s?(s.p(n,i),16&i&&$(s,1)):(s=_r(n),s.c(),$(s,1),s.m(t.parentNode,t)):s&&(mt(),C(s,1,1,()=>{s=null}),_t())},i(n){e||($(s),e=!0)},o(n){C(s),e=!1},d(n){n&&O(t),s&&s.d(n)}}}function Zl(r){var u;let t,e,s,n,i,o,a=r[3]&&gr(r),c=r[3]&&((u=r[4])==null?void 0:u.length)&&!r[7]&&mr(r);return{c(){t=tt("div"),a&&a.c(),e=J(),s=tt("div"),n=tt("div"),i=J(),c&&c.c(),X(n,"class","editor svelte-453n6i"),X(s,"class","editor-container svelte-453n6i"),X(t,"class","diff-view-container svelte-453n6i")},m(h,d){L(h,t,d),a&&a.m(t,null),et(t,e),et(t,s),et(s,n),r[13](n),et(s,i),c&&c.m(s,null),o=!0},p(h,d){var p;h[3]?a?(a.p(h,d),8&d&&$(a,1)):(a=gr(h),a.c(),$(a,1),a.m(t,e)):a&&(mt(),C(a,1,1,()=>{a=null}),_t()),h[3]&&((p=h[4])!=null&&p.length)&&!h[7]?c?(c.p(h,d),152&d&&$(c,1)):(c=mr(h),c.c(),$(c,1),c.m(s,null)):c&&(mt(),C(c,1,1,()=>{c=null}),_t())},i(h){o||($(a),$(c),o=!0)},o(h){C(a),C(c),o=!1},d(h){h&&O(t),a&&a.d(),r[13](null),c&&c.d()}}}function Jl(r){let t,e,s,n;return t=new ba.Root({props:{$$slots:{default:[Zl]},$$scope:{ctx:r}}}),{c(){E(t.$$.fragment)},m(i,o){T(t,i,o),e=!0,s||(n=[Me(In,"message",function(){var a,c;fe((a=r[0])==null?void 0:a.handleMessageFromExtension)&&((c=r[0])==null||c.handleMessageFromExtension.apply(this,arguments))}),Me(In,"focus",r[11]),Me(In,"blur",r[12])],s=!0)},p(i,[o]){r=i;const a={};1048986&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(i){e||($(t.$$.fragment,i),e=!0)},o(i){C(t.$$.fragment,i),e=!1},d(i){k(t,i),s=!1,Zn(n)}}}function Yl(r,t,e){let s,n,i,o,a,c,u,h,d,p,g=Z,b=Z,S=Z;function x(_){const w=Uo.dark;return Ca((_==null?void 0:_.category)||w,_==null?void 0:_.intensity)??xa.get(w)}We(r,Ro,_=>e(10,a=_)),r.$$.on_destroy.push(()=>g()),r.$$.on_destroy.push(()=>b()),r.$$.on_destroy.push(()=>S()),wr(async()=>{e(9,p=await window.augmentDeps.monaco),p||console.error("Monaco not loaded. Diff view cannot be initialized.")}),Jn(()=>{h==null||h.dispose()});let y=!1;return r.$$.update=()=>{if(1539&r.$$.dirty&&p&&d&&!h&&(e(0,h=new Ba(d,x(a),p)),g(),g=de(h,_=>e(3,o=_))),1&r.$$.dirty&&(e(6,s=h==null?void 0:h.disableApply),S(),S=de(s,_=>e(8,u=_))),1&r.$$.dirty&&(e(5,n=h==null?void 0:h.disableResolution),b(),b=de(n,_=>e(7,c=_))),1025&r.$$.dirty){const _=a;h&&(h==null||h.updateTheme(x(_)))}8&r.$$.dirty&&e(4,i=o==null?void 0:o.leaves),5&r.$$.dirty&&(h==null||h.updateIsWebviewFocused(y))},[h,d,y,o,i,n,s,c,u,p,a,()=>e(2,y=!0),()=>e(2,y=!1),function(_){ts[_?"unshift":"push"](()=>{d=_,e(1,d)})},_=>o==null?void 0:o.acceptChunk(_),_=>o==null?void 0:o.rejectChunk(_)]}new class extends Te{constructor(r){super(),ke(this,r,Yl,Jl,Fe,{})}}({target:document.getElementById("app")});

import{S as I,i as L,s as H,D as _,E as T,c as j,e as k,F as B,u as D,t as F,h as E,G,a3 as b,aa as M,a as $,j as Q,a1 as g,a2 as u,ab as w,Q as h,ac as S,g as W,T as Y,W as p,a5 as q}from"./SpinnerAugment-CL9SZpf8.js";import"./IconButtonAugment-C4xMcLhX.js";import{B as A}from"./BaseTextInput-BAWt2_LS.js";function C(n){let e,t,l,i,r=[{spellcheck:"false"},{class:t=`c-text-area__input c-base-text-input__input ${n[8]}`},n[7]],o={};for(let s=0;s<r.length;s+=1)o=$(o,r[s]);return{c(){e=_("textarea"),g(e,o),u(e,"c-textarea--resize-none",n[5]==="none"),u(e,"c-textarea--resize-both",n[5]==="both"),u(e,"c-textarea--resize-horizontal",n[5]==="horizontal"),u(e,"c-textarea--resize-vertical",n[5]==="vertical"),u(e,"svelte-17sbhhs",!0)},m(s,f){k(s,e,f),e.autofocus&&e.focus(),n[19](e),w(e,n[1]),l||(i=[h(e,"input",n[20]),S(n[9].call(null,e)),h(e,"click",n[10]),h(e,"focus",n[11]),h(e,"keydown",n[12]),h(e,"change",n[13]),h(e,"input",n[14]),h(e,"keyup",n[15]),h(e,"blur",n[16]),h(e,"select",n[17]),h(e,"mouseup",n[18])],l=!0)},p(s,f){g(e,o=W(r,[{spellcheck:"false"},256&f&&t!==(t=`c-text-area__input c-base-text-input__input ${s[8]}`)&&{class:t},128&f&&s[7]])),2&f&&w(e,s[1]),u(e,"c-textarea--resize-none",s[5]==="none"),u(e,"c-textarea--resize-both",s[5]==="both"),u(e,"c-textarea--resize-horizontal",s[5]==="horizontal"),u(e,"c-textarea--resize-vertical",s[5]==="vertical"),u(e,"svelte-17sbhhs",!0)},d(s){s&&E(e),n[19](null),l=!1,Y(i)}}}function J(n){let e,t,l;return t=new A({props:{type:n[6],variant:n[2],size:n[3],color:n[4],$$slots:{default:[C]},$$scope:{ctx:n}}}),{c(){e=_("div"),T(t.$$.fragment),j(e,"class","c-text-area svelte-17sbhhs")},m(i,r){k(i,e,r),B(t,e,null),l=!0},p(i,[r]){const o={};64&r&&(o.type=i[6]),4&r&&(o.variant=i[2]),8&r&&(o.size=i[3]),16&r&&(o.color=i[4]),8389027&r&&(o.$$scope={dirty:r,ctx:i}),t.$set(o)},i(i){l||(D(t.$$.fragment,i),l=!0)},o(i){F(t.$$.fragment,i),l=!1},d(i){i&&E(e),G(t)}}}function K(n,e,t){let l,i;const r=["variant","size","color","resize","textInput","type","value"];let o=b(e,r),{variant:s="surface"}=e,{size:f=2}=e,{color:d}=e,{resize:m="none"}=e,{textInput:c}=e,{type:y="default"}=e,{value:z=""}=e;function v(){if(!c)return;t(0,c.style.height="auto",c);const a=.8*window.innerHeight,x=Math.min(c.scrollHeight,a);t(0,c.style.height=`${x}px`,c),t(0,c.style.overflowY=c.scrollHeight>a?"auto":"hidden",c)}return M(()=>{if(c){v();const a=()=>v();return window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}}}),n.$$set=a=>{e=$($({},e),Q(a)),t(22,o=b(e,r)),"variant"in a&&t(2,s=a.variant),"size"in a&&t(3,f=a.size),"color"in a&&t(4,d=a.color),"resize"in a&&t(5,m=a.resize),"textInput"in a&&t(0,c=a.textInput),"type"in a&&t(6,y=a.type),"value"in a&&t(1,z=a.value)},n.$$.update=()=>{t(8,{class:l,...i}=o,l,(t(7,i),t(22,o)))},[c,z,s,f,d,m,y,i,l,function(a){v();const x=()=>v();return a.addEventListener("input",x),setTimeout(v,0),{destroy(){a.removeEventListener("input",x)}}},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){p.call(this,n,a)},function(a){q[a?"unshift":"push"](()=>{c=a,t(0,c)})},function(){z=this.value,t(1,z)}]}class R extends I{constructor(e){super(),L(this,e,K,J,H,{variant:2,size:3,color:4,resize:5,textInput:0,type:6,value:1})}}export{R as T};

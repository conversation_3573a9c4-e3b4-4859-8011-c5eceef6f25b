var wf=Object.defineProperty;var xf=(s,r,i)=>r in s?wf(s,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[r]=i;var m=(s,r,i)=>xf(s,typeof r!="symbol"?r+"":r,i);import{g as Sf,h as bf,b as If,c as Tf,p as Ef,f as gt,i as Xo,F as Cf,j as Zo,k as oa,l as Jo,C as Mf,s as qf,m as Af,n as Cn,W as Rf,o as At,R as kf,q as ua,r as Qo,t as ca,u as tu,v as la,E as fa,w as da,x as Uf,y as Of}from"./index-BAWb-tvr.js";import{a as Ff}from"./async-messaging-CtwQrvzD.js";import{W as O}from"./IconButtonAugment-C4xMcLhX.js";import{P as Et,C as lt,a as Tt,b as ha,I as Mr,E as Lf}from"./message-broker-SEbJxN6J.js";import{C as Df}from"./types-CGlLNakm.js";import{n as Hf,f as Pf,i as jf}from"./file-paths-BPg3etNg.js";import{A as Nf,B as Mn,S as ou,i as uu,s as cu,a as an,b as lu,H as fu,w as du,x as hu,y as pu,h as Or,d as Fr,z as gu,g as _u,n as Lr,j as Dr}from"./SpinnerAugment-CL9SZpf8.js";var Re;function eu(s){const r=Re[s];return typeof r!="string"?s.toString():r[0].toLowerCase()+r.substring(1).replace(/[A-Z]/g,i=>"_"+i.toLowerCase())}(function(s){s[s.Canceled=1]="Canceled",s[s.Unknown=2]="Unknown",s[s.InvalidArgument=3]="InvalidArgument",s[s.DeadlineExceeded=4]="DeadlineExceeded",s[s.NotFound=5]="NotFound",s[s.AlreadyExists=6]="AlreadyExists",s[s.PermissionDenied=7]="PermissionDenied",s[s.ResourceExhausted=8]="ResourceExhausted",s[s.FailedPrecondition=9]="FailedPrecondition",s[s.Aborted=10]="Aborted",s[s.OutOfRange=11]="OutOfRange",s[s.Unimplemented=12]="Unimplemented",s[s.Internal=13]="Internal",s[s.Unavailable=14]="Unavailable",s[s.DataLoss=15]="DataLoss",s[s.Unauthenticated=16]="Unauthenticated"})(Re||(Re={}));class Ae extends Error{constructor(r,i=Re.Unknown,c,f,h){super(function(g,T){return g.length?`[${eu(T)}] ${g}`:`[${eu(T)}]`}(r,i)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=r,this.code=i,this.metadata=new Headers(c??{}),this.details=f??[],this.cause=h}static from(r,i=Re.Unknown){return r instanceof Ae?r:r instanceof Error?r.name=="AbortError"?new Ae(r.message,Re.Canceled):new Ae(r.message,i,void 0,void 0,r):new Ae(String(r),i,void 0,void 0,r)}static[Symbol.hasInstance](r){return r instanceof Error&&(Object.getPrototypeOf(r)===Ae.prototype||r.name==="ConnectError"&&"code"in r&&typeof r.code=="number"&&"metadata"in r&&"details"in r&&Array.isArray(r.details)&&"rawMessage"in r&&typeof r.rawMessage=="string"&&"cause"in r)}findDetails(r){const i=r.kind==="message"?{getMessage:f=>f===r.typeName?r:void 0}:r,c=[];for(const f of this.details){if("desc"in f){i.getMessage(f.desc.typeName)&&c.push(Sf(f.desc,f.value));continue}const h=i.getMessage(f.type);if(h)try{c.push(bf(h,f.value))}catch{}}return c}}var Bf=function(s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=s[Symbol.asyncIterator];return i?i.call(s):(s=typeof __values=="function"?__values(s):s[Symbol.iterator](),r={},c("next"),c("throw"),c("return"),r[Symbol.asyncIterator]=function(){return this},r);function c(f){r[f]=s[f]&&function(h){return new Promise(function(g,T){(function(C,L,G,M){Promise.resolve(M).then(function(k){C({value:k,done:G})},L)})(g,T,(h=s[f](h)).done,h.value)})}}},Rn=function(s){return this instanceof Rn?(this.v=s,this):new Rn(s)},zf=function(s,r,i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var c,f=i.apply(s,r||[]),h=[];return c=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),g("next"),g("throw"),g("return",function(M){return function(k){return Promise.resolve(k).then(M,L)}}),c[Symbol.asyncIterator]=function(){return this},c;function g(M,k){f[M]&&(c[M]=function(N){return new Promise(function(K,ft){h.push([M,N,K,ft])>1||T(M,N)})},k&&(c[M]=k(c[M])))}function T(M,k){try{(N=f[M](k)).value instanceof Rn?Promise.resolve(N.value.v).then(C,L):G(h[0][2],N)}catch(K){G(h[0][3],K)}var N}function C(M){T("next",M)}function L(M){T("throw",M)}function G(M,k){M(k),h.shift(),h.length&&T(h[0][0],h[0][1])}},Wf=function(s){var r,i;return r={},c("next"),c("throw",function(f){throw f}),c("return"),r[Symbol.iterator]=function(){return this},r;function c(f,h){r[f]=s[f]?function(g){return(i=!i)?{value:Rn(s[f](g)),done:!1}:h?h(g):g}:h}},yu=function(s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=s[Symbol.asyncIterator];return i?i.call(s):(s=typeof __values=="function"?__values(s):s[Symbol.iterator](),r={},c("next"),c("throw"),c("return"),r[Symbol.asyncIterator]=function(){return this},r);function c(f){r[f]=s[f]&&function(h){return new Promise(function(g,T){(function(C,L,G,M){Promise.resolve(M).then(function(k){C({value:k,done:G})},L)})(g,T,(h=s[f](h)).done,h.value)})}}},on=function(s){return this instanceof on?(this.v=s,this):new on(s)},$f=function(s){var r,i;return r={},c("next"),c("throw",function(f){throw f}),c("return"),r[Symbol.iterator]=function(){return this},r;function c(f,h){r[f]=s[f]?function(g){return(i=!i)?{value:on(s[f](g)),done:!1}:h?h(g):g}:h}},Gf=function(s,r,i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var c,f=i.apply(s,r||[]),h=[];return c=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),g("next"),g("throw"),g("return",function(M){return function(k){return Promise.resolve(k).then(M,L)}}),c[Symbol.asyncIterator]=function(){return this},c;function g(M,k){f[M]&&(c[M]=function(N){return new Promise(function(K,ft){h.push([M,N,K,ft])>1||T(M,N)})},k&&(c[M]=k(c[M])))}function T(M,k){try{(N=f[M](k)).value instanceof on?Promise.resolve(N.value.v).then(C,L):G(h[0][2],N)}catch(K){G(h[0][3],K)}var N}function C(M){T("next",M)}function L(M){T("throw",M)}function G(M,k){M(k),h.shift(),h.length&&T(h[0][0],h[0][1])}};function Vf(s,r){return function(i,c){const f={};for(const h of i.methods){const g=c(h);g!=null&&(f[h.localName]=g)}return f}(s,i=>{switch(i.methodKind){case"unary":return function(c,f){return async function(h,g){var T,C;const L=await c.unary(f,g==null?void 0:g.signal,g==null?void 0:g.timeoutMs,g==null?void 0:g.headers,h,g==null?void 0:g.contextValues);return(T=g==null?void 0:g.onHeader)===null||T===void 0||T.call(g,L.header),(C=g==null?void 0:g.onTrailer)===null||C===void 0||C.call(g,L.trailer),L.message}}(r,i);case"server_streaming":return function(c,f){return function(h,g){return nu(c.stream(f,g==null?void 0:g.signal,g==null?void 0:g.timeoutMs,g==null?void 0:g.headers,function(T){return zf(this,arguments,function*(){yield Rn(yield*Wf(Bf(T)))})}([h]),g==null?void 0:g.contextValues),g)}}(r,i);case"client_streaming":return function(c,f){return async function(h,g){var T,C,L,G,M,k;const N=await c.stream(f,g==null?void 0:g.signal,g==null?void 0:g.timeoutMs,g==null?void 0:g.headers,h,g==null?void 0:g.contextValues);let K;(M=g==null?void 0:g.onHeader)===null||M===void 0||M.call(g,N.header);let ft=0;try{for(var Z,_t=!0,jt=yu(N.message);!(T=(Z=await jt.next()).done);_t=!0)G=Z.value,_t=!1,K=G,ft++}catch(te){C={error:te}}finally{try{_t||T||!(L=jt.return)||await L.call(jt)}finally{if(C)throw C.error}}if(!K)throw new Ae("protocol error: missing response message",Re.Unimplemented);if(ft>1)throw new Ae("protocol error: received extra messages for client streaming method",Re.Unimplemented);return(k=g==null?void 0:g.onTrailer)===null||k===void 0||k.call(g,N.trailer),K}}(r,i);case"bidi_streaming":return function(c,f){return function(h,g){return nu(c.stream(f,g==null?void 0:g.signal,g==null?void 0:g.timeoutMs,g==null?void 0:g.headers,h,g==null?void 0:g.contextValues),g)}}(r,i);default:return null}})}function nu(s,r){const i=function(){return Gf(this,arguments,function*(){var c,f;const h=yield on(s);(c=r==null?void 0:r.onHeader)===null||c===void 0||c.call(r,h.header),yield on(yield*$f(yu(h.message))),(f=r==null?void 0:r.onTrailer)===null||f===void 0||f.call(r,h.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>i.next()})}}function Yf(s,r,i=1e3){let c=null,f=0;const h=Nf(r),g=()=>{const T=(()=>{const C=Date.now();if(c!==null&&C-f<i)return c;const L=s();return c=L,f=C,L})();h.set(T)};return{subscribe:h.subscribe,resetCache:()=>{c=null,g()},updateStore:g}}var mu=(s=>(s[s.unset=0]="unset",s[s.positive=1]="positive",s[s.negative=2]="negative",s))(mu||{});function Ft(s,r){return r in s&&s[r]!==void 0}function Kf(s){return Ft(s,"file")}function Xf(s){return Ft(s,"recentFile")}function Zf(s){return Ft(s,"folder")}function Jf(s){return Ft(s,"sourceFolder")}function kd(s){return Ft(s,"sourceFolderGroup")}function Ud(s){return Ft(s,"selection")}function Qf(s){return Ft(s,"externalSource")}function Od(s){return Ft(s,"allDefaultContext")}function Fd(s){return Ft(s,"clearContext")}function Ld(s){return Ft(s,"userGuidelines")}function Dd(s){return Ft(s,"agentMemories")}function vu(s){return Ft(s,"personality")}function td(s){return Ft(s,"rule")}function ed(s){return Ft(s,"task")}const Hd={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Pd={clearContext:!0,label:"Clear Context",id:"clearContext"},jd={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Nd={agentMemories:{},label:"Agent Memories",id:"agentMemories"},ru=[{personality:{type:Et.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:Et.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:Et.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:Et.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Bd(s){return Ft(s,"group")}function zd(s){const r=new Map;return s.forEach(i=>{Kf(i)?r.set("file",[...r.get("file")??[],i]):Xf(i)?r.set("recentFile",[...r.get("recentFile")??[],i]):Zf(i)?r.set("folder",[...r.get("folder")??[],i]):Qf(i)?r.set("externalSource",[...r.get("externalSource")??[],i]):Jf(i)?r.set("sourceFolder",[...r.get("sourceFolder")??[],i]):vu(i)?r.set("personality",[...r.get("personality")??[],i]):td(i)&&r.set("rule",[...r.get("rule")??[],i])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:r.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:r.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:r.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:r.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:r.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:r.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:r.get("rule")??[]}}].filter(i=>i.group.items.length>0)}function nd(s){const r=Ef({rootPath:s.repoRoot,relPath:s.pathName}),i={label:Hf(s.pathName).split("/").filter(c=>c.trim()!=="").pop()||"",name:r,id:r};if(s.fullRange){const c=`:L${s.fullRange.startLineNumber}-${s.fullRange.endLineNumber}`;i.label+=c,i.name+=c,i.id+=c}else if(s.range){const c=`:L${s.range.start}-${s.range.stop}`;i.label+=c,i.name+=c,i.id+=c}return i}function rd(s){const r=s.path.split("/"),i=r[r.length-1],c=i.endsWith(".md")?i.slice(0,-3):i,f=`${If}/${Tf}/${s.path}`;return{label:c,name:f,id:f}}function sd(s){var i;if(!s)return Mr.IMAGE_FORMAT_UNSPECIFIED;switch((i=s.split("/")[1])==null?void 0:i.toLowerCase()){case"jpeg":case"jpg":return Mr.JPEG;case"png":return Mr.PNG;default:return Mr.IMAGE_FORMAT_UNSPECIFIED}}function ad(s,r,i){var f,h;if(s.phase!==gt.cancelled&&s.phase!==gt.completed&&s.phase!==gt.error)return;let c;return(f=s.result)!=null&&f.contentNodes?(c=function(g,T){return g.map(C=>C.type===Xo.ContentText?{type:ha.CONTENT_TEXT,text_content:C.text_content}:C.type===Xo.ContentImage&&C.image_content&&T?{type:ha.CONTENT_IMAGE,image_content:{image_data:C.image_content.image_data,format:sd(C.image_content.media_type)}}:{type:ha.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(s.result.contentNodes,i),{content:"",is_error:s.result.isError,request_id:s.result.requestId,tool_use_id:r,content_nodes:c}):((h=s.result)==null?void 0:h.text)!==void 0?{content:s.result.text,is_error:s.result.isError,request_id:s.result.requestId,tool_use_id:r}:void 0}function id(s=[]){let r;for(const i of s){if(i.type===lt.TOOL_USE)return i;i.type===lt.TOOL_USE_START&&(r=i)}return r}const od="__NEW_AGENT__",Wd=s=>s.chatItemType===void 0,$d=s=>{var c;const r=s.chatHistory.at(-1);if(!r||!St(r))return Be.notRunning;if(!(r.status===tt.success||r.status===tt.failed||r.status===tt.cancelled))return Be.running;const i=(((c=r.structured_output_nodes)==null?void 0:c.filter(f=>f.type===lt.TOOL_USE&&!!f.tool_use))??[]).at(-1);if(!i)return Be.notRunning;switch(s.getToolUseState(r.request_id,i.tool_use.tool_use_id).phase){case gt.runnable:return Be.awaitingUserAction;case gt.cancelled:return Be.notRunning;default:return Be.running}},_a=s=>St(s)&&!!s.request_message,ud=s=>s.chatHistory.findLast(r=>_a(r)),Gd=(s,r)=>{const i=ud(s);return i!=null&&i.request_id?s.historyFrom(i.request_id,!0).filter(c=>St(c)&&(!r||r(c))):[]},Vd=s=>{var c;const r=s.chatHistory.at(-1);if(!(r!=null&&r.request_id)||!St(r))return!1;const i=((c=r.structured_output_nodes)==null?void 0:c.filter(f=>f.type===lt.TOOL_USE))??[];for(const f of i)if(f.tool_use&&s.getToolUseState(r.request_id,f.tool_use.tool_use_id).phase===gt.runnable)return s.updateToolUseState({requestId:r.request_id,toolUseId:f.tool_use.tool_use_id,phase:gt.cancelled}),!0;return!1};function cd(s,r){const i=s.customPersonalityPrompts;if(i)switch(r){case Et.DEFAULT:if(i.agent&&i.agent.trim()!=="")return i.agent;break;case Et.PROTOTYPER:if(i.prototyper&&i.prototyper.trim()!=="")return i.prototyper;break;case Et.BRAINSTORM:if(i.brainstorm&&i.brainstorm.trim()!=="")return i.brainstorm;break;case Et.REVIEWER:if(i.reviewer&&i.reviewer.trim()!=="")return i.reviewer}return ld[r]}const ld={[Et.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Et.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Et.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Et.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};function qr(s){var r;return((r=s.extraData)==null?void 0:r.isAgentConversation)===!0}var fd=(s=>(s[s.active=0]="active",s[s.inactive=1]="inactive",s))(fd||{});const Ur="temp-fe";class $t{constructor(r,i,c,f){m(this,"_state");m(this,"_subscribers",new Set);m(this,"_focusModel",new Cf);m(this,"_onSendExchangeListeners",[]);m(this,"_onNewConversationListeners",[]);m(this,"_onHistoryDeleteListeners",[]);m(this,"_onBeforeChangeConversationListeners",[]);m(this,"_totalCharactersCacheThrottleMs",1e3);m(this,"_totalCharactersStore");m(this,"subscribe",r=>(this._subscribers.add(r),r(this),()=>{this._subscribers.delete(r)}));m(this,"setConversation",(r,i=!0,c=!0)=>{const f=r.id!==this._state.id;f&&c&&(r.toolUseStates=Object.fromEntries(Object.entries(r.toolUseStates??{}).map(([g,T])=>{if(T.requestId&&T.toolUseId){const{requestId:C,toolUseId:L}=Zo(g);return C===T.requestId&&L===T.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",g,"but object has ",oa(T)),[g,T]}return[g,{...T,...Zo(g)}]})),(r=this._notifyBeforeChangeConversation(this._state,r)).lastInteractedAtIso=new Date().toISOString()),i&&f&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const h=$t.isEmpty(r);if(f&&h){const g=this._state.draftExchange;g&&(r.draftExchange=g)}return this._state=r,this._focusModel.setItems(this._state.chatHistory.filter(St)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(g=>g(this)),this._saveConversation(this._state),f&&(this._loadContextFromConversation(r),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(g=>g())),!0});m(this,"update",r=>{this.setConversation({...this._state,...r}),this._totalCharactersStore.updateStore()});m(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});m(this,"setName",r=>{this.update({name:r})});m(this,"setSelectedModelId",r=>{this.update({selectedModelId:r})});m(this,"updateFeedback",(r,i)=>{this.update({feedbackStates:{...this._state.feedbackStates,[r]:i}})});m(this,"updateToolUseState",r=>{this.update({toolUseStates:{...this._state.toolUseStates,[oa(r)]:r}})});m(this,"getToolUseState",(r,i)=>r===void 0||i===void 0||this.toolUseStates===void 0?{phase:gt.unknown,requestId:r??"",toolUseId:i??""}:this.toolUseStates[oa({requestId:r,toolUseId:i})]||{phase:gt.new});m(this,"getLastToolUseId",()=>{var c,f;const r=this.lastExchange;if(!r)return;const i=(((c=r==null?void 0:r.structured_output_nodes)==null?void 0:c.filter(h=>h.type===lt.TOOL_USE))??[]).at(-1);return i?(f=i.tool_use)==null?void 0:f.tool_use_id:void 0});m(this,"getLastToolUseState",()=>{var c;const r=this.lastExchange;if(!r)return{phase:gt.unknown};const i=function(f=[]){let h;for(const g of f){if(g.type===lt.TOOL_USE)return g;g.type===lt.TOOL_USE_START&&(h=g)}return h}(r==null?void 0:r.structured_output_nodes);return i?this.getToolUseState(r.request_id,(c=i.tool_use)==null?void 0:c.tool_use_id):{phase:gt.unknown}});m(this,"addExchange",(r,i)=>{const c=this._state.chatHistory;let f,h;f=i===void 0?[...c,r]:i===-1?c.length===0?[r]:[...c.slice(0,-1),r,c[c.length-1]]:[...c.slice(0,i),r,...c.slice(i)],St(r)&&(h=r.request_id?{...this._state.feedbackStates,[r.request_id]:{selectedRating:mu.unset,feedbackNote:""}}:void 0),this.update({chatHistory:f,...h?{feedbackStates:h}:{},lastUrl:void 0})});m(this,"addExchangeBeforeLast",r=>{this.addExchange(r,-1)});m(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});m(this,"updateExchangeById",(r,i,c=!1)=>{var T;const f=this.exchangeWithRequestId(i);if(f===null)return console.warn("No exchange with this request ID found."),!1;c&&r.response_text!==void 0&&(r.response_text=(f.response_text??"")+(r.response_text??"")),c&&(r.structured_output_nodes=function(C=[]){const L=id(C);return L&&L.type===lt.TOOL_USE?C.filter(G=>G.type!==lt.TOOL_USE_START):C}([...f.structured_output_nodes??[],...r.structured_output_nodes??[]])),r.stop_reason!==f.stop_reason&&f.stop_reason&&r.stop_reason===Df.REASON_UNSPECIFIED&&(r.stop_reason=f.stop_reason),c&&r.workspace_file_chunks!==void 0&&(r.workspace_file_chunks=[...f.workspace_file_chunks??[],...r.workspace_file_chunks??[]]);const h=(T=(r.structured_output_nodes||[]).find(C=>C.type===lt.MAIN_TEXT_FINISHED))==null?void 0:T.content;h&&h!==r.response_text&&(r.response_text=h);let g=this._state.isShareable||pa({...f,...r});return this.update({chatHistory:this.chatHistory.map(C=>C.request_id===i?{...C,...r}:C),isShareable:g}),!0});m(this,"clearMessagesFromHistory",r=>{const i=this._collectToolUseIdsFromMessages(this.chatHistory.filter(c=>c.request_id&&r.has(c.request_id)));this.update({chatHistory:this.chatHistory.filter(c=>!c.request_id||!r.has(c.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(r),toolUseIds:i})});m(this,"clearHistory",()=>{const r=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:r}),this.update({chatHistory:[]})});m(this,"clearHistoryFrom",async(r,i=!0)=>{const c=this.historyFrom(r,i),f=c.map(g=>g.request_id).filter(g=>g!==void 0),h=this._collectToolUseIdsFromMessages(c);this.update({chatHistory:this.historyTo(r,!i)}),this._extensionClient.clearMetadataFor({requestIds:f,toolUseIds:h}),c.forEach(g=>{this._onHistoryDeleteListeners.forEach(T=>T(g))})});m(this,"clearMessageFromHistory",r=>{const i=this.chatHistory.find(f=>f.request_id===r),c=i?this._collectToolUseIdsFromMessages([i]):[];this.update({chatHistory:this.chatHistory.filter(f=>f.request_id!==r)}),this._extensionClient.clearMetadataFor({requestIds:[r],toolUseIds:c})});m(this,"_collectToolUseIdsFromMessages",r=>{var c;const i=[];for(const f of r)if(St(f)&&f.structured_output_nodes)for(const h of f.structured_output_nodes)h.type===lt.TOOL_USE&&((c=h.tool_use)!=null&&c.tool_use_id)&&i.push(h.tool_use.tool_use_id);return i});m(this,"historyTo",(r,i=!1)=>{const c=this.chatHistory.findIndex(f=>f.request_id===r);return c===-1?[]:this.chatHistory.slice(0,i?c+1:c)});m(this,"historyFrom",(r,i=!0)=>{const c=this.chatHistory.findIndex(f=>f.request_id===r);return c===-1?[]:this.chatHistory.slice(i?c:c+1)});m(this,"resendLastExchange",async()=>{const r=this.lastExchange;if(r&&!this.awaitingReply)return this.resendTurn(r)});m(this,"resendTurn",r=>this.awaitingReply?Promise.resolve():(this._removeTurn(r),this.sendExchange({chatItemType:r.chatItemType,request_message:r.request_message,rich_text_json_repr:r.rich_text_json_repr,status:tt.draft,mentioned_items:r.mentioned_items,structured_request_nodes:r.structured_request_nodes,disableSelectedCodeDetails:r.disableSelectedCodeDetails,chatHistory:r.chatHistory,model_id:r.model_id},!1,r.request_id)));m(this,"_removeTurn",r=>{this.update({chatHistory:this.chatHistory.filter(i=>i!==r&&(!r.request_id||i.request_id!==r.request_id))})});m(this,"exchangeWithRequestId",r=>this.chatHistory.find(i=>i.request_id===r)||null);m(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});m(this,"historySummaryVersion",1);m(this,"markSeen",async r=>{if(!r.request_id||!this.chatHistory.find(c=>c.request_id===r.request_id))return;const i={seen_state:Rt.seen};this.update({chatHistory:this.chatHistory.map(c=>c.request_id===r.request_id?{...c,...i}:c)})});m(this,"createStructuredRequestNodes",r=>this._jsonToStructuredRequest(r));m(this,"saveDraftMentions",r=>{if(!this.draftExchange)return;const i=r.filter(c=>!c.personality&&!c.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:i}})});m(this,"saveDraftActiveContextIds",()=>{const r=this._specialContextInputModel.recentActiveItems.map(i=>i.id);this.update({draftActiveContextIds:r})});m(this,"loadDraftActiveContextIds",()=>{const r=new Set(this.draftActiveContextIds??[]),i=this._specialContextInputModel.recentItems.filter(f=>r.has(f.id)||f.recentFile||f.selection||f.sourceFolder),c=this._specialContextInputModel.recentItems.filter(f=>!(r.has(f.id)||f.recentFile||f.selection||f.sourceFolder));this._specialContextInputModel.markItemsActive(i.reverse()),this._specialContextInputModel.markItemsInactive(c.reverse())});m(this,"saveDraftExchange",(r,i)=>{var g,T,C;const c=r!==((g=this.draftExchange)==null?void 0:g.request_message),f=i!==((T=this.draftExchange)==null?void 0:T.rich_text_json_repr);if(!c&&!f)return;const h=(C=this.draftExchange)==null?void 0:C.mentioned_items;this.update({draftExchange:{request_message:r,rich_text_json_repr:i,mentioned_items:h,status:tt.draft}})});m(this,"clearDraftExchange",()=>{const r=this.draftExchange;return this.update({draftExchange:void 0}),r});m(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const r=this.clearDraftExchange();if(!r)return!1;const i=this._chatFlagModel.enableChatMultimodal&&r.rich_text_json_repr?this._jsonToStructuredRequest(r.rich_text_json_repr):void 0;return this.sendExchange({...r,structured_request_nodes:i,model_id:this.selectedModelId??void 0}).then(()=>{var c;if(!qr(this)){const f=!this.name&&this.chatHistory.length===1&&((c=this.firstExchange)==null?void 0:c.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&f&&this.updateConversationTitle()}}).finally(()=>{var c;qr(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Jo.sentUserMessage,conversationId:this.id,requestId:((c=this.lastExchange)==null?void 0:c.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});m(this,"cancelMessage",async()=>{var r;this.canCancelMessage&&((r=this.lastExchange)!=null&&r.request_id)&&(this.updateExchangeById({status:tt.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});m(this,"sendInstructionExchange",async(r,i)=>{let c=`${Ur}-${crypto.randomUUID()}`;const f={status:tt.sent,request_id:c,request_message:r,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Rt.unseen,timestamp:new Date().toISOString()};this.addExchange(f);for await(const h of this._extensionClient.sendInstructionMessage(f,i)){if(!this.updateExchangeById(h,c,!0))return;c=h.request_id||c}});m(this,"updateConversationTitle",async()=>{const{responseText:r}=await this.sendSummaryExchange();this.update({name:r})});m(this,"checkAndGenerateAgentTitle",()=>{var i;if(!(!qr(this)||!this._chatFlagModel.summaryTitles||this.name)){var r;!this.name&&(r=this.chatHistory,r.filter(c=>_a(c))).length===1&&!((i=this.extraData)!=null&&i.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});m(this,"sendSummaryExchange",()=>{const r={status:tt.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:sn.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(r)});m(this,"generateCommitMessage",async()=>{let r=`${Ur}-${crypto.randomUUID()}`;const i={status:tt.sent,request_id:r,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Rt.unseen,chatItemType:sn.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(i);for await(const c of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(c,r,!0))return;r=c.request_id||r}});m(this,"sendExchange",async(r,i=!1,c)=>{var T;this.updateLastInteraction();let f=`${Ur}-${crypto.randomUUID()}`,h=this._chatFlagModel.isModelIdValid(r.model_id)?r.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&$t.isNew(this._state)){const C=crypto.randomUUID(),L=this._state.id;try{await this._extensionClient.migrateConversationId(L,C)}catch(G){console.error("Failed to migrate conversation checkpoints:",G)}this._state={...this._state,id:C},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(C),this._subscribers.forEach(G=>G(this))}r=au(r);let g={status:tt.sent,request_id:f,request_message:r.request_message,rich_text_json_repr:r.rich_text_json_repr,model_id:h,mentioned_items:r.mentioned_items,structured_output_nodes:r.structured_output_nodes,seen_state:Rt.unseen,chatItemType:r.chatItemType,disableSelectedCodeDetails:r.disableSelectedCodeDetails,chatHistory:r.chatHistory,structured_request_nodes:r.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(g),this._loadContextFromExchange(g),this._onSendExchangeListeners.forEach(C=>C(g)),this._chatFlagModel.useHistorySummary&&await this.maybeAddHistorySummaryNode()&&this._clearStaleHistorySummaryNodes(),g=await this._addIdeStateNode(g),this.updateExchangeById({structured_request_nodes:g.structured_request_nodes},f,!1);for await(const C of this.sendUserMessage(f,g,i,c)){if(((T=this.exchangeWithRequestId(f))==null?void 0:T.status)!==tt.sent||!this.updateExchangeById(C,f,!0))return;f=C.request_id||f}});m(this,"sendSuggestedQuestion",r=>{this.sendExchange({request_message:r,status:tt.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Mf.chatUseSuggestedQuestion)});m(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});m(this,"recoverExchange",async r=>{var f;if(!r.request_id||r.status!==tt.sent)return;let i=r.request_id;const c=(f=r.structured_output_nodes)==null?void 0:f.filter(h=>h.type===lt.AGENT_MEMORY);this.updateExchangeById({...r,response_text:"",structured_output_nodes:c??[]},i);for await(const h of this.getChatStream(r)){if(!this.updateExchangeById(h,i,!0))return;i=h.request_id||i}});m(this,"_loadContextFromConversation",r=>{r.chatHistory.forEach(i=>{St(i)&&this._loadContextFromExchange(i)})});m(this,"_loadContextFromExchange",r=>{r.mentioned_items&&(this._specialContextInputModel.updateItems(r.mentioned_items,[]),this._specialContextInputModel.markItemsActive(r.mentioned_items))});m(this,"_unloadContextFromConversation",r=>{r.chatHistory.forEach(i=>{St(i)&&this._unloadContextFromExchange(i)})});m(this,"_unloadContextFromExchange",r=>{r.mentioned_items&&this._specialContextInputModel.updateItems([],r.mentioned_items)});m(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});m(this,"_jsonToStructuredRequest",r=>{const i=[],c=h=>{var T;const g=i.at(-1);if((g==null?void 0:g.type)===Tt.TEXT){const C=((T=g.text_node)==null?void 0:T.content)??"",L={...g,text_node:{content:C+h}};i[i.length-1]=L}else i.push({id:i.length,type:Tt.TEXT,text_node:{content:h}})},f=h=>{var g,T,C,L,G;if(h.type==="doc"||h.type==="paragraph")for(const M of h.content??[])f(M);else if(h.type==="hardBreak")c(`
`);else if(h.type==="text")c(h.text??"");else if(h.type==="file"){if(typeof((g=h.attrs)==null?void 0:g.src)!="string")return void console.error("File source is not a string: ",(T=h.attrs)==null?void 0:T.src);if(h.attrs.isLoading)return;const M=(C=h.attrs)==null?void 0:C.title,k=Pf(M);jf(M)?i.push({id:i.length,type:Tt.IMAGE_ID,image_id_node:{image_id:h.attrs.src,format:k}}):i.push({id:i.length,type:Tt.FILE_ID,file_id_node:{file_id:h.attrs.src,file_name:M}})}else if(h.type==="mention"){const M=(L=h.attrs)==null?void 0:L.data;M&&vu(M)?i.push({id:i.length,type:Tt.TEXT,text_node:{content:cd(this._chatFlagModel,M.personality.type)}}):M&&ed(M)?i.push({id:i.length,type:Tt.TEXT,text_node:{content:Af.getTaskOrchestratorPrompt(M.task)}}):c(`@\`${(M==null?void 0:M.name)??(M==null?void 0:M.id)}\``)}else if(h.type==="askMode"){const M=(G=h.attrs)==null?void 0:G.prompt;M&&i.push({id:i.length,type:Tt.TEXT,text_node:{content:M}})}};return f(r),i});this._extensionClient=r,this._chatFlagModel=i,this._specialContextInputModel=c,this._saveConversation=f,this._state={...$t.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return Yf(()=>{let r=0;const i=this._state.chatHistory;return this._convertHistoryToExchanges(i).forEach(c=>{r+=JSON.stringify(c).length}),this._state.draftExchange&&(r+=JSON.stringify(this._state.draftExchange).length),r},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var r;try{return(((r=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:r.reduce((c,f)=>c+f,0))||0)<=4?Et.PROTOTYPER:Et.DEFAULT}catch(i){return console.error("Error determining persona type:",i),Et.DEFAULT}}static create(r={}){const i=new Date().toISOString();return{id:r.id||crypto.randomUUID(),name:void 0,createdAtIso:i,lastInteractedAtIso:i,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Et.DEFAULT,...r}}static toSentenceCase(r){return r.charAt(0).toUpperCase()+r.slice(1)}static getDisplayName(r){if(r.name)return r.name;const i=r.chatHistory.find(St);return i&&i.request_message?$t.toSentenceCase(i.request_message):qr(r)?"New Agent":"New Chat"}static isNew(r){return r.id===od}static isEmpty(r){var f;const i=r.chatHistory.filter(h=>St(h)),c=r.chatHistory.filter(h=>hd(h));return i.length===0&&c.length===0&&!((f=r.draftExchange)!=null&&f.request_message)}static isNamed(r){return r.name!==void 0&&r.name!==""}static getTime(r,i){return i==="lastMessageTimestamp"?$t.lastMessageTimestamp(r):i==="lastInteractedAt"?$t.lastInteractedAt(r):$t.createdAt(r)}static createdAt(r){return new Date(r.createdAtIso)}static lastInteractedAt(r){return new Date(r.lastInteractedAtIso)}static lastMessageTimestamp(r){var c;const i=(c=r.chatHistory.findLast(St))==null?void 0:c.timestamp;return i?new Date(i):this.createdAt(r)}static isValid(r){return r.id!==void 0&&(!$t.isEmpty(r)||$t.isNamed(r))}onBeforeChangeConversation(r){return this._onBeforeChangeConversationListeners.push(r),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(i=>i!==r)}}_notifyBeforeChangeConversation(r,i){let c=i;for(const f of this._onBeforeChangeConversationListeners){const h=f(r,c);h!==void 0&&(c=h)}return c}get extraData(){return this._state.extraData}set extraData(r){this.update({extraData:r})}get focusModel(){return this._focusModel}get isValid(){return $t.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Et.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(r){this.update({rootTaskUuid:r})}get displayName(){return $t.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return $t.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(r){this.addExchange(r)}get requestIds(){return this._state.chatHistory.map(r=>r.request_id).filter(r=>r!==void 0)}get hasDraft(){var c;const r=(((c=this.draftExchange)==null?void 0:c.request_message)??"").trim()!=="",i=this.hasImagesInDraft();return r||i}hasImagesInDraft(){var c;const r=(c=this.draftExchange)==null?void 0:c.rich_text_json_repr;if(!r)return!1;const i=f=>Array.isArray(f)?f.some(i):!!f&&(f.type==="file"||!(!f.content||!Array.isArray(f.content))&&f.content.some(i));return i(r)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(St)??null}get lastExchange(){return this.chatHistory.findLast(St)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(r=>St(r)&&r.status===tt.sent)}get successfulMessages(){return this._state.chatHistory.filter(r=>pa(r)||An(r)||Ar(r))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(r){if(r.length===0)return[];const i=r.findLastIndex(f=>Ar(f)&&f.summaryVersion===this.historySummaryVersion);this._chatFlagModel.useHistorySummary&&i>0&&(console.info("Using history summary node found at index %d",i),r=r.slice(i));const c=[];for(const f of r)if(pa(f))c.push(su(f));else if(An(f)&&f.fromTimestamp!==void 0&&f.toTimestamp!==void 0){if(f.revertTarget){const h=dd(f,1),g={request_message:"",response_text:"",request_id:f.request_id||crypto.randomUUID(),request_nodes:[h],response_nodes:[]};c.push(g)}}else this._chatFlagModel.useHistorySummary&&Ar(f)&&f.summaryVersion===this.historySummaryVersion&&c.push(su(f));return c}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===tt.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(r){const i=crypto.randomUUID();let c,f="";const h=await this._addIdeStateNode(au({...r,request_id:i,status:tt.sent,timestamp:new Date().toISOString()}));for await(const g of this.sendUserMessage(i,h,!0))g.response_text&&(f+=g.response_text),g.request_id&&(c=g.request_id);return{responseText:f,requestId:c}}async*getChatStream(r){r.request_id&&(yield*this._extensionClient.getExistingChatStream(r,{flags:this._chatFlagModel}))}_createStreamStateHandlers(r,i,c){return[]}_resolveUnresolvedToolUses(r,i,c){var G,M,k;if(r.length===0)return[r,i];const f=r[r.length-1],h=((G=f.response_nodes)==null?void 0:G.filter(N=>N.type===lt.TOOL_USE))??[];if(h.length===0)return[r,i];const g=new Set;(M=i.structured_request_nodes)==null||M.forEach(N=>{var K;N.type===Tt.TOOL_RESULT&&((K=N.tool_result_node)!=null&&K.tool_use_id)&&g.add(N.tool_result_node.tool_use_id)});const T=h.filter(N=>{var ft;const K=(ft=N.tool_use)==null?void 0:ft.tool_use_id;return K&&!g.has(K)});if(T.length===0)return[r,i];const C=T.map((N,K)=>{const ft=N.tool_use.tool_use_id;return function(Z,_t,jt,te){const ve=ad(_t,Z,te);let st;if(ve!==void 0)st=ve;else{let wt;switch(_t.phase){case gt.runnable:wt="Tool was cancelled before running.";break;case gt.new:wt="Cancelled by user.";break;case gt.checkingSafety:wt="Tool was cancelled during safety check.";break;case gt.running:wt="Tool was cancelled while running.";break;case gt.cancelling:wt="Tool cancellation was interrupted.";break;case gt.cancelled:wt="Cancelled by user.";break;case gt.error:wt="Tool execution failed.";break;case gt.completed:wt="Tool completed but result was unavailable.";break;case gt.unknown:default:wt="Cancelled by user.",_t.phase!==gt.unknown&&console.error(`Unexpected tool state phase: ${_t.phase}`)}st={tool_use_id:Z,content:wt,is_error:!0}}return{id:jt,type:Tt.TOOL_RESULT,tool_result_node:st}}(ft,this.getToolUseState(f.request_id,ft),ya(i.structured_request_nodes??[])+K+1,this._chatFlagModel.enableDebugFeatures)});if((k=i.structured_request_nodes)==null?void 0:k.some(N=>N.type===Tt.TOOL_RESULT))return[r,{...i,structured_request_nodes:[...i.structured_request_nodes??[],...C]}];{const N={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:C,structured_output_nodes:[],status:tt.success,hidden:!0};return c||this.addExchangeBeforeLast(N),[r.concat(this._convertHistoryToExchanges([N])),i]}}async*sendUserMessage(r,i,c,f){var M;const h=this._specialContextInputModel.chatActiveContext;let g;if(i.chatHistory!==void 0)g=i.chatHistory;else{let k=this.successfulMessages;if(i.chatItemType===sn.summaryTitle){const N=k.findIndex(K=>K.chatItemType!==sn.agentOnboarding&&_a(K));N!==-1&&(k=k.slice(N))}g=this._convertHistoryToExchanges(k)}this._chatFlagModel.enableDebugFeatures&&([g,i]=this._resolveUnresolvedToolUses(g,i,c));let T=this.personaType;if(i.structured_request_nodes){const k=i.structured_request_nodes.find(N=>N.type===Tt.CHANGE_PERSONALITY);k&&k.change_personality_node&&(T=k.change_personality_node.personality_type)}const C={text:i.request_message,chatHistory:g,silent:c,modelId:i.model_id,context:h,userSpecifiedFiles:h.userSpecifiedFiles,externalSourceIds:(M=h.externalSources)==null?void 0:M.map(k=>k.id),disableRetrieval:i.disableRetrieval??!1,disableSelectedCodeDetails:i.disableSelectedCodeDetails??!1,nodes:i.structured_request_nodes,memoriesInfo:i.memoriesInfo,personaType:T,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:f},L=this._createStreamStateHandlers(r,C,{flags:this._chatFlagModel}),G=this._extensionClient.startChatStreamWithRetry(r,C,{flags:this._chatFlagModel});for await(const k of G){let N=k;r=k.request_id||r;for(const K of L)N=K.handleChunk(N)??N;yield N}for(const k of L)yield*k.handleComplete();this.updateExchangeById({structured_request_nodes:i.structured_request_nodes},r)}onSendExchange(r){return this._onSendExchangeListeners.push(r),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(i=>i!==r)}}onNewConversation(r){return this._onNewConversationListeners.push(r),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(i=>i!==r)}}onHistoryDelete(r){return this._onHistoryDeleteListeners.push(r),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(i=>i!==r)}}updateChatItem(r,i){return this.chatHistory.find(c=>c.request_id===r)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(c=>c.request_id===r?{...c,...i}:c)}),!0)}async _addIdeStateNode(r){let i,c=(r.structured_request_nodes??[]).filter(f=>f.type!==Tt.IDE_STATE);try{i=await this._extensionClient.getChatRequestIdeState()}catch(f){console.error("Failed to add IDE state to exchange:",f)}return i?(c=[...c,{id:ya(c)+1,type:Tt.IDE_STATE,ide_state_node:i}],{...r,structured_request_nodes:c}):r}async maybeAddHistorySummaryNode(){var jt,te,ve;const r=this._chatFlagModel.historySummaryPrompt;if(!r||r.trim()==="")return!1;const i=this._convertHistoryToExchanges(this.chatHistory),[c,f]=qf(i,this._chatFlagModel.historySummaryLowerChars,this._chatFlagModel.historySummaryMaxChars);if(c.length===0)return!1;const h=JSON.stringify(i).length,g=JSON.stringify(c).length,T=JSON.stringify(f).length,C={totalHistoryCharCount:h,totalHistoryExchangeCount:i.length,headCharCount:g,headExchangeCount:c.length,headLastRequestId:((jt=c.at(-1))==null?void 0:jt.request_id)??"",tailCharCount:T,tailExchangeCount:f.length,tailLastRequestId:((te=f.at(-1))==null?void 0:te.request_id)??"",summaryCharCount:0,summarizationDurationMs:0};let L=((ve=c.at(-1))==null?void 0:ve.response_nodes)??[],G=L.filter(st=>st.type===lt.TOOL_USE);G.length>0&&(c.at(-1).response_nodes=L.filter(st=>st.type!==lt.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",c.length);const M=Date.now(),{responseText:k,requestId:N}=await this.sendSilentExchange({request_message:r,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:c}),K=Date.now();C.summaryCharCount=k.length,C.summarizationDurationMs=K-M,this._extensionClient.reportAgentRequestEvent({eventName:Jo.chatHistorySummarization,conversationId:this.id,requestId:N??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length,eventData:{chatHistorySummarizationData:C}});const ft={chatItemType:sn.historySummary,summaryVersion:this.historySummaryVersion,request_id:N,request_message:r,response_text:k,structured_output_nodes:[{id:G.map(st=>st.id).reduce((st,wt)=>Math.max(st,wt),-1)+1,type:lt.RAW_RESPONSE,content:k},...G],status:tt.success,seen_state:Rt.seen,timestamp:new Date().toISOString()},Z=this.chatHistory.findLastIndex(st=>st.request_id===c.at(-1).request_id)+1;console.info("Adding a history summary node at index %d",Z);const _t=[...this._state.chatHistory];return _t.splice(Z,0,ft),this.update({chatHistory:_t}),!0}_clearStaleHistorySummaryNodes(){this.update({chatHistory:this.chatHistory.filter(r=>!Ar(r)||r.summaryVersion===this.historySummaryVersion)})}}function dd(s,r){const i=(An(s),s.fromTimestamp),c=(An(s),s.toTimestamp),f=An(s)&&s.revertTarget!==void 0;return{id:r,type:Tt.CHECKPOINT_REF,checkpoint_ref_node:{request_id:s.request_id||"",from_timestamp:i,to_timestamp:c,source:f?Lf.CHECKPOINT_REVERT:void 0}}}function su(s){const r=(s.structured_output_nodes??[]).filter(i=>i.type===lt.RAW_RESPONSE||i.type===lt.TOOL_USE||i.type===lt.TOOL_USE_START).map(i=>i.type===lt.TOOL_USE_START?{...i,tool_use:{...i.tool_use,input_json:"{}"},type:lt.TOOL_USE}:i);return{request_message:s.request_message,response_text:s.response_text??"",request_id:s.request_id||"",request_nodes:s.structured_request_nodes??[],response_nodes:r}}function ya(s){return s.length>0?Math.max(...s.map(r=>r.id)):0}function au(s){var r;if(s.request_message.length>0&&!((r=s.structured_request_nodes)!=null&&r.some(i=>i.type===Tt.TEXT))){let i=s.structured_request_nodes??[];return i=[...i,{id:ya(i)+1,type:Tt.TEXT,text_node:{content:s.request_message}}],{...s,structured_request_nodes:i}}return s}const Yd="augment-welcome";var tt=(s=>(s.draft="draft",s.sent="sent",s.failed="failed",s.success="success",s.cancelled="cancelled",s))(tt||{}),Be=(s=>(s.running="running",s.awaitingUserAction="awaiting-user-action",s.notRunning="not-running",s))(Be||{}),Rt=(s=>(s.seen="seen",s.unseen="unseen",s))(Rt||{}),sn=(s=>(s.signInWelcome="sign-in-welcome",s.generateCommitMessage="generate-commit-message",s.summaryResponse="summary-response",s.summaryTitle="summary-title",s.educateFeatures="educate-features",s.agentOnboarding="agent-onboarding",s.agenticTurnDelimiter="agentic-turn-delimiter",s.agenticRevertDelimiter="agentic-revert-delimiter",s.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",s.exchange="exchange",s.exchangePointer="exchange-pointer",s.historySummary="history-summary",s))(sn||{});function iu(s){return St(s)||pd(s)||gd(s)}function St(s){return!!s&&(s.chatItemType===void 0||s.chatItemType==="agent-onboarding")}function pa(s){return St(s)&&s.status==="success"}function hd(s){return!!s&&s.chatItemType==="exchange-pointer"}function Kd(s){return s.chatItemType==="sign-in-welcome"}function pd(s){return s.chatItemType==="generate-commit-message"}function Xd(s){return s.chatItemType==="summary-response"}function Zd(s){return s.chatItemType==="educate-features"}function gd(s){return s.chatItemType==="agent-onboarding"}function Jd(s){return s.chatItemType==="agentic-turn-delimiter"}function An(s){return s.chatItemType==="agentic-checkpoint-delimiter"}function Ar(s){return s.chatItemType==="history-summary"}function Qd(s){return s.revertTarget!==void 0}function th(s,r){const i=function(f){if(!f)return;const h=f.findLast(g=>iu(g.turn));return h?h.turn:void 0}(s);if(!((i==null?void 0:i.status)==="success"||(i==null?void 0:i.status)==="failed"||(i==null?void 0:i.status)==="cancelled"))return!1;const c=function(f){return f?f.findLast(g=>{var T;return!((T=g.turn.request_id)!=null&&T.startsWith(Ur))&&iu(g.turn)}):void 0}(s);return(c==null?void 0:c.turn.request_id)===r.request_id}function eh(s){var r;return((r=s.structured_output_nodes)==null?void 0:r.some(i=>i.type===lt.TOOL_USE))??!1}function nh(s){var r;return((r=s.structured_request_nodes)==null?void 0:r.some(i=>i.type===Tt.TOOL_RESULT))??!1}function rh(s){return!(!s||typeof s!="object")&&(!("request_id"in s)||typeof s.request_id=="string")&&(!("seen_state"in s)||s.seen_state==="seen"||s.seen_state==="unseen")}function sh(s){return(s==null?void 0:s.status)==="success"||(s==null?void 0:s.status)==="failed"||(s==null?void 0:s.status)==="cancelled"}function ah(s){if(!s)return;const r=s.filter(i=>St(i.turn)).map(i=>{return"response_text"in(c=i.turn)?c.response_text??"":"";var c}).filter(i=>i.length>0);return r.length>0?r.join(`
`):void 0}async function*_d(s,r=1e3){for(;s>0;)yield s,await new Promise(i=>setTimeout(i,Math.min(r,s))),s-=r}class yd{constructor(r,i,c,f=5,h=4e3,g){m(this,"_isCancelled",!1);this.requestId=r,this.chatMessage=i,this.startStreamFn=c,this.maxRetries=f,this.baseDelay=h,this.flags=g}cancel(){this._isCancelled=!0}async*getStream(){let r=0,i=0,c=!1;try{for(;!this._isCancelled;){const f=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let h,g,T=!1,C=!0;for await(const L of f){if(L.status===tt.failed){if(L.isRetriable!==!0||c)return yield L;T=!0,C=L.shouldBackoff??!0,h=L.display_error_message,g=L.request_id;break}c=!0,yield L}if(!T)return;if(this._isCancelled)return yield this.createCancelledStatus();if(r++,r>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${h}`),void(yield{request_id:g??this.requestId,seen_state:Rt.unseen,status:tt.failed,display_error_message:h,isRetriable:!1});if(C){const L=this.baseDelay*2**i;i++;for await(const G of _d(L))yield{request_id:this.requestId,status:tt.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(G/1e3)} seconds... (Attempt ${r} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:tt.sent,display_error_message:`Generating response... (Attempt ${r+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(f){console.error("Unexpected error in chat stream:",f),yield{request_id:this.requestId,seen_state:Rt.unseen,status:tt.failed,display_error_message:f instanceof Error?f.message:String(f)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:Rt.unseen,status:tt.cancelled}}}class md{constructor(r){m(this,"getHydratedTask",async r=>{const i={type:Cn.getHydratedTaskRequest,data:{uuid:r}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data.task});m(this,"createTask",async(r,i,c)=>{const f={type:Cn.createTaskRequest,data:{name:r,description:i,parentTaskUuid:c}};return(await this._asyncMsgSender.sendToSidecar(f,3e4)).data.uuid});m(this,"updateTask",async(r,i,c)=>{const f={type:Cn.updateTaskRequest,data:{uuid:r,updates:i,updatedBy:c}};await this._asyncMsgSender.sendToSidecar(f,3e4)});m(this,"setCurrentRootTaskUuid",r=>{const i={type:Cn.setCurrentRootTaskUuid,data:{uuid:r}};this._asyncMsgSender.sendToSidecar(i)});m(this,"updateHydratedTask",async(r,i)=>{const c={type:Cn.updateHydratedTaskRequest,data:{task:r,updatedBy:i}};return(await this._asyncMsgSender.sendToSidecar(c,3e4)).data});this._asyncMsgSender=r}}class ih{constructor(r,i,c){m(this,"_taskClient");m(this,"getChatInitData",async()=>{const r=await this._asyncMsgSender.send({type:O.chatLoaded},3e4);if(r.data.enableDebugFeatures)try{console.log("Running hello world test...");const i=await async function(c){return(await Vf(Of,new Uf({sendMessage:h=>{c.postMessage(h)},onReceiveMessage:h=>{const g=T=>{h(T.data)};return window.addEventListener("message",g),()=>{window.removeEventListener("message",g)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",i)}catch(i){console.error("Hello world error:",i)}return r.data});m(this,"reportWebviewClientEvent",r=>{this._asyncMsgSender.send({type:O.reportWebviewClientMetric,data:{webviewName:Rf.chat,client_metric:r,value:1}})});m(this,"reportAgentSessionEvent",r=>{this._asyncMsgSender.sendToSidecar({type:At.reportAgentSessionEvent,data:r})});m(this,"reportAgentRequestEvent",r=>{this._asyncMsgSender.sendToSidecar({type:At.reportAgentRequestEvent,data:r})});m(this,"getSuggestions",async(r,i=!1)=>{const c={rootPath:"",relPath:r},f=this.findFiles(c,6),h=this.findRecentlyOpenedFiles(c,6),g=this.findFolders(c,3),T=this.findExternalSources(r,i),C=this._flags.enableRules?this.findRules(r,6):Promise.resolve([]),[L,G,M,k,N]=await Promise.all([qn(f,[]),qn(h,[]),qn(g,[]),qn(T,[]),qn(C,[])]),K=(Z,_t)=>({...nd(Z),[_t]:Z}),ft=[...L.map(Z=>K(Z,"file")),...M.map(Z=>K(Z,"folder")),...G.map(Z=>K(Z,"recentFile")),...k.map(Z=>({label:Z.name,name:Z.name,id:Z.id,externalSource:Z})),...N.map(Z=>({...rd(Z),rule:Z}))];if(this._flags.enablePersonalities){const Z=this.getPersonalities(r);Z.length>0&&ft.push(...Z)}return ft});m(this,"getPersonalities",r=>{if(!this._flags.enablePersonalities)return[];if(r==="")return ru;const i=r.toLowerCase();return ru.filter(c=>{const f=c.personality.description.toLowerCase(),h=c.label.toLowerCase();return f.includes(i)||h.includes(i)})});m(this,"sendAction",r=>{this._host.postMessage({type:O.mainPanelPerformAction,data:r})});m(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:O.showAugmentPanel})});m(this,"showNotification",r=>{this._host.postMessage({type:O.showNotification,data:r})});m(this,"openConfirmationModal",async r=>(await this._asyncMsgSender.send({type:O.openConfirmationModal,data:r},1e9)).data.ok);m(this,"clearMetadataFor",r=>{this._host.postMessage({type:O.chatClearMetadata,data:r})});m(this,"resolvePath",async(r,i=void 0)=>{const c=await this._asyncMsgSender.send({type:O.resolveFileRequest,data:{...r,exactMatch:!0,maxResults:1,searchScope:i}},5e3);if(c.data)return c.data});m(this,"resolveSymbols",async(r,i)=>(await this._asyncMsgSender.send({type:O.findSymbolRequest,data:{query:r,searchScope:i}},3e4)).data);m(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:O.getDiagnosticsRequest},1e3)).data);m(this,"findFiles",async(r,i=12)=>(await this._asyncMsgSender.send({type:O.findFileRequest,data:{...r,maxResults:i}},5e3)).data);m(this,"findFolders",async(r,i=12)=>(await this._asyncMsgSender.send({type:O.findFolderRequest,data:{...r,maxResults:i}},5e3)).data);m(this,"findRecentlyOpenedFiles",async(r,i=12)=>(await this._asyncMsgSender.send({type:O.findRecentlyOpenedFilesRequest,data:{...r,maxResults:i}},5e3)).data);m(this,"findExternalSources",async(r,i=!1)=>this._flags.enableExternalSourcesInChat?i?[]:(await this._asyncMsgSender.send({type:O.findExternalSourcesRequest,data:{query:r,source_types:[]}},5e3)).data.sources??[]:[]);m(this,"findRules",async(r,i=12)=>(await this._asyncMsgSender.sendToSidecar({type:kf.getRulesListRequest,data:{query:r,maxResults:i}})).data.rules);m(this,"openFile",r=>{this._host.postMessage({type:O.openFile,data:r})});m(this,"saveFile",r=>this._host.postMessage({type:O.saveFile,data:r}));m(this,"loadFile",r=>this._host.postMessage({type:O.loadFile,data:r}));m(this,"openMemoriesFile",()=>{this._host.postMessage({type:O.openMemoriesFile})});m(this,"canShowTerminal",async(r,i)=>{try{return(await this._asyncMsgSender.send({type:O.canShowTerminal,data:{terminalId:r,command:i}},5e3)).data.canShow}catch(c){return console.error("Failed to check if terminal can be shown:",c),!1}});m(this,"showTerminal",async(r,i)=>{try{return(await this._asyncMsgSender.send({type:O.showTerminal,data:{terminalId:r,command:i}},5e3)).data.success}catch(c){return console.error("Failed to show terminal:",c),!1}});m(this,"createFile",(r,i)=>{this._host.postMessage({type:O.chatCreateFile,data:{code:r,relPath:i}})});m(this,"openScratchFile",async(r,i="shellscript")=>{await this._asyncMsgSender.send({type:O.openScratchFileRequest,data:{content:r,language:i}},1e4)});m(this,"resolveWorkspaceFileChunk",async r=>{try{return(await this._asyncMsgSender.send({type:O.resolveWorkspaceFileChunkRequest,data:r},5e3)).data}catch{return}});m(this,"smartPaste",r=>{this._host.postMessage({type:O.chatSmartPaste,data:r})});m(this,"getHydratedTask",async r=>this._taskClient.getHydratedTask(r));m(this,"updateHydratedTask",async(r,i)=>this._taskClient.updateHydratedTask(r,i));m(this,"setCurrentRootTaskUuid",r=>{this._taskClient.setCurrentRootTaskUuid(r)});m(this,"createTask",async(r,i,c)=>this._taskClient.createTask(r,i,c));m(this,"updateTask",async(r,i,c)=>this._taskClient.updateTask(r,i,c));m(this,"saveChat",async(r,i,c)=>this._asyncMsgSender.send({type:O.saveChat,data:{conversationId:r,chatHistory:i,title:c}},5e3));m(this,"updateUserGuidelines",r=>{this._host.postMessage({type:O.updateUserGuidelines,data:r})});m(this,"updateWorkspaceGuidelines",r=>{this._host.postMessage({type:O.updateWorkspaceGuidelines,data:r})});m(this,"openSettingsPage",r=>{this._host.postMessage({type:O.openSettingsPage,data:r})});m(this,"_activeRetryStreams",new Map);m(this,"cancelChatStream",async r=>{var i;(i=this._activeRetryStreams.get(r))==null||i.cancel(),await this._asyncMsgSender.send({type:O.chatUserCancel,data:{requestId:r}},1e4)});m(this,"sendUserRating",async(r,i,c,f="")=>{const h={requestId:r,rating:c,note:f,mode:i},g={type:O.chatRating,data:h};return(await this._asyncMsgSender.send(g,3e4)).data});m(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:O.usedChat})});m(this,"createProject",r=>{this._host.postMessage({type:O.mainPanelCreateProject,data:{name:r}})});m(this,"openProjectFolder",()=>{this._host.postMessage({type:O.mainPanelPerformAction,data:"open-folder"})});m(this,"closeProjectFolder",()=>{this._host.postMessage({type:O.mainPanelPerformAction,data:"close-folder"})});m(this,"cloneRepository",()=>{this._host.postMessage({type:O.mainPanelPerformAction,data:"clone-repository"})});m(this,"grantSyncPermission",()=>{this._host.postMessage({type:O.mainPanelPerformAction,data:"grant-sync-permission"})});m(this,"callTool",async(r,i,c,f,h,g)=>{const T={type:O.callTool,data:{chatRequestId:r,toolUseId:i,name:c,input:f,chatHistory:h,conversationId:g}};return(await this._asyncMsgSender.send(T,0)).data});m(this,"cancelToolRun",async(r,i)=>{const c={type:O.cancelToolRun,data:{requestId:r,toolUseId:i}};await this._asyncMsgSender.send(c,0)});m(this,"checkSafe",async r=>{const i={type:ua.checkToolCallSafeRequest,data:r};return(await this._asyncMsgSender.sendToSidecar(i,0)).data});m(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:ua.closeAllToolProcesses},0)});m(this,"getToolIdentifier",async r=>{const i={type:ua.getToolIdentifierRequest,data:{toolName:r}};return(await this._asyncMsgSender.sendToSidecar(i,0)).data});m(this,"getChatMode",async()=>{const r={type:At.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.chatMode});m(this,"setChatMode",r=>{this._asyncMsgSender.send({type:O.chatModeChanged,data:{mode:r}})});m(this,"getAgentEditList",async(r,i)=>{const c={type:At.getEditListRequest,data:{fromTimestamp:r,toTimestamp:i}};return(await this._asyncMsgSender.sendToSidecar(c,3e4)).data});m(this,"hasChangesSince",async r=>{const i={type:At.getEditListRequest,data:{fromTimestamp:r,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data.edits.filter(c=>{var f,h;return((f=c.changesSummary)==null?void 0:f.totalAddedLines)||((h=c.changesSummary)==null?void 0:h.totalRemovedLines)}).length>0});m(this,"getToolCallCheckpoint",async r=>{const i={type:O.getToolCallCheckpoint,data:{requestId:r}};return(await this._asyncMsgSender.send(i,3e4)).data.checkpointNumber});m(this,"setCurrentConversation",r=>{this._asyncMsgSender.sendToSidecar({type:At.setCurrentConversation,data:{conversationId:r}})});m(this,"migrateConversationId",async(r,i)=>{await this._asyncMsgSender.sendToSidecar({type:At.migrateConversationId,data:{oldConversationId:r,newConversationId:i}},3e4)});m(this,"showAgentReview",(r,i,c,f=!0,h)=>{this._asyncMsgSender.sendToSidecar({type:At.chatReviewAgentFile,data:{qualifiedPathName:r,fromTimestamp:i,toTimestamp:c,retainFocus:f,useNativeDiffIfAvailable:h}})});m(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:At.chatAgentEditAcceptAll}),!0));m(this,"revertToTimestamp",async(r,i)=>(await this._asyncMsgSender.sendToSidecar({type:At.revertToTimestamp,data:{timestamp:r,qualifiedPathNames:i}}),!0));m(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:O.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);m(this,"getAgentEditChangesByRequestId",async r=>{const i={type:At.getEditChangesByRequestIdRequest,data:{requestId:r}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data});m(this,"getAgentEditContentsByRequestId",async r=>{const i={type:At.getAgentEditContentsByRequestId,data:{requestId:r}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data});m(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:O.triggerInitialOrientation})});m(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:O.getWorkspaceInfoRequest},5e3)).data}catch(r){return console.error("Error getting workspace info:",r),{}}});m(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:O.toggleCollapseUnchangedRegions})});m(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:O.checkAgentAutoModeApproval},5e3)).data);m(this,"setAgentAutoModeApproved",async r=>{await this._asyncMsgSender.send({type:O.setAgentAutoModeApproved,data:r},5e3)});m(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:At.checkHasEverUsedAgent},5e3)).data);m(this,"setHasEverUsedAgent",async r=>{await this._asyncMsgSender.sendToSidecar({type:At.setHasEverUsedAgent,data:r},5e3)});m(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:At.checkHasEverUsedRemoteAgent},5e3)).data);m(this,"setHasEverUsedRemoteAgent",async r=>{await this._asyncMsgSender.sendToSidecar({type:At.setHasEverUsedRemoteAgent,data:r},5e3)});m(this,"getChatRequestIdeState",async()=>{const r={type:O.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(r,3e4)).data});m(this,"reportError",r=>{this._host.postMessage({type:O.reportError,data:r})});m(this,"sendMemoryCreated",async r=>{await this._asyncMsgSender.sendToSidecar(r,5e3)});this._host=r,this._asyncMsgSender=i,this._flags=c,this._taskClient=new md(i)}async*generateCommitMessage(){const r={type:O.generateCommitMessage},i=this._asyncMsgSender.stream(r,3e4,6e4);yield*ga(i,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(r,i){const c={instruction:r.request_message??"",selectedCodeDetails:i,requestId:r.request_id},f={type:O.chatInstructionMessage,data:c},h=this._asyncMsgSender.stream(f,3e4,6e4);yield*async function*(g){let T;try{for await(const C of g)T=C.data.requestId,yield{request_id:T,response_text:C.data.text,seen_state:Rt.unseen,status:tt.sent};yield{request_id:T,seen_state:Rt.unseen,status:tt.success}}catch(C){console.error("Error in chat instruction model reply stream:",C),yield{request_id:T,seen_state:Rt.unseen,status:tt.failed}}}(h)}async openGuidelines(r){this._host.postMessage({type:O.openGuidelines,data:r})}async*getExistingChatStream(r,i){if(!r.request_id)return;const c=i==null?void 0:i.flags.enablePreferenceCollection,f=c?1e9:6e4,h=c?1e9:3e5,g={type:O.chatGetStreamRequest,data:{requestId:r.request_id}},T=this._asyncMsgSender.stream(g,f,h);yield*ga(T,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(r,i){const c=i==null?void 0:i.flags.enablePreferenceCollection,f=c?1e9:1e5,h=c?1e9:3e5,g={type:O.chatUserMessage,data:r},T=this._asyncMsgSender.stream(g,f,h);yield*ga(T,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(r){return(await this._asyncMsgSender.send({type:O.checkToolExists,toolName:r},0)).exists}async saveImage(r,i){const c=Qo(await ca(r)),f=i??`${await tu(await la(c))}.${r.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:O.chatSaveImageRequest,data:{filename:f,data:c}},1e4)).data}async saveAttachment(r,i){const c=Qo(await ca(r)),f=i??`${await tu(await la(c))}.${r.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:O.chatSaveAttachmentRequest,data:{filename:f,data:c}},1e4)).data}async loadImage(r){const i=await this._asyncMsgSender.send({type:O.chatLoadImageRequest,data:r},1e4),c=i.data?await la(i.data):void 0;if(!c)return;let f="application/octet-stream";const h=r.split(".").at(-1);h==="png"?f="image/png":h!=="jpg"&&h!=="jpeg"||(f="image/jpeg");const g=new File([c],r,{type:f});return await ca(g)}async deleteImage(r){await this._asyncMsgSender.send({type:O.chatDeleteImageRequest,data:r},1e4)}async*startChatStreamWithRetry(r,i,c){const f=new yd(r,i,(h,g)=>this.startChatStream(h,g),(c==null?void 0:c.maxRetries)??5,4e3,c==null?void 0:c.flags);this._activeRetryStreams.set(r,f);try{yield*f.getStream()}finally{this._activeRetryStreams.delete(r)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:O.getSubscriptionInfo},5e3)}async loadExchanges(r,i){if(i.length===0)return[];const c={type:fa.loadExchangesByUuidsRequest,data:{conversationId:r,uuids:i}};return(await this._asyncMsgSender.sendToSidecar(c,3e4)).data.exchanges}async saveExchanges(r,i){if(i.length===0)return;const c={type:fa.saveExchangesRequest,data:{conversationId:r,exchanges:i}};await this._asyncMsgSender.sendToSidecar(c,3e4)}async deleteConversationExchanges(r){const i={type:fa.deleteConversationExchangesRequest,data:{conversationId:r}};await this._asyncMsgSender.sendToSidecar(i,3e4)}async loadConversationToolUseStates(r){const i={type:da.loadConversationToolUseStatesRequest,data:{conversationId:r}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data.toolUseStates}async saveToolUseStates(r,i){if(Object.keys(i).length===0)return;const c={type:da.saveToolUseStatesRequest,data:{conversationId:r,toolUseStates:i}};await this._asyncMsgSender.sendToSidecar(c,3e4)}async deleteConversationToolUseStates(r){const i={type:da.deleteConversationToolUseStatesRequest,data:{conversationId:r}};await this._asyncMsgSender.sendToSidecar(i,3e4)}}async function*ga(s,r=()=>{},i){let c;try{for await(const f of s){if(c=f.data.requestId,f.data.error)return console.error("Error in chat model reply stream:",f.data.error.displayErrorMessage),yield{request_id:c,seen_state:Rt.unseen,status:tt.failed,display_error_message:f.data.error.displayErrorMessage,isRetriable:f.data.error.isRetriable,shouldBackoff:f.data.error.shouldBackoff};const h={request_id:c,response_text:f.data.text,workspace_file_chunks:f.data.workspaceFileChunks,structured_output_nodes:vd(f.data.nodes),seen_state:Rt.unseen,status:tt.sent};f.data.stop_reason!=null&&(h.stop_reason=f.data.stop_reason),yield h}yield{request_id:c,seen_state:Rt.unseen,status:tt.success}}catch(f){let h,g;if(r({originalRequestId:c||"",sanitizedMessage:f instanceof Error?f.message:String(f),stackTrace:f instanceof Error&&f.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),f instanceof Ff&&i)switch(f.name){case"MessageTimeout":h=!0,g=!1;break;case"StreamTimeout":case"InvalidResponse":h=!1}console.error("Unexpected error in chat model reply stream:",f),yield{request_id:c,seen_state:Rt.unseen,status:tt.failed,isRetriable:h,shouldBackoff:g}}}async function qn(s,r){try{return await s}catch(i){return console.warn(`Error while resolving promise: ${i}`),r}}function vd(s){if(!s)return s;let r=!1;return s.filter(i=>i.type!==lt.TOOL_USE||!r&&(r=!0,!0))}var Rr,kr,ma={exports:{}};Rr=ma,kr=ma.exports,(function(){var s,r="Expected a function",i="__lodash_hash_undefined__",c="__lodash_placeholder__",f=16,h=32,g=64,T=128,C=256,L=1/0,G=9007199254740991,M=NaN,k=4294967295,N=[["ary",T],["bind",1],["bindKey",2],["curry",8],["curryRight",f],["flip",512],["partial",h],["partialRight",g],["rearg",C]],K="[object Arguments]",ft="[object Array]",Z="[object Boolean]",_t="[object Date]",jt="[object Error]",te="[object Function]",ve="[object GeneratorFunction]",st="[object Map]",wt="[object Number]",le="[object Object]",va="[object Promise]",un="[object RegExp]",ee="[object Set]",cn="[object String]",kn="[object Symbol]",ln="[object WeakMap]",fn="[object ArrayBuffer]",ze="[object DataView]",Hr="[object Float32Array]",Pr="[object Float64Array]",jr="[object Int8Array]",Nr="[object Int16Array]",Br="[object Int32Array]",zr="[object Uint8Array]",Wr="[object Uint8ClampedArray]",$r="[object Uint16Array]",Gr="[object Uint32Array]",wu=/\b__p \+= '';/g,xu=/\b(__p \+=) '' \+/g,Su=/(__e\(.*?\)|\b__t\)) \+\n'';/g,wa=/&(?:amp|lt|gt|quot|#39);/g,xa=/[&<>"']/g,bu=RegExp(wa.source),Iu=RegExp(xa.source),Tu=/<%-([\s\S]+?)%>/g,Eu=/<%([\s\S]+?)%>/g,Sa=/<%=([\s\S]+?)%>/g,Cu=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Mu=/^\w*$/,qu=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Vr=/[\\^$.*+?()[\]{}|]/g,Au=RegExp(Vr.source),Yr=/^\s+/,Ru=/\s/,ku=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Uu=/\{\n\/\* \[wrapped with (.+)\] \*/,Ou=/,? & /,Fu=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Lu=/[()=,{}\[\]\/\s]/,Du=/\\(\\)?/g,Hu=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,ba=/\w*$/,Pu=/^[-+]0x[0-9a-f]+$/i,ju=/^0b[01]+$/i,Nu=/^\[object .+?Constructor\]$/,Bu=/^0o[0-7]+$/i,zu=/^(?:0|[1-9]\d*)$/,Wu=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Un=/($^)/,$u=/['\n\r\u2028\u2029\\]/g,On="\\ud800-\\udfff",Ia="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Ta="\\u2700-\\u27bf",Ea="a-z\\xdf-\\xf6\\xf8-\\xff",Ca="A-Z\\xc0-\\xd6\\xd8-\\xde",Ma="\\ufe0e\\ufe0f",qa="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Gu="['’]",Vu="["+On+"]",Aa="["+qa+"]",Fn="["+Ia+"]",Ra="\\d+",Yu="["+Ta+"]",ka="["+Ea+"]",Ua="[^"+On+qa+Ra+Ta+Ea+Ca+"]",Kr="\\ud83c[\\udffb-\\udfff]",Oa="[^"+On+"]",Xr="(?:\\ud83c[\\udde6-\\uddff]){2}",Zr="[\\ud800-\\udbff][\\udc00-\\udfff]",We="["+Ca+"]",Fa="\\u200d",La="(?:"+ka+"|"+Ua+")",Ku="(?:"+We+"|"+Ua+")",Da="(?:['’](?:d|ll|m|re|s|t|ve))?",Ha="(?:['’](?:D|LL|M|RE|S|T|VE))?",Pa="(?:"+Fn+"|"+Kr+")?",ja="["+Ma+"]?",Na=ja+Pa+"(?:"+Fa+"(?:"+[Oa,Xr,Zr].join("|")+")"+ja+Pa+")*",Xu="(?:"+[Yu,Xr,Zr].join("|")+")"+Na,Zu="(?:"+[Oa+Fn+"?",Fn,Xr,Zr,Vu].join("|")+")",Ju=RegExp(Gu,"g"),Qu=RegExp(Fn,"g"),Jr=RegExp(Kr+"(?="+Kr+")|"+Zu+Na,"g"),tc=RegExp([We+"?"+ka+"+"+Da+"(?="+[Aa,We,"$"].join("|")+")",Ku+"+"+Ha+"(?="+[Aa,We+La,"$"].join("|")+")",We+"?"+La+"+"+Da,We+"+"+Ha,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ra,Xu].join("|"),"g"),ec=RegExp("["+Fa+On+Ia+Ma+"]"),nc=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,rc=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],sc=-1,at={};at[Hr]=at[Pr]=at[jr]=at[Nr]=at[Br]=at[zr]=at[Wr]=at[$r]=at[Gr]=!0,at[K]=at[ft]=at[fn]=at[Z]=at[ze]=at[_t]=at[jt]=at[te]=at[st]=at[wt]=at[le]=at[un]=at[ee]=at[cn]=at[ln]=!1;var rt={};rt[K]=rt[ft]=rt[fn]=rt[ze]=rt[Z]=rt[_t]=rt[Hr]=rt[Pr]=rt[jr]=rt[Nr]=rt[Br]=rt[st]=rt[wt]=rt[le]=rt[un]=rt[ee]=rt[cn]=rt[kn]=rt[zr]=rt[Wr]=rt[$r]=rt[Gr]=!0,rt[jt]=rt[te]=rt[ln]=!1;var ac={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ic=parseFloat,oc=parseInt,Ba=typeof Mn=="object"&&Mn&&Mn.Object===Object&&Mn,uc=typeof self=="object"&&self&&self.Object===Object&&self,bt=Ba||uc||Function("return this")(),Qr=kr&&!kr.nodeType&&kr,ke=Qr&&Rr&&!Rr.nodeType&&Rr,za=ke&&ke.exports===Qr,ts=za&&Ba.process,Gt=function(){try{var y=ke&&ke.require&&ke.require("util").types;return y||ts&&ts.binding&&ts.binding("util")}catch{}}(),Wa=Gt&&Gt.isArrayBuffer,$a=Gt&&Gt.isDate,Ga=Gt&&Gt.isMap,Va=Gt&&Gt.isRegExp,Ya=Gt&&Gt.isSet,Ka=Gt&&Gt.isTypedArray;function Nt(y,S,b){switch(b.length){case 0:return y.call(S);case 1:return y.call(S,b[0]);case 2:return y.call(S,b[0],b[1]);case 3:return y.call(S,b[0],b[1],b[2])}return y.apply(S,b)}function cc(y,S,b,A){for(var z=-1,J=y==null?0:y.length;++z<J;){var yt=y[z];S(A,yt,b(yt),y)}return A}function Vt(y,S){for(var b=-1,A=y==null?0:y.length;++b<A&&S(y[b],b,y)!==!1;);return y}function lc(y,S){for(var b=y==null?0:y.length;b--&&S(y[b],b,y)!==!1;);return y}function Xa(y,S){for(var b=-1,A=y==null?0:y.length;++b<A;)if(!S(y[b],b,y))return!1;return!0}function we(y,S){for(var b=-1,A=y==null?0:y.length,z=0,J=[];++b<A;){var yt=y[b];S(yt,b,y)&&(J[z++]=yt)}return J}function Ln(y,S){return!(y==null||!y.length)&&$e(y,S,0)>-1}function es(y,S,b){for(var A=-1,z=y==null?0:y.length;++A<z;)if(b(S,y[A]))return!0;return!1}function ut(y,S){for(var b=-1,A=y==null?0:y.length,z=Array(A);++b<A;)z[b]=S(y[b],b,y);return z}function xe(y,S){for(var b=-1,A=S.length,z=y.length;++b<A;)y[z+b]=S[b];return y}function ns(y,S,b,A){var z=-1,J=y==null?0:y.length;for(A&&J&&(b=y[++z]);++z<J;)b=S(b,y[z],z,y);return b}function fc(y,S,b,A){var z=y==null?0:y.length;for(A&&z&&(b=y[--z]);z--;)b=S(b,y[z],z,y);return b}function rs(y,S){for(var b=-1,A=y==null?0:y.length;++b<A;)if(S(y[b],b,y))return!0;return!1}var dc=ss("length");function Za(y,S,b){var A;return b(y,function(z,J,yt){if(S(z,J,yt))return A=J,!1}),A}function Dn(y,S,b,A){for(var z=y.length,J=b+(A?1:-1);A?J--:++J<z;)if(S(y[J],J,y))return J;return-1}function $e(y,S,b){return S==S?function(A,z,J){for(var yt=J-1,ae=A.length;++yt<ae;)if(A[yt]===z)return yt;return-1}(y,S,b):Dn(y,Ja,b)}function hc(y,S,b,A){for(var z=b-1,J=y.length;++z<J;)if(A(y[z],S))return z;return-1}function Ja(y){return y!=y}function Qa(y,S){var b=y==null?0:y.length;return b?is(y,S)/b:M}function ss(y){return function(S){return S==null?s:S[y]}}function as(y){return function(S){return y==null?s:y[S]}}function ti(y,S,b,A,z){return z(y,function(J,yt,ae){b=A?(A=!1,J):S(b,J,yt,ae)}),b}function is(y,S){for(var b,A=-1,z=y.length;++A<z;){var J=S(y[A]);J!==s&&(b=b===s?J:b+J)}return b}function os(y,S){for(var b=-1,A=Array(y);++b<y;)A[b]=S(b);return A}function ei(y){return y&&y.slice(0,ai(y)+1).replace(Yr,"")}function Bt(y){return function(S){return y(S)}}function us(y,S){return ut(S,function(b){return y[b]})}function dn(y,S){return y.has(S)}function ni(y,S){for(var b=-1,A=y.length;++b<A&&$e(S,y[b],0)>-1;);return b}function ri(y,S){for(var b=y.length;b--&&$e(S,y[b],0)>-1;);return b}var pc=as({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),gc=as({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function _c(y){return"\\"+ac[y]}function Ge(y){return ec.test(y)}function cs(y){var S=-1,b=Array(y.size);return y.forEach(function(A,z){b[++S]=[z,A]}),b}function si(y,S){return function(b){return y(S(b))}}function Se(y,S){for(var b=-1,A=y.length,z=0,J=[];++b<A;){var yt=y[b];yt!==S&&yt!==c||(y[b]=c,J[z++]=b)}return J}function Hn(y){var S=-1,b=Array(y.size);return y.forEach(function(A){b[++S]=A}),b}function yc(y){var S=-1,b=Array(y.size);return y.forEach(function(A){b[++S]=[A,A]}),b}function Ve(y){return Ge(y)?function(S){for(var b=Jr.lastIndex=0;Jr.test(S);)++b;return b}(y):dc(y)}function ne(y){return Ge(y)?function(S){return S.match(Jr)||[]}(y):function(S){return S.split("")}(y)}function ai(y){for(var S=y.length;S--&&Ru.test(y.charAt(S)););return S}var mc=as({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Ye=function y(S){var b,A=(S=S==null?bt:Ye.defaults(bt.Object(),S,Ye.pick(bt,rc))).Array,z=S.Date,J=S.Error,yt=S.Function,ae=S.Math,it=S.Object,ls=S.RegExp,vc=S.String,Yt=S.TypeError,Pn=A.prototype,wc=yt.prototype,Ke=it.prototype,jn=S["__core-js_shared__"],Nn=wc.toString,nt=Ke.hasOwnProperty,xc=0,ii=(b=/[^.]+$/.exec(jn&&jn.keys&&jn.keys.IE_PROTO||""))?"Symbol(src)_1."+b:"",Bn=Ke.toString,Sc=Nn.call(it),bc=bt._,Ic=ls("^"+Nn.call(nt).replace(Vr,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),zn=za?S.Buffer:s,be=S.Symbol,Wn=S.Uint8Array,oi=zn?zn.allocUnsafe:s,$n=si(it.getPrototypeOf,it),ui=it.create,ci=Ke.propertyIsEnumerable,Gn=Pn.splice,li=be?be.isConcatSpreadable:s,hn=be?be.iterator:s,Ue=be?be.toStringTag:s,Vn=function(){try{var t=He(it,"defineProperty");return t({},"",{}),t}catch{}}(),Tc=S.clearTimeout!==bt.clearTimeout&&S.clearTimeout,Ec=z&&z.now!==bt.Date.now&&z.now,Cc=S.setTimeout!==bt.setTimeout&&S.setTimeout,Yn=ae.ceil,Kn=ae.floor,fs=it.getOwnPropertySymbols,Mc=zn?zn.isBuffer:s,fi=S.isFinite,qc=Pn.join,Ac=si(it.keys,it),mt=ae.max,Ct=ae.min,Rc=z.now,kc=S.parseInt,di=ae.random,Uc=Pn.reverse,ds=He(S,"DataView"),pn=He(S,"Map"),hs=He(S,"Promise"),Xe=He(S,"Set"),gn=He(S,"WeakMap"),_n=He(it,"create"),Xn=gn&&new gn,Ze={},Oc=Pe(ds),Fc=Pe(pn),Lc=Pe(hs),Dc=Pe(Xe),Hc=Pe(gn),Zn=be?be.prototype:s,yn=Zn?Zn.valueOf:s,hi=Zn?Zn.toString:s;function u(t){if(dt(t)&&!$(t)&&!(t instanceof X)){if(t instanceof Kt)return t;if(nt.call(t,"__wrapped__"))return go(t)}return new Kt(t)}var Je=function(){function t(){}return function(e){if(!ct(e))return{};if(ui)return ui(e);t.prototype=e;var n=new t;return t.prototype=s,n}}();function Jn(){}function Kt(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=s}function X(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=k,this.__views__=[]}function Oe(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var a=t[e];this.set(a[0],a[1])}}function fe(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var a=t[e];this.set(a[0],a[1])}}function de(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var a=t[e];this.set(a[0],a[1])}}function Fe(t){var e=-1,n=t==null?0:t.length;for(this.__data__=new de;++e<n;)this.add(t[e])}function re(t){var e=this.__data__=new fe(t);this.size=e.size}function pi(t,e){var n=$(t),a=!n&&je(t),o=!n&&!a&&Me(t),l=!n&&!a&&!o&&nn(t),d=n||a||o||l,p=d?os(t.length,vc):[],_=p.length;for(var w in t)!e&&!nt.call(t,w)||d&&(w=="length"||o&&(w=="offset"||w=="parent")||l&&(w=="buffer"||w=="byteLength"||w=="byteOffset")||_e(w,_))||p.push(w);return p}function gi(t){var e=t.length;return e?t[Is(0,e-1)]:s}function Pc(t,e){return dr(Lt(t),Le(e,0,t.length))}function jc(t){return dr(Lt(t))}function ps(t,e,n){(n!==s&&!se(t[e],n)||n===s&&!(e in t))&&he(t,e,n)}function mn(t,e,n){var a=t[e];nt.call(t,e)&&se(a,n)&&(n!==s||e in t)||he(t,e,n)}function Qn(t,e){for(var n=t.length;n--;)if(se(t[n][0],e))return n;return-1}function Nc(t,e,n,a){return Ie(t,function(o,l,d){e(a,o,n(o),d)}),a}function _i(t,e){return t&&oe(e,xt(e),t)}function he(t,e,n){e=="__proto__"&&Vn?Vn(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function gs(t,e){for(var n=-1,a=e.length,o=A(a),l=t==null;++n<a;)o[n]=l?s:Ks(t,e[n]);return o}function Le(t,e,n){return t==t&&(n!==s&&(t=t<=n?t:n),e!==s&&(t=t>=e?t:e)),t}function Xt(t,e,n,a,o,l){var d,p=1&e,_=2&e,w=4&e;if(n&&(d=o?n(t,a,o,l):n(t)),d!==s)return d;if(!ct(t))return t;var v=$(t);if(v){if(d=function(x){var E=x.length,P=new x.constructor(E);return E&&typeof x[0]=="string"&&nt.call(x,"index")&&(P.index=x.index,P.input=x.input),P}(t),!p)return Lt(t,d)}else{var I=Mt(t),R=I==te||I==ve;if(Me(t))return ji(t,p);if(I==le||I==K||R&&!o){if(d=_||R?{}:ao(t),!p)return _?function(x,E){return oe(x,ro(x),E)}(t,function(x,E){return x&&oe(E,Ht(E),x)}(d,t)):function(x,E){return oe(x,Hs(x),E)}(t,_i(d,t))}else{if(!rt[I])return o?t:{};d=function(x,E,P){var q,W=x.constructor;switch(E){case fn:return Rs(x);case Z:case _t:return new W(+x);case ze:return function(B,Q){var F=Q?Rs(B.buffer):B.buffer;return new B.constructor(F,B.byteOffset,B.byteLength)}(x,P);case Hr:case Pr:case jr:case Nr:case Br:case zr:case Wr:case $r:case Gr:return Ni(x,P);case st:return new W;case wt:case cn:return new W(x);case un:return function(B){var Q=new B.constructor(B.source,ba.exec(B));return Q.lastIndex=B.lastIndex,Q}(x);case ee:return new W;case kn:return q=x,yn?it(yn.call(q)):{}}}(t,I,p)}}l||(l=new re);var U=l.get(t);if(U)return U;l.set(t,d),Uo(t)?t.forEach(function(x){d.add(Xt(x,e,n,x,t,l))}):Ro(t)&&t.forEach(function(x,E){d.set(E,Xt(x,e,n,E,t,l))});var D=v?s:(w?_?Fs:Os:_?Ht:xt)(t);return Vt(D||t,function(x,E){D&&(x=t[E=x]),mn(d,E,Xt(x,e,n,E,t,l))}),d}function yi(t,e,n){var a=n.length;if(t==null)return!a;for(t=it(t);a--;){var o=n[a],l=e[o],d=t[o];if(d===s&&!(o in t)||!l(d))return!1}return!0}function mi(t,e,n){if(typeof t!="function")throw new Yt(r);return Tn(function(){t.apply(s,n)},e)}function vn(t,e,n,a){var o=-1,l=Ln,d=!0,p=t.length,_=[],w=e.length;if(!p)return _;n&&(e=ut(e,Bt(n))),a?(l=es,d=!1):e.length>=200&&(l=dn,d=!1,e=new Fe(e));t:for(;++o<p;){var v=t[o],I=n==null?v:n(v);if(v=a||v!==0?v:0,d&&I==I){for(var R=w;R--;)if(e[R]===I)continue t;_.push(v)}else l(e,I,a)||_.push(v)}return _}u.templateSettings={escape:Tu,evaluate:Eu,interpolate:Sa,variable:"",imports:{_:u}},u.prototype=Jn.prototype,u.prototype.constructor=u,Kt.prototype=Je(Jn.prototype),Kt.prototype.constructor=Kt,X.prototype=Je(Jn.prototype),X.prototype.constructor=X,Oe.prototype.clear=function(){this.__data__=_n?_n(null):{},this.size=0},Oe.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Oe.prototype.get=function(t){var e=this.__data__;if(_n){var n=e[t];return n===i?s:n}return nt.call(e,t)?e[t]:s},Oe.prototype.has=function(t){var e=this.__data__;return _n?e[t]!==s:nt.call(e,t)},Oe.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=_n&&e===s?i:e,this},fe.prototype.clear=function(){this.__data__=[],this.size=0},fe.prototype.delete=function(t){var e=this.__data__,n=Qn(e,t);return!(n<0||(n==e.length-1?e.pop():Gn.call(e,n,1),--this.size,0))},fe.prototype.get=function(t){var e=this.__data__,n=Qn(e,t);return n<0?s:e[n][1]},fe.prototype.has=function(t){return Qn(this.__data__,t)>-1},fe.prototype.set=function(t,e){var n=this.__data__,a=Qn(n,t);return a<0?(++this.size,n.push([t,e])):n[a][1]=e,this},de.prototype.clear=function(){this.size=0,this.__data__={hash:new Oe,map:new(pn||fe),string:new Oe}},de.prototype.delete=function(t){var e=fr(this,t).delete(t);return this.size-=e?1:0,e},de.prototype.get=function(t){return fr(this,t).get(t)},de.prototype.has=function(t){return fr(this,t).has(t)},de.prototype.set=function(t,e){var n=fr(this,t),a=n.size;return n.set(t,e),this.size+=n.size==a?0:1,this},Fe.prototype.add=Fe.prototype.push=function(t){return this.__data__.set(t,i),this},Fe.prototype.has=function(t){return this.__data__.has(t)},re.prototype.clear=function(){this.__data__=new fe,this.size=0},re.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},re.prototype.get=function(t){return this.__data__.get(t)},re.prototype.has=function(t){return this.__data__.has(t)},re.prototype.set=function(t,e){var n=this.__data__;if(n instanceof fe){var a=n.__data__;if(!pn||a.length<199)return a.push([t,e]),this.size=++n.size,this;n=this.__data__=new de(a)}return n.set(t,e),this.size=n.size,this};var Ie=$i(ie),vi=$i(ys,!0);function Bc(t,e){var n=!0;return Ie(t,function(a,o,l){return n=!!e(a,o,l)}),n}function tr(t,e,n){for(var a=-1,o=t.length;++a<o;){var l=t[a],d=e(l);if(d!=null&&(p===s?d==d&&!Wt(d):n(d,p)))var p=d,_=l}return _}function wi(t,e){var n=[];return Ie(t,function(a,o,l){e(a,o,l)&&n.push(a)}),n}function It(t,e,n,a,o){var l=-1,d=t.length;for(n||(n=tl),o||(o=[]);++l<d;){var p=t[l];e>0&&n(p)?e>1?It(p,e-1,n,a,o):xe(o,p):a||(o[o.length]=p)}return o}var _s=Gi(),xi=Gi(!0);function ie(t,e){return t&&_s(t,e,xt)}function ys(t,e){return t&&xi(t,e,xt)}function er(t,e){return we(e,function(n){return ye(t[n])})}function De(t,e){for(var n=0,a=(e=Ee(e,t)).length;t!=null&&n<a;)t=t[ue(e[n++])];return n&&n==a?t:s}function Si(t,e,n){var a=e(t);return $(t)?a:xe(a,n(t))}function kt(t){return t==null?t===s?"[object Undefined]":"[object Null]":Ue&&Ue in it(t)?function(e){var n=nt.call(e,Ue),a=e[Ue];try{e[Ue]=s;var o=!0}catch{}var l=Bn.call(e);return o&&(n?e[Ue]=a:delete e[Ue]),l}(t):function(e){return Bn.call(e)}(t)}function ms(t,e){return t>e}function zc(t,e){return t!=null&&nt.call(t,e)}function Wc(t,e){return t!=null&&e in it(t)}function vs(t,e,n){for(var a=n?es:Ln,o=t[0].length,l=t.length,d=l,p=A(l),_=1/0,w=[];d--;){var v=t[d];d&&e&&(v=ut(v,Bt(e))),_=Ct(v.length,_),p[d]=!n&&(e||o>=120&&v.length>=120)?new Fe(d&&v):s}v=t[0];var I=-1,R=p[0];t:for(;++I<o&&w.length<_;){var U=v[I],D=e?e(U):U;if(U=n||U!==0?U:0,!(R?dn(R,D):a(w,D,n))){for(d=l;--d;){var x=p[d];if(!(x?dn(x,D):a(t[d],D,n)))continue t}R&&R.push(D),w.push(U)}}return w}function wn(t,e,n){var a=(t=co(t,e=Ee(e,t)))==null?t:t[ue(Jt(e))];return a==null?s:Nt(a,t,n)}function bi(t){return dt(t)&&kt(t)==K}function xn(t,e,n,a,o){return t===e||(t==null||e==null||!dt(t)&&!dt(e)?t!=t&&e!=e:function(l,d,p,_,w,v){var I=$(l),R=$(d),U=I?ft:Mt(l),D=R?ft:Mt(d),x=(U=U==K?le:U)==le,E=(D=D==K?le:D)==le,P=U==D;if(P&&Me(l)){if(!Me(d))return!1;I=!0,x=!1}if(P&&!x)return v||(v=new re),I||nn(l)?no(l,d,p,_,w,v):function(F,j,vt,pt,Ot,ot,qt){switch(vt){case ze:if(F.byteLength!=j.byteLength||F.byteOffset!=j.byteOffset)return!1;F=F.buffer,j=j.buffer;case fn:return!(F.byteLength!=j.byteLength||!ot(new Wn(F),new Wn(j)));case Z:case _t:case wt:return se(+F,+j);case jt:return F.name==j.name&&F.message==j.message;case un:case cn:return F==j+"";case st:var ce=cs;case ee:var qe=1&pt;if(ce||(ce=Hn),F.size!=j.size&&!qe)return!1;var xr=qt.get(F);if(xr)return xr==j;pt|=2,qt.set(F,j);var aa=no(ce(F),ce(j),pt,Ot,ot,qt);return qt.delete(F),aa;case kn:if(yn)return yn.call(F)==yn.call(j)}return!1}(l,d,U,p,_,w,v);if(!(1&p)){var q=x&&nt.call(l,"__wrapped__"),W=E&&nt.call(d,"__wrapped__");if(q||W){var B=q?l.value():l,Q=W?d.value():d;return v||(v=new re),w(B,Q,p,_,v)}}return!!P&&(v||(v=new re),function(F,j,vt,pt,Ot,ot){var qt=1&vt,ce=Os(F),qe=ce.length,xr=Os(j),aa=xr.length;if(qe!=aa&&!qt)return!1;for(var Sr=qe;Sr--;){var Ne=ce[Sr];if(!(qt?Ne in j:nt.call(j,Ne)))return!1}var Vo=ot.get(F),Yo=ot.get(j);if(Vo&&Yo)return Vo==j&&Yo==F;var br=!0;ot.set(F,j),ot.set(j,F);for(var ia=qt;++Sr<qe;){var Ir=F[Ne=ce[Sr]],Tr=j[Ne];if(pt)var Ko=qt?pt(Tr,Ir,Ne,j,F,ot):pt(Ir,Tr,Ne,F,j,ot);if(!(Ko===s?Ir===Tr||Ot(Ir,Tr,vt,pt,ot):Ko)){br=!1;break}ia||(ia=Ne=="constructor")}if(br&&!ia){var Er=F.constructor,Cr=j.constructor;Er==Cr||!("constructor"in F)||!("constructor"in j)||typeof Er=="function"&&Er instanceof Er&&typeof Cr=="function"&&Cr instanceof Cr||(br=!1)}return ot.delete(F),ot.delete(j),br}(l,d,p,_,w,v))}(t,e,n,a,xn,o))}function ws(t,e,n,a){var o=n.length,l=o,d=!a;if(t==null)return!l;for(t=it(t);o--;){var p=n[o];if(d&&p[2]?p[1]!==t[p[0]]:!(p[0]in t))return!1}for(;++o<l;){var _=(p=n[o])[0],w=t[_],v=p[1];if(d&&p[2]){if(w===s&&!(_ in t))return!1}else{var I=new re;if(a)var R=a(w,v,_,t,e,I);if(!(R===s?xn(v,w,3,a,I):R))return!1}}return!0}function Ii(t){return!(!ct(t)||(e=t,ii&&ii in e))&&(ye(t)?Ic:Nu).test(Pe(t));var e}function Ti(t){return typeof t=="function"?t:t==null?Pt:typeof t=="object"?$(t)?Mi(t[0],t[1]):Ci(t):Go(t)}function xs(t){if(!In(t))return Ac(t);var e=[];for(var n in it(t))nt.call(t,n)&&n!="constructor"&&e.push(n);return e}function $c(t){if(!ct(t))return function(o){var l=[];if(o!=null)for(var d in it(o))l.push(d);return l}(t);var e=In(t),n=[];for(var a in t)(a!="constructor"||!e&&nt.call(t,a))&&n.push(a);return n}function Ss(t,e){return t<e}function Ei(t,e){var n=-1,a=Dt(t)?A(t.length):[];return Ie(t,function(o,l,d){a[++n]=e(o,l,d)}),a}function Ci(t){var e=Ds(t);return e.length==1&&e[0][2]?oo(e[0][0],e[0][1]):function(n){return n===t||ws(n,t,e)}}function Mi(t,e){return Ps(t)&&io(e)?oo(ue(t),e):function(n){var a=Ks(n,t);return a===s&&a===e?Xs(n,t):xn(e,a,3)}}function nr(t,e,n,a,o){t!==e&&_s(e,function(l,d){if(o||(o=new re),ct(l))(function(_,w,v,I,R,U,D){var x=Ns(_,v),E=Ns(w,v),P=D.get(E);if(P)ps(_,v,P);else{var q=U?U(x,E,v+"",_,w,D):s,W=q===s;if(W){var B=$(E),Q=!B&&Me(E),F=!B&&!Q&&nn(E);q=E,B||Q||F?$(x)?q=x:ht(x)?q=Lt(x):Q?(W=!1,q=ji(E,!0)):F?(W=!1,q=Ni(E,!0)):q=[]:En(E)||je(E)?(q=x,je(x)?q=Lo(x):ct(x)&&!ye(x)||(q=ao(E))):W=!1}W&&(D.set(E,q),R(q,E,I,U,D),D.delete(E)),ps(_,v,q)}})(t,e,d,n,nr,a,o);else{var p=a?a(Ns(t,d),l,d+"",t,e,o):s;p===s&&(p=l),ps(t,d,p)}},Ht)}function qi(t,e){var n=t.length;if(n)return _e(e+=e<0?n:0,n)?t[e]:s}function Ai(t,e,n){e=e.length?ut(e,function(l){return $(l)?function(d){return De(d,l.length===1?l[0]:l)}:l}):[Pt];var a=-1;e=ut(e,Bt(H()));var o=Ei(t,function(l,d,p){var _=ut(e,function(w){return w(l)});return{criteria:_,index:++a,value:l}});return function(l,d){var p=l.length;for(l.sort(d);p--;)l[p]=l[p].value;return l}(o,function(l,d){return function(p,_,w){for(var v=-1,I=p.criteria,R=_.criteria,U=I.length,D=w.length;++v<U;){var x=Bi(I[v],R[v]);if(x)return v>=D?x:x*(w[v]=="desc"?-1:1)}return p.index-_.index}(l,d,n)})}function Ri(t,e,n){for(var a=-1,o=e.length,l={};++a<o;){var d=e[a],p=De(t,d);n(p,d)&&Sn(l,Ee(d,t),p)}return l}function bs(t,e,n,a){var o=a?hc:$e,l=-1,d=e.length,p=t;for(t===e&&(e=Lt(e)),n&&(p=ut(t,Bt(n)));++l<d;)for(var _=0,w=e[l],v=n?n(w):w;(_=o(p,v,_,a))>-1;)p!==t&&Gn.call(p,_,1),Gn.call(t,_,1);return t}function ki(t,e){for(var n=t?e.length:0,a=n-1;n--;){var o=e[n];if(n==a||o!==l){var l=o;_e(o)?Gn.call(t,o,1):Cs(t,o)}}return t}function Is(t,e){return t+Kn(di()*(e-t+1))}function Ts(t,e){var n="";if(!t||e<1||e>G)return n;do e%2&&(n+=t),(e=Kn(e/2))&&(t+=t);while(e);return n}function Y(t,e){return Bs(uo(t,e,Pt),t+"")}function Gc(t){return gi(rn(t))}function Vc(t,e){var n=rn(t);return dr(n,Le(e,0,n.length))}function Sn(t,e,n,a){if(!ct(t))return t;for(var o=-1,l=(e=Ee(e,t)).length,d=l-1,p=t;p!=null&&++o<l;){var _=ue(e[o]),w=n;if(_==="__proto__"||_==="constructor"||_==="prototype")return t;if(o!=d){var v=p[_];(w=a?a(v,_,p):s)===s&&(w=ct(v)?v:_e(e[o+1])?[]:{})}mn(p,_,w),p=p[_]}return t}var Ui=Xn?function(t,e){return Xn.set(t,e),t}:Pt,Yc=Vn?function(t,e){return Vn(t,"toString",{configurable:!0,enumerable:!1,value:Js(e),writable:!0})}:Pt;function Kc(t){return dr(rn(t))}function Zt(t,e,n){var a=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(n=n>o?o:n)<0&&(n+=o),o=e>n?0:n-e>>>0,e>>>=0;for(var l=A(o);++a<o;)l[a]=t[a+e];return l}function Xc(t,e){var n;return Ie(t,function(a,o,l){return!(n=e(a,o,l))}),!!n}function rr(t,e,n){var a=0,o=t==null?a:t.length;if(typeof e=="number"&&e==e&&o<=2147483647){for(;a<o;){var l=a+o>>>1,d=t[l];d!==null&&!Wt(d)&&(n?d<=e:d<e)?a=l+1:o=l}return o}return Es(t,e,Pt,n)}function Es(t,e,n,a){var o=0,l=t==null?0:t.length;if(l===0)return 0;for(var d=(e=n(e))!=e,p=e===null,_=Wt(e),w=e===s;o<l;){var v=Kn((o+l)/2),I=n(t[v]),R=I!==s,U=I===null,D=I==I,x=Wt(I);if(d)var E=a||D;else E=w?D&&(a||R):p?D&&R&&(a||!U):_?D&&R&&!U&&(a||!x):!U&&!x&&(a?I<=e:I<e);E?o=v+1:l=v}return Ct(l,4294967294)}function Oi(t,e){for(var n=-1,a=t.length,o=0,l=[];++n<a;){var d=t[n],p=e?e(d):d;if(!n||!se(p,_)){var _=p;l[o++]=d===0?0:d}}return l}function Fi(t){return typeof t=="number"?t:Wt(t)?M:+t}function zt(t){if(typeof t=="string")return t;if($(t))return ut(t,zt)+"";if(Wt(t))return hi?hi.call(t):"";var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function Te(t,e,n){var a=-1,o=Ln,l=t.length,d=!0,p=[],_=p;if(n)d=!1,o=es;else if(l>=200){var w=e?null:Jc(t);if(w)return Hn(w);d=!1,o=dn,_=new Fe}else _=e?[]:p;t:for(;++a<l;){var v=t[a],I=e?e(v):v;if(v=n||v!==0?v:0,d&&I==I){for(var R=_.length;R--;)if(_[R]===I)continue t;e&&_.push(I),p.push(v)}else o(_,I,n)||(_!==p&&_.push(I),p.push(v))}return p}function Cs(t,e){return(t=co(t,e=Ee(e,t)))==null||delete t[ue(Jt(e))]}function Li(t,e,n,a){return Sn(t,e,n(De(t,e)),a)}function sr(t,e,n,a){for(var o=t.length,l=a?o:-1;(a?l--:++l<o)&&e(t[l],l,t););return n?Zt(t,a?0:l,a?l+1:o):Zt(t,a?l+1:0,a?o:l)}function Di(t,e){var n=t;return n instanceof X&&(n=n.value()),ns(e,function(a,o){return o.func.apply(o.thisArg,xe([a],o.args))},n)}function Ms(t,e,n){var a=t.length;if(a<2)return a?Te(t[0]):[];for(var o=-1,l=A(a);++o<a;)for(var d=t[o],p=-1;++p<a;)p!=o&&(l[o]=vn(l[o]||d,t[p],e,n));return Te(It(l,1),e,n)}function Hi(t,e,n){for(var a=-1,o=t.length,l=e.length,d={};++a<o;){var p=a<l?e[a]:s;n(d,t[a],p)}return d}function qs(t){return ht(t)?t:[]}function As(t){return typeof t=="function"?t:Pt}function Ee(t,e){return $(t)?t:Ps(t,e)?[t]:po(et(t))}var Zc=Y;function Ce(t,e,n){var a=t.length;return n=n===s?a:n,!e&&n>=a?t:Zt(t,e,n)}var Pi=Tc||function(t){return bt.clearTimeout(t)};function ji(t,e){if(e)return t.slice();var n=t.length,a=oi?oi(n):new t.constructor(n);return t.copy(a),a}function Rs(t){var e=new t.constructor(t.byteLength);return new Wn(e).set(new Wn(t)),e}function Ni(t,e){var n=e?Rs(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}function Bi(t,e){if(t!==e){var n=t!==s,a=t===null,o=t==t,l=Wt(t),d=e!==s,p=e===null,_=e==e,w=Wt(e);if(!p&&!w&&!l&&t>e||l&&d&&_&&!p&&!w||a&&d&&_||!n&&_||!o)return 1;if(!a&&!l&&!w&&t<e||w&&n&&o&&!a&&!l||p&&n&&o||!d&&o||!_)return-1}return 0}function zi(t,e,n,a){for(var o=-1,l=t.length,d=n.length,p=-1,_=e.length,w=mt(l-d,0),v=A(_+w),I=!a;++p<_;)v[p]=e[p];for(;++o<d;)(I||o<l)&&(v[n[o]]=t[o]);for(;w--;)v[p++]=t[o++];return v}function Wi(t,e,n,a){for(var o=-1,l=t.length,d=-1,p=n.length,_=-1,w=e.length,v=mt(l-p,0),I=A(v+w),R=!a;++o<v;)I[o]=t[o];for(var U=o;++_<w;)I[U+_]=e[_];for(;++d<p;)(R||o<l)&&(I[U+n[d]]=t[o++]);return I}function Lt(t,e){var n=-1,a=t.length;for(e||(e=A(a));++n<a;)e[n]=t[n];return e}function oe(t,e,n,a){var o=!n;n||(n={});for(var l=-1,d=e.length;++l<d;){var p=e[l],_=a?a(n[p],t[p],p,n,t):s;_===s&&(_=t[p]),o?he(n,p,_):mn(n,p,_)}return n}function ar(t,e){return function(n,a){var o=$(n)?cc:Nc,l=e?e():{};return o(n,t,H(a,2),l)}}function Qe(t){return Y(function(e,n){var a=-1,o=n.length,l=o>1?n[o-1]:s,d=o>2?n[2]:s;for(l=t.length>3&&typeof l=="function"?(o--,l):s,d&&Ut(n[0],n[1],d)&&(l=o<3?s:l,o=1),e=it(e);++a<o;){var p=n[a];p&&t(e,p,a,l)}return e})}function $i(t,e){return function(n,a){if(n==null)return n;if(!Dt(n))return t(n,a);for(var o=n.length,l=e?o:-1,d=it(n);(e?l--:++l<o)&&a(d[l],l,d)!==!1;);return n}}function Gi(t){return function(e,n,a){for(var o=-1,l=it(e),d=a(e),p=d.length;p--;){var _=d[t?p:++o];if(n(l[_],_,l)===!1)break}return e}}function Vi(t){return function(e){var n=Ge(e=et(e))?ne(e):s,a=n?n[0]:e.charAt(0),o=n?Ce(n,1).join(""):e.slice(1);return a[t]()+o}}function tn(t){return function(e){return ns(Wo(zo(e).replace(Ju,"")),t,"")}}function bn(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=Je(t.prototype),a=t.apply(n,e);return ct(a)?a:n}}function Yi(t){return function(e,n,a){var o=it(e);if(!Dt(e)){var l=H(n,3);e=xt(e),n=function(p){return l(o[p],p,o)}}var d=t(e,n,a);return d>-1?o[l?e[d]:d]:s}}function Ki(t){return ge(function(e){var n=e.length,a=n,o=Kt.prototype.thru;for(t&&e.reverse();a--;){var l=e[a];if(typeof l!="function")throw new Yt(r);if(o&&!d&&lr(l)=="wrapper")var d=new Kt([],!0)}for(a=d?a:n;++a<n;){var p=lr(l=e[a]),_=p=="wrapper"?Ls(l):s;d=_&&js(_[0])&&_[1]==424&&!_[4].length&&_[9]==1?d[lr(_[0])].apply(d,_[3]):l.length==1&&js(l)?d[p]():d.thru(l)}return function(){var w=arguments,v=w[0];if(d&&w.length==1&&$(v))return d.plant(v).value();for(var I=0,R=n?e[I].apply(this,w):v;++I<n;)R=e[I].call(this,R);return R}})}function ir(t,e,n,a,o,l,d,p,_,w){var v=e&T,I=1&e,R=2&e,U=24&e,D=512&e,x=R?s:bn(t);return function E(){for(var P=arguments.length,q=A(P),W=P;W--;)q[W]=arguments[W];if(U)var B=en(E),Q=function(pt,Ot){for(var ot=pt.length,qt=0;ot--;)pt[ot]===Ot&&++qt;return qt}(q,B);if(a&&(q=zi(q,a,o,U)),l&&(q=Wi(q,l,d,U)),P-=Q,U&&P<w){var F=Se(q,B);return Ji(t,e,ir,E.placeholder,n,q,F,p,_,w-P)}var j=I?n:this,vt=R?j[t]:t;return P=q.length,p?q=function(pt,Ot){for(var ot=pt.length,qt=Ct(Ot.length,ot),ce=Lt(pt);qt--;){var qe=Ot[qt];pt[qt]=_e(qe,ot)?ce[qe]:s}return pt}(q,p):D&&P>1&&q.reverse(),v&&_<P&&(q.length=_),this&&this!==bt&&this instanceof E&&(vt=x||bn(vt)),vt.apply(j,q)}}function Xi(t,e){return function(n,a){return function(o,l,d,p){return ie(o,function(_,w,v){l(p,d(_),w,v)}),p}(n,t,e(a),{})}}function or(t,e){return function(n,a){var o;if(n===s&&a===s)return e;if(n!==s&&(o=n),a!==s){if(o===s)return a;typeof n=="string"||typeof a=="string"?(n=zt(n),a=zt(a)):(n=Fi(n),a=Fi(a)),o=t(n,a)}return o}}function ks(t){return ge(function(e){return e=ut(e,Bt(H())),Y(function(n){var a=this;return t(e,function(o){return Nt(o,a,n)})})})}function ur(t,e){var n=(e=e===s?" ":zt(e)).length;if(n<2)return n?Ts(e,t):e;var a=Ts(e,Yn(t/Ve(e)));return Ge(e)?Ce(ne(a),0,t).join(""):a.slice(0,t)}function Zi(t){return function(e,n,a){return a&&typeof a!="number"&&Ut(e,n,a)&&(n=a=s),e=me(e),n===s?(n=e,e=0):n=me(n),function(o,l,d,p){for(var _=-1,w=mt(Yn((l-o)/(d||1)),0),v=A(w);w--;)v[p?w:++_]=o,o+=d;return v}(e,n,a=a===s?e<n?1:-1:me(a),t)}}function cr(t){return function(e,n){return typeof e=="string"&&typeof n=="string"||(e=Qt(e),n=Qt(n)),t(e,n)}}function Ji(t,e,n,a,o,l,d,p,_,w){var v=8&e;e|=v?h:g,4&(e&=~(v?g:h))||(e&=-4);var I=[t,e,o,v?l:s,v?d:s,v?s:l,v?s:d,p,_,w],R=n.apply(s,I);return js(t)&&lo(R,I),R.placeholder=a,fo(R,t,e)}function Us(t){var e=ae[t];return function(n,a){if(n=Qt(n),(a=a==null?0:Ct(V(a),292))&&fi(n)){var o=(et(n)+"e").split("e");return+((o=(et(e(o[0]+"e"+(+o[1]+a)))+"e").split("e"))[0]+"e"+(+o[1]-a))}return e(n)}}var Jc=Xe&&1/Hn(new Xe([,-0]))[1]==L?function(t){return new Xe(t)}:ea;function Qi(t){return function(e){var n=Mt(e);return n==st?cs(e):n==ee?yc(e):function(a,o){return ut(o,function(l){return[l,a[l]]})}(e,t(e))}}function pe(t,e,n,a,o,l,d,p){var _=2&e;if(!_&&typeof t!="function")throw new Yt(r);var w=a?a.length:0;if(w||(e&=-97,a=o=s),d=d===s?d:mt(V(d),0),p=p===s?p:V(p),w-=o?o.length:0,e&g){var v=a,I=o;a=o=s}var R=_?s:Ls(t),U=[t,e,n,a,o,v,I,l,d,p];if(R&&function(x,E){var P=x[1],q=E[1],W=P|q,B=W<131,Q=q==T&&P==8||q==T&&P==C&&x[7].length<=E[8]||q==384&&E[7].length<=E[8]&&P==8;if(!B&&!Q)return x;1&q&&(x[2]=E[2],W|=1&P?0:4);var F=E[3];if(F){var j=x[3];x[3]=j?zi(j,F,E[4]):F,x[4]=j?Se(x[3],c):E[4]}(F=E[5])&&(j=x[5],x[5]=j?Wi(j,F,E[6]):F,x[6]=j?Se(x[5],c):E[6]),(F=E[7])&&(x[7]=F),q&T&&(x[8]=x[8]==null?E[8]:Ct(x[8],E[8])),x[9]==null&&(x[9]=E[9]),x[0]=E[0],x[1]=W}(U,R),t=U[0],e=U[1],n=U[2],a=U[3],o=U[4],!(p=U[9]=U[9]===s?_?0:t.length:mt(U[9]-w,0))&&24&e&&(e&=-25),e&&e!=1)D=e==8||e==f?function(x,E,P){var q=bn(x);return function W(){for(var B=arguments.length,Q=A(B),F=B,j=en(W);F--;)Q[F]=arguments[F];var vt=B<3&&Q[0]!==j&&Q[B-1]!==j?[]:Se(Q,j);return(B-=vt.length)<P?Ji(x,E,ir,W.placeholder,s,Q,vt,s,s,P-B):Nt(this&&this!==bt&&this instanceof W?q:x,this,Q)}}(t,e,p):e!=h&&e!=33||o.length?ir.apply(s,U):function(x,E,P,q){var W=1&E,B=bn(x);return function Q(){for(var F=-1,j=arguments.length,vt=-1,pt=q.length,Ot=A(pt+j),ot=this&&this!==bt&&this instanceof Q?B:x;++vt<pt;)Ot[vt]=q[vt];for(;j--;)Ot[vt++]=arguments[++F];return Nt(ot,W?P:this,Ot)}}(t,e,n,a);else var D=function(x,E,P){var q=1&E,W=bn(x);return function B(){return(this&&this!==bt&&this instanceof B?W:x).apply(q?P:this,arguments)}}(t,e,n);return fo((R?Ui:lo)(D,U),t,e)}function to(t,e,n,a){return t===s||se(t,Ke[n])&&!nt.call(a,n)?e:t}function eo(t,e,n,a,o,l){return ct(t)&&ct(e)&&(l.set(e,t),nr(t,e,s,eo,l),l.delete(e)),t}function Qc(t){return En(t)?s:t}function no(t,e,n,a,o,l){var d=1&n,p=t.length,_=e.length;if(p!=_&&!(d&&_>p))return!1;var w=l.get(t),v=l.get(e);if(w&&v)return w==e&&v==t;var I=-1,R=!0,U=2&n?new Fe:s;for(l.set(t,e),l.set(e,t);++I<p;){var D=t[I],x=e[I];if(a)var E=d?a(x,D,I,e,t,l):a(D,x,I,t,e,l);if(E!==s){if(E)continue;R=!1;break}if(U){if(!rs(e,function(P,q){if(!dn(U,q)&&(D===P||o(D,P,n,a,l)))return U.push(q)})){R=!1;break}}else if(D!==x&&!o(D,x,n,a,l)){R=!1;break}}return l.delete(t),l.delete(e),R}function ge(t){return Bs(uo(t,s,mo),t+"")}function Os(t){return Si(t,xt,Hs)}function Fs(t){return Si(t,Ht,ro)}var Ls=Xn?function(t){return Xn.get(t)}:ea;function lr(t){for(var e=t.name+"",n=Ze[e],a=nt.call(Ze,e)?n.length:0;a--;){var o=n[a],l=o.func;if(l==null||l==t)return o.name}return e}function en(t){return(nt.call(u,"placeholder")?u:t).placeholder}function H(){var t=u.iteratee||Qs;return t=t===Qs?Ti:t,arguments.length?t(arguments[0],arguments[1]):t}function fr(t,e){var n,a,o=t.__data__;return((a=typeof(n=e))=="string"||a=="number"||a=="symbol"||a=="boolean"?n!=="__proto__":n===null)?o[typeof e=="string"?"string":"hash"]:o.map}function Ds(t){for(var e=xt(t),n=e.length;n--;){var a=e[n],o=t[a];e[n]=[a,o,io(o)]}return e}function He(t,e){var n=function(a,o){return a==null?s:a[o]}(t,e);return Ii(n)?n:s}var Hs=fs?function(t){return t==null?[]:(t=it(t),we(fs(t),function(e){return ci.call(t,e)}))}:na,ro=fs?function(t){for(var e=[];t;)xe(e,Hs(t)),t=$n(t);return e}:na,Mt=kt;function so(t,e,n){for(var a=-1,o=(e=Ee(e,t)).length,l=!1;++a<o;){var d=ue(e[a]);if(!(l=t!=null&&n(t,d)))break;t=t[d]}return l||++a!=o?l:!!(o=t==null?0:t.length)&&mr(o)&&_e(d,o)&&($(t)||je(t))}function ao(t){return typeof t.constructor!="function"||In(t)?{}:Je($n(t))}function tl(t){return $(t)||je(t)||!!(li&&t&&t[li])}function _e(t,e){var n=typeof t;return!!(e=e??G)&&(n=="number"||n!="symbol"&&zu.test(t))&&t>-1&&t%1==0&&t<e}function Ut(t,e,n){if(!ct(n))return!1;var a=typeof e;return!!(a=="number"?Dt(n)&&_e(e,n.length):a=="string"&&e in n)&&se(n[e],t)}function Ps(t,e){if($(t))return!1;var n=typeof t;return!(n!="number"&&n!="symbol"&&n!="boolean"&&t!=null&&!Wt(t))||Mu.test(t)||!Cu.test(t)||e!=null&&t in it(e)}function js(t){var e=lr(t),n=u[e];if(typeof n!="function"||!(e in X.prototype))return!1;if(t===n)return!0;var a=Ls(n);return!!a&&t===a[0]}(ds&&Mt(new ds(new ArrayBuffer(1)))!=ze||pn&&Mt(new pn)!=st||hs&&Mt(hs.resolve())!=va||Xe&&Mt(new Xe)!=ee||gn&&Mt(new gn)!=ln)&&(Mt=function(t){var e=kt(t),n=e==le?t.constructor:s,a=n?Pe(n):"";if(a)switch(a){case Oc:return ze;case Fc:return st;case Lc:return va;case Dc:return ee;case Hc:return ln}return e});var el=jn?ye:ra;function In(t){var e=t&&t.constructor;return t===(typeof e=="function"&&e.prototype||Ke)}function io(t){return t==t&&!ct(t)}function oo(t,e){return function(n){return n!=null&&n[t]===e&&(e!==s||t in it(n))}}function uo(t,e,n){return e=mt(e===s?t.length-1:e,0),function(){for(var a=arguments,o=-1,l=mt(a.length-e,0),d=A(l);++o<l;)d[o]=a[e+o];o=-1;for(var p=A(e+1);++o<e;)p[o]=a[o];return p[e]=n(d),Nt(t,this,p)}}function co(t,e){return e.length<2?t:De(t,Zt(e,0,-1))}function Ns(t,e){if((e!=="constructor"||typeof t[e]!="function")&&e!="__proto__")return t[e]}var lo=ho(Ui),Tn=Cc||function(t,e){return bt.setTimeout(t,e)},Bs=ho(Yc);function fo(t,e,n){var a=e+"";return Bs(t,function(o,l){var d=l.length;if(!d)return o;var p=d-1;return l[p]=(d>1?"& ":"")+l[p],l=l.join(d>2?", ":" "),o.replace(ku,`{
/* [wrapped with `+l+`] */
`)}(a,function(o,l){return Vt(N,function(d){var p="_."+d[0];l&d[1]&&!Ln(o,p)&&o.push(p)}),o.sort()}(function(o){var l=o.match(Uu);return l?l[1].split(Ou):[]}(a),n)))}function ho(t){var e=0,n=0;return function(){var a=Rc(),o=16-(a-n);if(n=a,o>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(s,arguments)}}function dr(t,e){var n=-1,a=t.length,o=a-1;for(e=e===s?a:e;++n<e;){var l=Is(n,o),d=t[l];t[l]=t[n],t[n]=d}return t.length=e,t}var po=function(t){var e=_r(t,function(a){return n.size===500&&n.clear(),a}),n=e.cache;return e}(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(qu,function(n,a,o,l){e.push(o?l.replace(Du,"$1"):a||n)}),e});function ue(t){if(typeof t=="string"||Wt(t))return t;var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function Pe(t){if(t!=null){try{return Nn.call(t)}catch{}try{return t+""}catch{}}return""}function go(t){if(t instanceof X)return t.clone();var e=new Kt(t.__wrapped__,t.__chain__);return e.__actions__=Lt(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}var nl=Y(function(t,e){return ht(t)?vn(t,It(e,1,ht,!0)):[]}),rl=Y(function(t,e){var n=Jt(e);return ht(n)&&(n=s),ht(t)?vn(t,It(e,1,ht,!0),H(n,2)):[]}),sl=Y(function(t,e){var n=Jt(e);return ht(n)&&(n=s),ht(t)?vn(t,It(e,1,ht,!0),s,n):[]});function _o(t,e,n){var a=t==null?0:t.length;if(!a)return-1;var o=n==null?0:V(n);return o<0&&(o=mt(a+o,0)),Dn(t,H(e,3),o)}function yo(t,e,n){var a=t==null?0:t.length;if(!a)return-1;var o=a-1;return n!==s&&(o=V(n),o=n<0?mt(a+o,0):Ct(o,a-1)),Dn(t,H(e,3),o,!0)}function mo(t){return t!=null&&t.length?It(t,1):[]}function vo(t){return t&&t.length?t[0]:s}var al=Y(function(t){var e=ut(t,qs);return e.length&&e[0]===t[0]?vs(e):[]}),il=Y(function(t){var e=Jt(t),n=ut(t,qs);return e===Jt(n)?e=s:n.pop(),n.length&&n[0]===t[0]?vs(n,H(e,2)):[]}),ol=Y(function(t){var e=Jt(t),n=ut(t,qs);return(e=typeof e=="function"?e:s)&&n.pop(),n.length&&n[0]===t[0]?vs(n,s,e):[]});function Jt(t){var e=t==null?0:t.length;return e?t[e-1]:s}var ul=Y(wo);function wo(t,e){return t&&t.length&&e&&e.length?bs(t,e):t}var cl=ge(function(t,e){var n=t==null?0:t.length,a=gs(t,e);return ki(t,ut(e,function(o){return _e(o,n)?+o:o}).sort(Bi)),a});function zs(t){return t==null?t:Uc.call(t)}var ll=Y(function(t){return Te(It(t,1,ht,!0))}),fl=Y(function(t){var e=Jt(t);return ht(e)&&(e=s),Te(It(t,1,ht,!0),H(e,2))}),dl=Y(function(t){var e=Jt(t);return e=typeof e=="function"?e:s,Te(It(t,1,ht,!0),s,e)});function Ws(t){if(!t||!t.length)return[];var e=0;return t=we(t,function(n){if(ht(n))return e=mt(n.length,e),!0}),os(e,function(n){return ut(t,ss(n))})}function xo(t,e){if(!t||!t.length)return[];var n=Ws(t);return e==null?n:ut(n,function(a){return Nt(e,s,a)})}var hl=Y(function(t,e){return ht(t)?vn(t,e):[]}),pl=Y(function(t){return Ms(we(t,ht))}),gl=Y(function(t){var e=Jt(t);return ht(e)&&(e=s),Ms(we(t,ht),H(e,2))}),_l=Y(function(t){var e=Jt(t);return e=typeof e=="function"?e:s,Ms(we(t,ht),s,e)}),yl=Y(Ws),ml=Y(function(t){var e=t.length,n=e>1?t[e-1]:s;return n=typeof n=="function"?(t.pop(),n):s,xo(t,n)});function So(t){var e=u(t);return e.__chain__=!0,e}function hr(t,e){return e(t)}var vl=ge(function(t){var e=t.length,n=e?t[0]:0,a=this.__wrapped__,o=function(l){return gs(l,t)};return!(e>1||this.__actions__.length)&&a instanceof X&&_e(n)?((a=a.slice(n,+n+(e?1:0))).__actions__.push({func:hr,args:[o],thisArg:s}),new Kt(a,this.__chain__).thru(function(l){return e&&!l.length&&l.push(s),l})):this.thru(o)}),wl=ar(function(t,e,n){nt.call(t,n)?++t[n]:he(t,n,1)}),xl=Yi(_o),Sl=Yi(yo);function bo(t,e){return($(t)?Vt:Ie)(t,H(e,3))}function Io(t,e){return($(t)?lc:vi)(t,H(e,3))}var bl=ar(function(t,e,n){nt.call(t,n)?t[n].push(e):he(t,n,[e])}),Il=Y(function(t,e,n){var a=-1,o=typeof e=="function",l=Dt(t)?A(t.length):[];return Ie(t,function(d){l[++a]=o?Nt(e,d,n):wn(d,e,n)}),l}),Tl=ar(function(t,e,n){he(t,n,e)});function pr(t,e){return($(t)?ut:Ei)(t,H(e,3))}var El=ar(function(t,e,n){t[n?0:1].push(e)},function(){return[[],[]]}),Cl=Y(function(t,e){if(t==null)return[];var n=e.length;return n>1&&Ut(t,e[0],e[1])?e=[]:n>2&&Ut(e[0],e[1],e[2])&&(e=[e[0]]),Ai(t,It(e,1),[])}),gr=Ec||function(){return bt.Date.now()};function To(t,e,n){return e=n?s:e,e=t&&e==null?t.length:e,pe(t,T,s,s,s,s,e)}function Eo(t,e){var n;if(typeof e!="function")throw new Yt(r);return t=V(t),function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=s),n}}var $s=Y(function(t,e,n){var a=1;if(n.length){var o=Se(n,en($s));a|=h}return pe(t,a,e,n,o)}),Co=Y(function(t,e,n){var a=3;if(n.length){var o=Se(n,en(Co));a|=h}return pe(e,a,t,n,o)});function Mo(t,e,n){var a,o,l,d,p,_,w=0,v=!1,I=!1,R=!0;if(typeof t!="function")throw new Yt(r);function U(q){var W=a,B=o;return a=o=s,w=q,d=t.apply(B,W)}function D(q){var W=q-_;return _===s||W>=e||W<0||I&&q-w>=l}function x(){var q=gr();if(D(q))return E(q);p=Tn(x,function(W){var B=e-(W-_);return I?Ct(B,l-(W-w)):B}(q))}function E(q){return p=s,R&&a?U(q):(a=o=s,d)}function P(){var q=gr(),W=D(q);if(a=arguments,o=this,_=q,W){if(p===s)return function(B){return w=B,p=Tn(x,e),v?U(B):d}(_);if(I)return Pi(p),p=Tn(x,e),U(_)}return p===s&&(p=Tn(x,e)),d}return e=Qt(e)||0,ct(n)&&(v=!!n.leading,l=(I="maxWait"in n)?mt(Qt(n.maxWait)||0,e):l,R="trailing"in n?!!n.trailing:R),P.cancel=function(){p!==s&&Pi(p),w=0,a=_=o=p=s},P.flush=function(){return p===s?d:E(gr())},P}var Ml=Y(function(t,e){return mi(t,1,e)}),ql=Y(function(t,e,n){return mi(t,Qt(e)||0,n)});function _r(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new Yt(r);var n=function(){var a=arguments,o=e?e.apply(this,a):a[0],l=n.cache;if(l.has(o))return l.get(o);var d=t.apply(this,a);return n.cache=l.set(o,d)||l,d};return n.cache=new(_r.Cache||de),n}function yr(t){if(typeof t!="function")throw new Yt(r);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}_r.Cache=de;var Al=Zc(function(t,e){var n=(e=e.length==1&&$(e[0])?ut(e[0],Bt(H())):ut(It(e,1),Bt(H()))).length;return Y(function(a){for(var o=-1,l=Ct(a.length,n);++o<l;)a[o]=e[o].call(this,a[o]);return Nt(t,this,a)})}),Gs=Y(function(t,e){var n=Se(e,en(Gs));return pe(t,h,s,e,n)}),qo=Y(function(t,e){var n=Se(e,en(qo));return pe(t,g,s,e,n)}),Rl=ge(function(t,e){return pe(t,C,s,s,s,e)});function se(t,e){return t===e||t!=t&&e!=e}var kl=cr(ms),Ul=cr(function(t,e){return t>=e}),je=bi(function(){return arguments}())?bi:function(t){return dt(t)&&nt.call(t,"callee")&&!ci.call(t,"callee")},$=A.isArray,Ol=Wa?Bt(Wa):function(t){return dt(t)&&kt(t)==fn};function Dt(t){return t!=null&&mr(t.length)&&!ye(t)}function ht(t){return dt(t)&&Dt(t)}var Me=Mc||ra,Fl=$a?Bt($a):function(t){return dt(t)&&kt(t)==_t};function Vs(t){if(!dt(t))return!1;var e=kt(t);return e==jt||e=="[object DOMException]"||typeof t.message=="string"&&typeof t.name=="string"&&!En(t)}function ye(t){if(!ct(t))return!1;var e=kt(t);return e==te||e==ve||e=="[object AsyncFunction]"||e=="[object Proxy]"}function Ao(t){return typeof t=="number"&&t==V(t)}function mr(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=G}function ct(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}function dt(t){return t!=null&&typeof t=="object"}var Ro=Ga?Bt(Ga):function(t){return dt(t)&&Mt(t)==st};function ko(t){return typeof t=="number"||dt(t)&&kt(t)==wt}function En(t){if(!dt(t)||kt(t)!=le)return!1;var e=$n(t);if(e===null)return!0;var n=nt.call(e,"constructor")&&e.constructor;return typeof n=="function"&&n instanceof n&&Nn.call(n)==Sc}var Ys=Va?Bt(Va):function(t){return dt(t)&&kt(t)==un},Uo=Ya?Bt(Ya):function(t){return dt(t)&&Mt(t)==ee};function vr(t){return typeof t=="string"||!$(t)&&dt(t)&&kt(t)==cn}function Wt(t){return typeof t=="symbol"||dt(t)&&kt(t)==kn}var nn=Ka?Bt(Ka):function(t){return dt(t)&&mr(t.length)&&!!at[kt(t)]},Ll=cr(Ss),Dl=cr(function(t,e){return t<=e});function Oo(t){if(!t)return[];if(Dt(t))return vr(t)?ne(t):Lt(t);if(hn&&t[hn])return function(n){for(var a,o=[];!(a=n.next()).done;)o.push(a.value);return o}(t[hn]());var e=Mt(t);return(e==st?cs:e==ee?Hn:rn)(t)}function me(t){return t?(t=Qt(t))===L||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:t===0?t:0}function V(t){var e=me(t),n=e%1;return e==e?n?e-n:e:0}function Fo(t){return t?Le(V(t),0,k):0}function Qt(t){if(typeof t=="number")return t;if(Wt(t))return M;if(ct(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=ct(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=ei(t);var n=ju.test(t);return n||Bu.test(t)?oc(t.slice(2),n?2:8):Pu.test(t)?M:+t}function Lo(t){return oe(t,Ht(t))}function et(t){return t==null?"":zt(t)}var Hl=Qe(function(t,e){if(In(e)||Dt(e))oe(e,xt(e),t);else for(var n in e)nt.call(e,n)&&mn(t,n,e[n])}),Do=Qe(function(t,e){oe(e,Ht(e),t)}),wr=Qe(function(t,e,n,a){oe(e,Ht(e),t,a)}),Pl=Qe(function(t,e,n,a){oe(e,xt(e),t,a)}),jl=ge(gs),Nl=Y(function(t,e){t=it(t);var n=-1,a=e.length,o=a>2?e[2]:s;for(o&&Ut(e[0],e[1],o)&&(a=1);++n<a;)for(var l=e[n],d=Ht(l),p=-1,_=d.length;++p<_;){var w=d[p],v=t[w];(v===s||se(v,Ke[w])&&!nt.call(t,w))&&(t[w]=l[w])}return t}),Bl=Y(function(t){return t.push(s,eo),Nt(Ho,s,t)});function Ks(t,e,n){var a=t==null?s:De(t,e);return a===s?n:a}function Xs(t,e){return t!=null&&so(t,e,Wc)}var zl=Xi(function(t,e,n){e!=null&&typeof e.toString!="function"&&(e=Bn.call(e)),t[e]=n},Js(Pt)),Wl=Xi(function(t,e,n){e!=null&&typeof e.toString!="function"&&(e=Bn.call(e)),nt.call(t,e)?t[e].push(n):t[e]=[n]},H),$l=Y(wn);function xt(t){return Dt(t)?pi(t):xs(t)}function Ht(t){return Dt(t)?pi(t,!0):$c(t)}var Gl=Qe(function(t,e,n){nr(t,e,n)}),Ho=Qe(function(t,e,n,a){nr(t,e,n,a)}),Vl=ge(function(t,e){var n={};if(t==null)return n;var a=!1;e=ut(e,function(l){return l=Ee(l,t),a||(a=l.length>1),l}),oe(t,Fs(t),n),a&&(n=Xt(n,7,Qc));for(var o=e.length;o--;)Cs(n,e[o]);return n}),Yl=ge(function(t,e){return t==null?{}:function(n,a){return Ri(n,a,function(o,l){return Xs(n,l)})}(t,e)});function Po(t,e){if(t==null)return{};var n=ut(Fs(t),function(a){return[a]});return e=H(e),Ri(t,n,function(a,o){return e(a,o[0])})}var jo=Qi(xt),No=Qi(Ht);function rn(t){return t==null?[]:us(t,xt(t))}var Kl=tn(function(t,e,n){return e=e.toLowerCase(),t+(n?Bo(e):e)});function Bo(t){return Zs(et(t).toLowerCase())}function zo(t){return(t=et(t))&&t.replace(Wu,pc).replace(Qu,"")}var Xl=tn(function(t,e,n){return t+(n?"-":"")+e.toLowerCase()}),Zl=tn(function(t,e,n){return t+(n?" ":"")+e.toLowerCase()}),Jl=Vi("toLowerCase"),Ql=tn(function(t,e,n){return t+(n?"_":"")+e.toLowerCase()}),tf=tn(function(t,e,n){return t+(n?" ":"")+Zs(e)}),ef=tn(function(t,e,n){return t+(n?" ":"")+e.toUpperCase()}),Zs=Vi("toUpperCase");function Wo(t,e,n){return t=et(t),(e=n?s:e)===s?function(a){return nc.test(a)}(t)?function(a){return a.match(tc)||[]}(t):function(a){return a.match(Fu)||[]}(t):t.match(e)||[]}var $o=Y(function(t,e){try{return Nt(t,s,e)}catch(n){return Vs(n)?n:new J(n)}}),nf=ge(function(t,e){return Vt(e,function(n){n=ue(n),he(t,n,$s(t[n],t))}),t});function Js(t){return function(){return t}}var rf=Ki(),sf=Ki(!0);function Pt(t){return t}function Qs(t){return Ti(typeof t=="function"?t:Xt(t,1))}var af=Y(function(t,e){return function(n){return wn(n,t,e)}}),of=Y(function(t,e){return function(n){return wn(t,n,e)}});function ta(t,e,n){var a=xt(e),o=er(e,a);n!=null||ct(e)&&(o.length||!a.length)||(n=e,e=t,t=this,o=er(e,xt(e)));var l=!(ct(n)&&"chain"in n&&!n.chain),d=ye(t);return Vt(o,function(p){var _=e[p];t[p]=_,d&&(t.prototype[p]=function(){var w=this.__chain__;if(l||w){var v=t(this.__wrapped__);return(v.__actions__=Lt(this.__actions__)).push({func:_,args:arguments,thisArg:t}),v.__chain__=w,v}return _.apply(t,xe([this.value()],arguments))})}),t}function ea(){}var uf=ks(ut),cf=ks(Xa),lf=ks(rs);function Go(t){return Ps(t)?ss(ue(t)):function(e){return function(n){return De(n,e)}}(t)}var ff=Zi(),df=Zi(!0);function na(){return[]}function ra(){return!1}var sa,hf=or(function(t,e){return t+e},0),pf=Us("ceil"),gf=or(function(t,e){return t/e},1),_f=Us("floor"),yf=or(function(t,e){return t*e},1),mf=Us("round"),vf=or(function(t,e){return t-e},0);return u.after=function(t,e){if(typeof e!="function")throw new Yt(r);return t=V(t),function(){if(--t<1)return e.apply(this,arguments)}},u.ary=To,u.assign=Hl,u.assignIn=Do,u.assignInWith=wr,u.assignWith=Pl,u.at=jl,u.before=Eo,u.bind=$s,u.bindAll=nf,u.bindKey=Co,u.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return $(t)?t:[t]},u.chain=So,u.chunk=function(t,e,n){e=(n?Ut(t,e,n):e===s)?1:mt(V(e),0);var a=t==null?0:t.length;if(!a||e<1)return[];for(var o=0,l=0,d=A(Yn(a/e));o<a;)d[l++]=Zt(t,o,o+=e);return d},u.compact=function(t){for(var e=-1,n=t==null?0:t.length,a=0,o=[];++e<n;){var l=t[e];l&&(o[a++]=l)}return o},u.concat=function(){var t=arguments.length;if(!t)return[];for(var e=A(t-1),n=arguments[0],a=t;a--;)e[a-1]=arguments[a];return xe($(n)?Lt(n):[n],It(e,1))},u.cond=function(t){var e=t==null?0:t.length,n=H();return t=e?ut(t,function(a){if(typeof a[1]!="function")throw new Yt(r);return[n(a[0]),a[1]]}):[],Y(function(a){for(var o=-1;++o<e;){var l=t[o];if(Nt(l[0],this,a))return Nt(l[1],this,a)}})},u.conforms=function(t){return function(e){var n=xt(e);return function(a){return yi(a,e,n)}}(Xt(t,1))},u.constant=Js,u.countBy=wl,u.create=function(t,e){var n=Je(t);return e==null?n:_i(n,e)},u.curry=function t(e,n,a){var o=pe(e,8,s,s,s,s,s,n=a?s:n);return o.placeholder=t.placeholder,o},u.curryRight=function t(e,n,a){var o=pe(e,f,s,s,s,s,s,n=a?s:n);return o.placeholder=t.placeholder,o},u.debounce=Mo,u.defaults=Nl,u.defaultsDeep=Bl,u.defer=Ml,u.delay=ql,u.difference=nl,u.differenceBy=rl,u.differenceWith=sl,u.drop=function(t,e,n){var a=t==null?0:t.length;return a?Zt(t,(e=n||e===s?1:V(e))<0?0:e,a):[]},u.dropRight=function(t,e,n){var a=t==null?0:t.length;return a?Zt(t,0,(e=a-(e=n||e===s?1:V(e)))<0?0:e):[]},u.dropRightWhile=function(t,e){return t&&t.length?sr(t,H(e,3),!0,!0):[]},u.dropWhile=function(t,e){return t&&t.length?sr(t,H(e,3),!0):[]},u.fill=function(t,e,n,a){var o=t==null?0:t.length;return o?(n&&typeof n!="number"&&Ut(t,e,n)&&(n=0,a=o),function(l,d,p,_){var w=l.length;for((p=V(p))<0&&(p=-p>w?0:w+p),(_=_===s||_>w?w:V(_))<0&&(_+=w),_=p>_?0:Fo(_);p<_;)l[p++]=d;return l}(t,e,n,a)):[]},u.filter=function(t,e){return($(t)?we:wi)(t,H(e,3))},u.flatMap=function(t,e){return It(pr(t,e),1)},u.flatMapDeep=function(t,e){return It(pr(t,e),L)},u.flatMapDepth=function(t,e,n){return n=n===s?1:V(n),It(pr(t,e),n)},u.flatten=mo,u.flattenDeep=function(t){return t!=null&&t.length?It(t,L):[]},u.flattenDepth=function(t,e){return t!=null&&t.length?It(t,e=e===s?1:V(e)):[]},u.flip=function(t){return pe(t,512)},u.flow=rf,u.flowRight=sf,u.fromPairs=function(t){for(var e=-1,n=t==null?0:t.length,a={};++e<n;){var o=t[e];a[o[0]]=o[1]}return a},u.functions=function(t){return t==null?[]:er(t,xt(t))},u.functionsIn=function(t){return t==null?[]:er(t,Ht(t))},u.groupBy=bl,u.initial=function(t){return t!=null&&t.length?Zt(t,0,-1):[]},u.intersection=al,u.intersectionBy=il,u.intersectionWith=ol,u.invert=zl,u.invertBy=Wl,u.invokeMap=Il,u.iteratee=Qs,u.keyBy=Tl,u.keys=xt,u.keysIn=Ht,u.map=pr,u.mapKeys=function(t,e){var n={};return e=H(e,3),ie(t,function(a,o,l){he(n,e(a,o,l),a)}),n},u.mapValues=function(t,e){var n={};return e=H(e,3),ie(t,function(a,o,l){he(n,o,e(a,o,l))}),n},u.matches=function(t){return Ci(Xt(t,1))},u.matchesProperty=function(t,e){return Mi(t,Xt(e,1))},u.memoize=_r,u.merge=Gl,u.mergeWith=Ho,u.method=af,u.methodOf=of,u.mixin=ta,u.negate=yr,u.nthArg=function(t){return t=V(t),Y(function(e){return qi(e,t)})},u.omit=Vl,u.omitBy=function(t,e){return Po(t,yr(H(e)))},u.once=function(t){return Eo(2,t)},u.orderBy=function(t,e,n,a){return t==null?[]:($(e)||(e=e==null?[]:[e]),$(n=a?s:n)||(n=n==null?[]:[n]),Ai(t,e,n))},u.over=uf,u.overArgs=Al,u.overEvery=cf,u.overSome=lf,u.partial=Gs,u.partialRight=qo,u.partition=El,u.pick=Yl,u.pickBy=Po,u.property=Go,u.propertyOf=function(t){return function(e){return t==null?s:De(t,e)}},u.pull=ul,u.pullAll=wo,u.pullAllBy=function(t,e,n){return t&&t.length&&e&&e.length?bs(t,e,H(n,2)):t},u.pullAllWith=function(t,e,n){return t&&t.length&&e&&e.length?bs(t,e,s,n):t},u.pullAt=cl,u.range=ff,u.rangeRight=df,u.rearg=Rl,u.reject=function(t,e){return($(t)?we:wi)(t,yr(H(e,3)))},u.remove=function(t,e){var n=[];if(!t||!t.length)return n;var a=-1,o=[],l=t.length;for(e=H(e,3);++a<l;){var d=t[a];e(d,a,t)&&(n.push(d),o.push(a))}return ki(t,o),n},u.rest=function(t,e){if(typeof t!="function")throw new Yt(r);return Y(t,e=e===s?e:V(e))},u.reverse=zs,u.sampleSize=function(t,e,n){return e=(n?Ut(t,e,n):e===s)?1:V(e),($(t)?Pc:Vc)(t,e)},u.set=function(t,e,n){return t==null?t:Sn(t,e,n)},u.setWith=function(t,e,n,a){return a=typeof a=="function"?a:s,t==null?t:Sn(t,e,n,a)},u.shuffle=function(t){return($(t)?jc:Kc)(t)},u.slice=function(t,e,n){var a=t==null?0:t.length;return a?(n&&typeof n!="number"&&Ut(t,e,n)?(e=0,n=a):(e=e==null?0:V(e),n=n===s?a:V(n)),Zt(t,e,n)):[]},u.sortBy=Cl,u.sortedUniq=function(t){return t&&t.length?Oi(t):[]},u.sortedUniqBy=function(t,e){return t&&t.length?Oi(t,H(e,2)):[]},u.split=function(t,e,n){return n&&typeof n!="number"&&Ut(t,e,n)&&(e=n=s),(n=n===s?k:n>>>0)?(t=et(t))&&(typeof e=="string"||e!=null&&!Ys(e))&&!(e=zt(e))&&Ge(t)?Ce(ne(t),0,n):t.split(e,n):[]},u.spread=function(t,e){if(typeof t!="function")throw new Yt(r);return e=e==null?0:mt(V(e),0),Y(function(n){var a=n[e],o=Ce(n,0,e);return a&&xe(o,a),Nt(t,this,o)})},u.tail=function(t){var e=t==null?0:t.length;return e?Zt(t,1,e):[]},u.take=function(t,e,n){return t&&t.length?Zt(t,0,(e=n||e===s?1:V(e))<0?0:e):[]},u.takeRight=function(t,e,n){var a=t==null?0:t.length;return a?Zt(t,(e=a-(e=n||e===s?1:V(e)))<0?0:e,a):[]},u.takeRightWhile=function(t,e){return t&&t.length?sr(t,H(e,3),!1,!0):[]},u.takeWhile=function(t,e){return t&&t.length?sr(t,H(e,3)):[]},u.tap=function(t,e){return e(t),t},u.throttle=function(t,e,n){var a=!0,o=!0;if(typeof t!="function")throw new Yt(r);return ct(n)&&(a="leading"in n?!!n.leading:a,o="trailing"in n?!!n.trailing:o),Mo(t,e,{leading:a,maxWait:e,trailing:o})},u.thru=hr,u.toArray=Oo,u.toPairs=jo,u.toPairsIn=No,u.toPath=function(t){return $(t)?ut(t,ue):Wt(t)?[t]:Lt(po(et(t)))},u.toPlainObject=Lo,u.transform=function(t,e,n){var a=$(t),o=a||Me(t)||nn(t);if(e=H(e,4),n==null){var l=t&&t.constructor;n=o?a?new l:[]:ct(t)&&ye(l)?Je($n(t)):{}}return(o?Vt:ie)(t,function(d,p,_){return e(n,d,p,_)}),n},u.unary=function(t){return To(t,1)},u.union=ll,u.unionBy=fl,u.unionWith=dl,u.uniq=function(t){return t&&t.length?Te(t):[]},u.uniqBy=function(t,e){return t&&t.length?Te(t,H(e,2)):[]},u.uniqWith=function(t,e){return e=typeof e=="function"?e:s,t&&t.length?Te(t,s,e):[]},u.unset=function(t,e){return t==null||Cs(t,e)},u.unzip=Ws,u.unzipWith=xo,u.update=function(t,e,n){return t==null?t:Li(t,e,As(n))},u.updateWith=function(t,e,n,a){return a=typeof a=="function"?a:s,t==null?t:Li(t,e,As(n),a)},u.values=rn,u.valuesIn=function(t){return t==null?[]:us(t,Ht(t))},u.without=hl,u.words=Wo,u.wrap=function(t,e){return Gs(As(e),t)},u.xor=pl,u.xorBy=gl,u.xorWith=_l,u.zip=yl,u.zipObject=function(t,e){return Hi(t||[],e||[],mn)},u.zipObjectDeep=function(t,e){return Hi(t||[],e||[],Sn)},u.zipWith=ml,u.entries=jo,u.entriesIn=No,u.extend=Do,u.extendWith=wr,ta(u,u),u.add=hf,u.attempt=$o,u.camelCase=Kl,u.capitalize=Bo,u.ceil=pf,u.clamp=function(t,e,n){return n===s&&(n=e,e=s),n!==s&&(n=(n=Qt(n))==n?n:0),e!==s&&(e=(e=Qt(e))==e?e:0),Le(Qt(t),e,n)},u.clone=function(t){return Xt(t,4)},u.cloneDeep=function(t){return Xt(t,5)},u.cloneDeepWith=function(t,e){return Xt(t,5,e=typeof e=="function"?e:s)},u.cloneWith=function(t,e){return Xt(t,4,e=typeof e=="function"?e:s)},u.conformsTo=function(t,e){return e==null||yi(t,e,xt(e))},u.deburr=zo,u.defaultTo=function(t,e){return t==null||t!=t?e:t},u.divide=gf,u.endsWith=function(t,e,n){t=et(t),e=zt(e);var a=t.length,o=n=n===s?a:Le(V(n),0,a);return(n-=e.length)>=0&&t.slice(n,o)==e},u.eq=se,u.escape=function(t){return(t=et(t))&&Iu.test(t)?t.replace(xa,gc):t},u.escapeRegExp=function(t){return(t=et(t))&&Au.test(t)?t.replace(Vr,"\\$&"):t},u.every=function(t,e,n){var a=$(t)?Xa:Bc;return n&&Ut(t,e,n)&&(e=s),a(t,H(e,3))},u.find=xl,u.findIndex=_o,u.findKey=function(t,e){return Za(t,H(e,3),ie)},u.findLast=Sl,u.findLastIndex=yo,u.findLastKey=function(t,e){return Za(t,H(e,3),ys)},u.floor=_f,u.forEach=bo,u.forEachRight=Io,u.forIn=function(t,e){return t==null?t:_s(t,H(e,3),Ht)},u.forInRight=function(t,e){return t==null?t:xi(t,H(e,3),Ht)},u.forOwn=function(t,e){return t&&ie(t,H(e,3))},u.forOwnRight=function(t,e){return t&&ys(t,H(e,3))},u.get=Ks,u.gt=kl,u.gte=Ul,u.has=function(t,e){return t!=null&&so(t,e,zc)},u.hasIn=Xs,u.head=vo,u.identity=Pt,u.includes=function(t,e,n,a){t=Dt(t)?t:rn(t),n=n&&!a?V(n):0;var o=t.length;return n<0&&(n=mt(o+n,0)),vr(t)?n<=o&&t.indexOf(e,n)>-1:!!o&&$e(t,e,n)>-1},u.indexOf=function(t,e,n){var a=t==null?0:t.length;if(!a)return-1;var o=n==null?0:V(n);return o<0&&(o=mt(a+o,0)),$e(t,e,o)},u.inRange=function(t,e,n){return e=me(e),n===s?(n=e,e=0):n=me(n),function(a,o,l){return a>=Ct(o,l)&&a<mt(o,l)}(t=Qt(t),e,n)},u.invoke=$l,u.isArguments=je,u.isArray=$,u.isArrayBuffer=Ol,u.isArrayLike=Dt,u.isArrayLikeObject=ht,u.isBoolean=function(t){return t===!0||t===!1||dt(t)&&kt(t)==Z},u.isBuffer=Me,u.isDate=Fl,u.isElement=function(t){return dt(t)&&t.nodeType===1&&!En(t)},u.isEmpty=function(t){if(t==null)return!0;if(Dt(t)&&($(t)||typeof t=="string"||typeof t.splice=="function"||Me(t)||nn(t)||je(t)))return!t.length;var e=Mt(t);if(e==st||e==ee)return!t.size;if(In(t))return!xs(t).length;for(var n in t)if(nt.call(t,n))return!1;return!0},u.isEqual=function(t,e){return xn(t,e)},u.isEqualWith=function(t,e,n){var a=(n=typeof n=="function"?n:s)?n(t,e):s;return a===s?xn(t,e,s,n):!!a},u.isError=Vs,u.isFinite=function(t){return typeof t=="number"&&fi(t)},u.isFunction=ye,u.isInteger=Ao,u.isLength=mr,u.isMap=Ro,u.isMatch=function(t,e){return t===e||ws(t,e,Ds(e))},u.isMatchWith=function(t,e,n){return n=typeof n=="function"?n:s,ws(t,e,Ds(e),n)},u.isNaN=function(t){return ko(t)&&t!=+t},u.isNative=function(t){if(el(t))throw new J("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Ii(t)},u.isNil=function(t){return t==null},u.isNull=function(t){return t===null},u.isNumber=ko,u.isObject=ct,u.isObjectLike=dt,u.isPlainObject=En,u.isRegExp=Ys,u.isSafeInteger=function(t){return Ao(t)&&t>=-9007199254740991&&t<=G},u.isSet=Uo,u.isString=vr,u.isSymbol=Wt,u.isTypedArray=nn,u.isUndefined=function(t){return t===s},u.isWeakMap=function(t){return dt(t)&&Mt(t)==ln},u.isWeakSet=function(t){return dt(t)&&kt(t)=="[object WeakSet]"},u.join=function(t,e){return t==null?"":qc.call(t,e)},u.kebabCase=Xl,u.last=Jt,u.lastIndexOf=function(t,e,n){var a=t==null?0:t.length;if(!a)return-1;var o=a;return n!==s&&(o=(o=V(n))<0?mt(a+o,0):Ct(o,a-1)),e==e?function(l,d,p){for(var _=p+1;_--;)if(l[_]===d)return _;return _}(t,e,o):Dn(t,Ja,o,!0)},u.lowerCase=Zl,u.lowerFirst=Jl,u.lt=Ll,u.lte=Dl,u.max=function(t){return t&&t.length?tr(t,Pt,ms):s},u.maxBy=function(t,e){return t&&t.length?tr(t,H(e,2),ms):s},u.mean=function(t){return Qa(t,Pt)},u.meanBy=function(t,e){return Qa(t,H(e,2))},u.min=function(t){return t&&t.length?tr(t,Pt,Ss):s},u.minBy=function(t,e){return t&&t.length?tr(t,H(e,2),Ss):s},u.stubArray=na,u.stubFalse=ra,u.stubObject=function(){return{}},u.stubString=function(){return""},u.stubTrue=function(){return!0},u.multiply=yf,u.nth=function(t,e){return t&&t.length?qi(t,V(e)):s},u.noConflict=function(){return bt._===this&&(bt._=bc),this},u.noop=ea,u.now=gr,u.pad=function(t,e,n){t=et(t);var a=(e=V(e))?Ve(t):0;if(!e||a>=e)return t;var o=(e-a)/2;return ur(Kn(o),n)+t+ur(Yn(o),n)},u.padEnd=function(t,e,n){t=et(t);var a=(e=V(e))?Ve(t):0;return e&&a<e?t+ur(e-a,n):t},u.padStart=function(t,e,n){t=et(t);var a=(e=V(e))?Ve(t):0;return e&&a<e?ur(e-a,n)+t:t},u.parseInt=function(t,e,n){return n||e==null?e=0:e&&(e=+e),kc(et(t).replace(Yr,""),e||0)},u.random=function(t,e,n){if(n&&typeof n!="boolean"&&Ut(t,e,n)&&(e=n=s),n===s&&(typeof e=="boolean"?(n=e,e=s):typeof t=="boolean"&&(n=t,t=s)),t===s&&e===s?(t=0,e=1):(t=me(t),e===s?(e=t,t=0):e=me(e)),t>e){var a=t;t=e,e=a}if(n||t%1||e%1){var o=di();return Ct(t+o*(e-t+ic("1e-"+((o+"").length-1))),e)}return Is(t,e)},u.reduce=function(t,e,n){var a=$(t)?ns:ti,o=arguments.length<3;return a(t,H(e,4),n,o,Ie)},u.reduceRight=function(t,e,n){var a=$(t)?fc:ti,o=arguments.length<3;return a(t,H(e,4),n,o,vi)},u.repeat=function(t,e,n){return e=(n?Ut(t,e,n):e===s)?1:V(e),Ts(et(t),e)},u.replace=function(){var t=arguments,e=et(t[0]);return t.length<3?e:e.replace(t[1],t[2])},u.result=function(t,e,n){var a=-1,o=(e=Ee(e,t)).length;for(o||(o=1,t=s);++a<o;){var l=t==null?s:t[ue(e[a])];l===s&&(a=o,l=n),t=ye(l)?l.call(t):l}return t},u.round=mf,u.runInContext=y,u.sample=function(t){return($(t)?gi:Gc)(t)},u.size=function(t){if(t==null)return 0;if(Dt(t))return vr(t)?Ve(t):t.length;var e=Mt(t);return e==st||e==ee?t.size:xs(t).length},u.snakeCase=Ql,u.some=function(t,e,n){var a=$(t)?rs:Xc;return n&&Ut(t,e,n)&&(e=s),a(t,H(e,3))},u.sortedIndex=function(t,e){return rr(t,e)},u.sortedIndexBy=function(t,e,n){return Es(t,e,H(n,2))},u.sortedIndexOf=function(t,e){var n=t==null?0:t.length;if(n){var a=rr(t,e);if(a<n&&se(t[a],e))return a}return-1},u.sortedLastIndex=function(t,e){return rr(t,e,!0)},u.sortedLastIndexBy=function(t,e,n){return Es(t,e,H(n,2),!0)},u.sortedLastIndexOf=function(t,e){if(t!=null&&t.length){var n=rr(t,e,!0)-1;if(se(t[n],e))return n}return-1},u.startCase=tf,u.startsWith=function(t,e,n){return t=et(t),n=n==null?0:Le(V(n),0,t.length),e=zt(e),t.slice(n,n+e.length)==e},u.subtract=vf,u.sum=function(t){return t&&t.length?is(t,Pt):0},u.sumBy=function(t,e){return t&&t.length?is(t,H(e,2)):0},u.template=function(t,e,n){var a=u.templateSettings;n&&Ut(t,e,n)&&(e=s),t=et(t),e=wr({},e,a,to);var o,l,d=wr({},e.imports,a.imports,to),p=xt(d),_=us(d,p),w=0,v=e.interpolate||Un,I="__p += '",R=ls((e.escape||Un).source+"|"+v.source+"|"+(v===Sa?Hu:Un).source+"|"+(e.evaluate||Un).source+"|$","g"),U="//# sourceURL="+(nt.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++sc+"]")+`
`;t.replace(R,function(E,P,q,W,B,Q){return q||(q=W),I+=t.slice(w,Q).replace($u,_c),P&&(o=!0,I+=`' +
__e(`+P+`) +
'`),B&&(l=!0,I+=`';
`+B+`;
__p += '`),q&&(I+=`' +
((__t = (`+q+`)) == null ? '' : __t) +
'`),w=Q+E.length,E}),I+=`';
`;var D=nt.call(e,"variable")&&e.variable;if(D){if(Lu.test(D))throw new J("Invalid `variable` option passed into `_.template`")}else I=`with (obj) {
`+I+`
}
`;I=(l?I.replace(wu,""):I).replace(xu,"$1").replace(Su,"$1;"),I="function("+(D||"obj")+`) {
`+(D?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(o?", __e = _.escape":"")+(l?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+I+`return __p
}`;var x=$o(function(){return yt(p,U+"return "+I).apply(s,_)});if(x.source=I,Vs(x))throw x;return x},u.times=function(t,e){if((t=V(t))<1||t>G)return[];var n=k,a=Ct(t,k);e=H(e),t-=k;for(var o=os(a,e);++n<t;)e(n);return o},u.toFinite=me,u.toInteger=V,u.toLength=Fo,u.toLower=function(t){return et(t).toLowerCase()},u.toNumber=Qt,u.toSafeInteger=function(t){return t?Le(V(t),-9007199254740991,G):t===0?t:0},u.toString=et,u.toUpper=function(t){return et(t).toUpperCase()},u.trim=function(t,e,n){if((t=et(t))&&(n||e===s))return ei(t);if(!t||!(e=zt(e)))return t;var a=ne(t),o=ne(e);return Ce(a,ni(a,o),ri(a,o)+1).join("")},u.trimEnd=function(t,e,n){if((t=et(t))&&(n||e===s))return t.slice(0,ai(t)+1);if(!t||!(e=zt(e)))return t;var a=ne(t);return Ce(a,0,ri(a,ne(e))+1).join("")},u.trimStart=function(t,e,n){if((t=et(t))&&(n||e===s))return t.replace(Yr,"");if(!t||!(e=zt(e)))return t;var a=ne(t);return Ce(a,ni(a,ne(e))).join("")},u.truncate=function(t,e){var n=30,a="...";if(ct(e)){var o="separator"in e?e.separator:o;n="length"in e?V(e.length):n,a="omission"in e?zt(e.omission):a}var l=(t=et(t)).length;if(Ge(t)){var d=ne(t);l=d.length}if(n>=l)return t;var p=n-Ve(a);if(p<1)return a;var _=d?Ce(d,0,p).join(""):t.slice(0,p);if(o===s)return _+a;if(d&&(p+=_.length-p),Ys(o)){if(t.slice(p).search(o)){var w,v=_;for(o.global||(o=ls(o.source,et(ba.exec(o))+"g")),o.lastIndex=0;w=o.exec(v);)var I=w.index;_=_.slice(0,I===s?p:I)}}else if(t.indexOf(zt(o),p)!=p){var R=_.lastIndexOf(o);R>-1&&(_=_.slice(0,R))}return _+a},u.unescape=function(t){return(t=et(t))&&bu.test(t)?t.replace(wa,mc):t},u.uniqueId=function(t){var e=++xc;return et(t)+e},u.upperCase=ef,u.upperFirst=Zs,u.each=bo,u.eachRight=Io,u.first=vo,ta(u,(sa={},ie(u,function(t,e){nt.call(u.prototype,e)||(sa[e]=t)}),sa),{chain:!1}),u.VERSION="4.17.21",Vt(["bind","bindKey","curry","curryRight","partial","partialRight"],function(t){u[t].placeholder=u}),Vt(["drop","take"],function(t,e){X.prototype[t]=function(n){n=n===s?1:mt(V(n),0);var a=this.__filtered__&&!e?new X(this):this.clone();return a.__filtered__?a.__takeCount__=Ct(n,a.__takeCount__):a.__views__.push({size:Ct(n,k),type:t+(a.__dir__<0?"Right":"")}),a},X.prototype[t+"Right"]=function(n){return this.reverse()[t](n).reverse()}}),Vt(["filter","map","takeWhile"],function(t,e){var n=e+1,a=n==1||n==3;X.prototype[t]=function(o){var l=this.clone();return l.__iteratees__.push({iteratee:H(o,3),type:n}),l.__filtered__=l.__filtered__||a,l}}),Vt(["head","last"],function(t,e){var n="take"+(e?"Right":"");X.prototype[t]=function(){return this[n](1).value()[0]}}),Vt(["initial","tail"],function(t,e){var n="drop"+(e?"":"Right");X.prototype[t]=function(){return this.__filtered__?new X(this):this[n](1)}}),X.prototype.compact=function(){return this.filter(Pt)},X.prototype.find=function(t){return this.filter(t).head()},X.prototype.findLast=function(t){return this.reverse().find(t)},X.prototype.invokeMap=Y(function(t,e){return typeof t=="function"?new X(this):this.map(function(n){return wn(n,t,e)})}),X.prototype.reject=function(t){return this.filter(yr(H(t)))},X.prototype.slice=function(t,e){t=V(t);var n=this;return n.__filtered__&&(t>0||e<0)?new X(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),e!==s&&(n=(e=V(e))<0?n.dropRight(-e):n.take(e-t)),n)},X.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},X.prototype.toArray=function(){return this.take(k)},ie(X.prototype,function(t,e){var n=/^(?:filter|find|map|reject)|While$/.test(e),a=/^(?:head|last)$/.test(e),o=u[a?"take"+(e=="last"?"Right":""):e],l=a||/^find/.test(e);o&&(u.prototype[e]=function(){var d=this.__wrapped__,p=a?[1]:arguments,_=d instanceof X,w=p[0],v=_||$(d),I=function(P){var q=o.apply(u,xe([P],p));return a&&R?q[0]:q};v&&n&&typeof w=="function"&&w.length!=1&&(_=v=!1);var R=this.__chain__,U=!!this.__actions__.length,D=l&&!R,x=_&&!U;if(!l&&v){d=x?d:new X(this);var E=t.apply(d,p);return E.__actions__.push({func:hr,args:[I],thisArg:s}),new Kt(E,R)}return D&&x?t.apply(this,p):(E=this.thru(I),D?a?E.value()[0]:E.value():E)})}),Vt(["pop","push","shift","sort","splice","unshift"],function(t){var e=Pn[t],n=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",a=/^(?:pop|shift)$/.test(t);u.prototype[t]=function(){var o=arguments;if(a&&!this.__chain__){var l=this.value();return e.apply($(l)?l:[],o)}return this[n](function(d){return e.apply($(d)?d:[],o)})}}),ie(X.prototype,function(t,e){var n=u[e];if(n){var a=n.name+"";nt.call(Ze,a)||(Ze[a]=[]),Ze[a].push({name:e,func:n})}}),Ze[ir(s,2).name]=[{name:"wrapper",func:s}],X.prototype.clone=function(){var t=new X(this.__wrapped__);return t.__actions__=Lt(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=Lt(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=Lt(this.__views__),t},X.prototype.reverse=function(){if(this.__filtered__){var t=new X(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},X.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=$(t),a=e<0,o=n?t.length:0,l=function(Q,F,j){for(var vt=-1,pt=j.length;++vt<pt;){var Ot=j[vt],ot=Ot.size;switch(Ot.type){case"drop":Q+=ot;break;case"dropRight":F-=ot;break;case"take":F=Ct(F,Q+ot);break;case"takeRight":Q=mt(Q,F-ot)}}return{start:Q,end:F}}(0,o,this.__views__),d=l.start,p=l.end,_=p-d,w=a?p:d-1,v=this.__iteratees__,I=v.length,R=0,U=Ct(_,this.__takeCount__);if(!n||!a&&o==_&&U==_)return Di(t,this.__actions__);var D=[];t:for(;_--&&R<U;){for(var x=-1,E=t[w+=e];++x<I;){var P=v[x],q=P.iteratee,W=P.type,B=q(E);if(W==2)E=B;else if(!B){if(W==1)continue t;break t}}D[R++]=E}return D},u.prototype.at=vl,u.prototype.chain=function(){return So(this)},u.prototype.commit=function(){return new Kt(this.value(),this.__chain__)},u.prototype.next=function(){this.__values__===s&&(this.__values__=Oo(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?s:this.__values__[this.__index__++]}},u.prototype.plant=function(t){for(var e,n=this;n instanceof Jn;){var a=go(n);a.__index__=0,a.__values__=s,e?o.__wrapped__=a:e=a;var o=a;n=n.__wrapped__}return o.__wrapped__=t,e},u.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof X){var e=t;return this.__actions__.length&&(e=new X(this)),(e=e.reverse()).__actions__.push({func:hr,args:[zs],thisArg:s}),new Kt(e,this.__chain__)}return this.thru(zs)},u.prototype.toJSON=u.prototype.valueOf=u.prototype.value=function(){return Di(this.__wrapped__,this.__actions__)},u.prototype.first=u.prototype.head,hn&&(u.prototype[hn]=function(){return this}),u}();ke?((ke.exports=Ye)._=Ye,Qr._=Ye):bt._=Ye}).call(Mn);var oh=ma.exports;function wd(s){let r,i,c=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],f={};for(let h=0;h<c.length;h+=1)f=an(f,c[h]);return{c(){r=lu("svg"),i=new fu(!0),this.h()},l(h){r=du(h,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var g=hu(r);i=pu(g,!0),g.forEach(Or),this.h()},h(){i.a=null,Fr(r,f)},m(h,g){gu(h,r,g),i.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 401c9.4 9.4 24.6 9.4 33.9 0L465 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 175c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>',r)},p(h,[g]){Fr(r,f=_u(c,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&g&&h[0]]))},i:Lr,o:Lr,d(h){h&&Or(r)}}}function xd(s,r,i){return s.$$set=c=>{i(0,r=an(an({},r),Dr(c)))},[r=Dr(r)]}class uh extends ou{constructor(r){super(),uu(this,r,xd,wd,cu,{})}}function Sd(s){let r,i,c=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],f={};for(let h=0;h<c.length;h+=1)f=an(f,c[h]);return{c(){r=lu("svg"),i=new fu(!0),this.h()},l(h){r=du(h,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var g=hu(r);i=pu(g,!0),g.forEach(Or),this.h()},h(){i.a=null,Fr(r,f)},m(h,g){gu(h,r,g),i.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 24c0 13.3 10.7 24 24 24h102.1L207 271c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l223-223L464 184c0 13.3 10.7 24 24 24s24-10.7 24-24V24c0-13.3-10.7-24-24-24H328c-13.3 0-24 10.7-24 24M72 32C32.2 32 0 64.2 0 104v336c0 39.8 32.2 72 72 72h336c39.8 0 72-32.2 72-72V312c0-13.3-10.7-24-24-24s-24 10.7-24 24v128c0 13.3-10.7 24-24 24H72c-13.3 0-24-10.7-24-24V104c0-13.3 10.7-24 24-24h128c13.3 0 24-10.7 24-24s-10.7-24-24-24z"/>',r)},p(h,[g]){Fr(r,f=_u(c,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&g&&h[0]]))},i:Lr,o:Lr,d(h){h&&Or(r)}}}function bd(s,r,i){return s.$$set=c=>{i(0,r=an(an({},r),Dr(c)))},[r=Dr(r)]}class ch extends ou{constructor(r){super(),uu(this,r,bd,Sd,cu,{})}}export{hd as $,ch as A,kd as B,uh as C,Dd as D,ih as E,fd as F,zd as G,Bd as H,Od as I,Pd as J,jd as K,Fd as L,Kd as M,od as N,pd as O,Zd as P,gd as Q,An as R,Rt as S,eh as T,Hd as U,nh as V,th as W,Qd as X,rh as Y,iu as Z,Yd as _,tt as a,Nd as a0,nd as a1,rd as a2,Wd as a3,Gd as a4,mu as a5,ah as a6,sh as a7,sn as b,qr as c,$d as d,Be as e,$t as f,ud as g,pa as h,Xd as i,su as j,St as k,oh as l,ad as m,Vd as n,Jd as o,Kf as p,Xf as q,Zf as r,Qf as s,id as t,Jf as u,Ld as v,Ud as w,vu as x,ed as y,td as z};

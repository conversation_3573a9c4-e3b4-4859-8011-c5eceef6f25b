import{S as T,i as M,s as B,a as L,b as _,H as lt,w as it,x as rt,y as dt,h as v,d as Q,z as pt,g as et,n as k,j as z,ai as ut,al as ft,J as N,D as H,V as ot,c as p,a2 as $,P as R,e as C,f as b,ac as mt,K as j,L as A,M as P,af as ht,u as g,q,t as y,r as D,a3 as U,I as $t,aj as F,am as gt,N as nt,A as W,E as G,F as J,ad as yt,G as K}from"./SpinnerAugment-CL9SZpf8.js";import{I as vt}from"./IconButtonAugment-C4xMcLhX.js";function xt(n){let t,s,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],c={};for(let o=0;o<e.length;o+=1)c=L(c,e[o]);return{c(){t=_("svg"),s=new lt(!0),this.h()},l(o){t=it(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=rt(t);s=dt(a,!0),a.forEach(v),this.h()},h(){s.a=null,Q(t,c)},m(o,a){pt(o,t,a),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',t)},p(o,[a]){Q(t,c=et(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&o[0]]))},i:k,o:k,d(o){o&&v(t)}}}function wt(n,t,s){return n.$$set=e=>{s(0,t=L(L({},t),z(e)))},[t=z(t)]}class jt extends T{constructor(t){super(),M(this,t,wt,xt,B,{})}}const ct=Symbol("collapsible");function bt(){return ut(ct)}function kt(n,t){const{onStuck:s,onUnstuck:e,offset:c=0}=t,o=document.createElement("div");o.style.position="absolute",o.style.top=c?`${c}px`:"0",o.style.height="1px",o.style.width="100%",o.style.pointerEvents="none",o.style.opacity="0",o.style.zIndex="-1";const a=n.parentNode;if(!a)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(a).position==="static"&&(a.style.position="relative"),a.insertBefore(o,n);const i=new IntersectionObserver(([l])=>{l.isIntersecting?e==null||e():s==null||s()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return i.observe(o),{update(l){t.onStuck=l.onStuck,t.onUnstuck=l.onUnstuck,l.offset!==void 0&&l.offset!==c&&(o.style.top=`${l.offset}px`)},destroy(){i.disconnect(),o.remove()}}}const Ct=n=>({}),X=n=>({}),Lt=n=>({}),Y=n=>({});function tt(n){let t,s,e,c;const o=n[14].default,a=N(o,n,n[13],null);let i=n[9].footer&&st(n);return{c(){t=H("div"),a&&a.c(),s=ot(),i&&i.c(),e=nt(),p(t,"class","c-collapsible__body svelte-gbhym3")},m(l,r){C(l,t,r),a&&a.m(t,null),C(l,s,r),i&&i.m(l,r),C(l,e,r),c=!0},p(l,r){a&&a.p&&(!c||8192&r)&&j(a,o,l,l[13],c?P(o,l[13],r,null):A(l[13]),null),l[9].footer?i?(i.p(l,r),512&r&&g(i,1)):(i=st(l),i.c(),g(i,1),i.m(e.parentNode,e)):i&&(q(),y(i,1,1,()=>{i=null}),D())},i(l){c||(g(a,l),g(i),c=!0)},o(l){y(a,l),y(i),c=!1},d(l){l&&(v(t),v(s),v(e)),a&&a.d(l),i&&i.d(l)}}}function st(n){let t,s;const e=n[14].footer,c=N(e,n,n[13],X);return{c(){t=H("footer"),c&&c.c(),p(t,"class","c-collapsible__footer svelte-gbhym3")},m(o,a){C(o,t,a),c&&c.m(t,null),s=!0},p(o,a){c&&c.p&&(!s||8192&a)&&j(c,e,o,o[13],s?P(e,o[13],a,Ct):A(o[13]),X)},i(o){s||(g(c,o),s=!0)},o(o){y(c,o),s=!1},d(o){o&&v(t),c&&c.d(o)}}}function Ht(n){let t,s,e,c,o,a,i,l,r,x,w;const S=n[14].header,h=N(S,n,n[13],Y);let f=n[4]&&n[5]&&tt(n);return{c(){t=H("div"),s=H("header"),e=H("div"),h&&h.c(),o=ot(),a=H("div"),i=H("div"),f&&f.c(),p(e,"class","c-collapsible__header-inner svelte-gbhym3"),$(e,"is-collapsed",n[3]),$(e,"is-header-stuck",n[0]),$(e,"has-header-padding",n[2]>0),p(s,"class","c-collapsible__header svelte-gbhym3"),$(s,"is-sticky",n[1]),p(i,"class","c-collapsible__content-inner svelte-gbhym3"),p(a,"class","c-collapsible__content svelte-gbhym3"),$(a,"is-collapsed",n[3]),p(t,"class",l="c-collapsible "+n[6]+" svelte-gbhym3"),$(t,"is-collapsed",n[3]),$(t,"is-expandable",n[4]),R(t,"--sticky-header-top",`${n[2]}px`)},m(d,u){C(d,t,u),b(t,s),b(s,e),h&&h.m(e,null),b(t,o),b(t,a),b(a,i),f&&f.m(i,null),r=!0,x||(w=mt(c=kt.call(null,s,{offset:-n[2],onStuck:n[15],onUnstuck:n[16]})),x=!0)},p(d,[u]){h&&h.p&&(!r||8192&u)&&j(h,S,d,d[13],r?P(S,d[13],u,Lt):A(d[13]),Y),(!r||8&u)&&$(e,"is-collapsed",d[3]),(!r||1&u)&&$(e,"is-header-stuck",d[0]),(!r||4&u)&&$(e,"has-header-padding",d[2]>0),c&&ht(c.update)&&5&u&&c.update.call(null,{offset:-d[2],onStuck:d[15],onUnstuck:d[16]}),(!r||2&u)&&$(s,"is-sticky",d[1]),d[4]&&d[5]?f?(f.p(d,u),48&u&&g(f,1)):(f=tt(d),f.c(),g(f,1),f.m(i,null)):f&&(q(),y(f,1,1,()=>{f=null}),D()),(!r||8&u)&&$(a,"is-collapsed",d[3]),(!r||64&u&&l!==(l="c-collapsible "+d[6]+" svelte-gbhym3"))&&p(t,"class",l),(!r||72&u)&&$(t,"is-collapsed",d[3]),(!r||80&u)&&$(t,"is-expandable",d[4]),4&u&&R(t,"--sticky-header-top",`${d[2]}px`)},i(d){r||(g(h,d),g(f),r=!0)},o(d){y(h,d),y(f),r=!1},d(d){d&&v(t),h&&h.d(d),f&&f.d(),x=!1,w()}}}function St(n,t,s){let e;const c=["collapsed","stickyHeader","expandable","isHeaderStuck","stickyHeaderTop","toggle"];let o,a,i=U(t,c),{$$slots:l={},$$scope:r}=t;const x=$t(l);let{collapsed:w=!1}=t,{stickyHeader:S=!1}=t,{expandable:h=!0}=t,{isHeaderStuck:f=!1}=t,{stickyHeaderTop:d=-.5}=t;const u=W(w);F(n,u,m=>s(3,o=m));const at=gt(u,m=>m),I=W(h);F(n,I,m=>s(4,a=m));let E,Z=!1;function O(m){h?u.set(m):u.set(!0)}const V=function(){O(!o)};return ft(ct,{collapsed:at,setCollapsed:O,toggle:V,expandable:I}),n.$$set=m=>{t=L(L({},t),z(m)),s(22,i=U(t,c)),"collapsed"in m&&s(10,w=m.collapsed),"stickyHeader"in m&&s(1,S=m.stickyHeader),"expandable"in m&&s(11,h=m.expandable),"isHeaderStuck"in m&&s(0,f=m.isHeaderStuck),"stickyHeaderTop"in m&&s(2,d=m.stickyHeaderTop),"$$scope"in m&&s(13,r=m.$$scope)},n.$$.update=()=>{16&n.$$.dirty&&s(11,h=a),2048&n.$$.dirty&&I.set(h),8&n.$$.dirty&&s(10,w=o),1024&n.$$.dirty&&u.set(w),2048&n.$$.dirty&&(h||u.set(!0)),1024&n.$$.dirty&&(w?(clearTimeout(E),E=setTimeout(()=>{s(5,Z=!1)},200)):(clearTimeout(E),s(5,Z=!0))),s(6,{class:e}=i,e)},[f,S,d,o,a,Z,e,u,I,x,w,h,V,r,l,()=>{s(0,f=!0)},()=>{s(0,f=!1)}]}class At extends T{constructor(t){super(),M(this,t,St,Ht,B,{collapsed:10,stickyHeader:1,expandable:11,isHeaderStuck:0,stickyHeaderTop:2,toggle:12})}get toggle(){return this.$$.ctx[12]}}function _t(n){let t,s;return{c(){t=_("svg"),s=_("path"),p(s,"fill-rule","evenodd"),p(s,"clip-rule","evenodd"),p(s,"d","M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z"),p(s,"fill","currentColor"),p(t,"width","16"),p(t,"height","16"),p(t,"viewBox","0 0 16 16"),p(t,"fill","none"),p(t,"xmlns","http://www.w3.org/2000/svg")},m(e,c){C(e,t,c),b(t,s)},p:k,i:k,o:k,d(e){e&&v(t)}}}class Tt extends T{constructor(t){super(),M(this,t,null,_t,B,{})}}function Mt(n){let t,s,e;return{c(){t=_("svg"),s=_("path"),e=_("path"),p(s,"fill-rule","evenodd"),p(s,"clip-rule","evenodd"),p(s,"d","M5.26045 11.9272C5.07304 12.1146 5.07304 12.4185 5.26045 12.606C5.44792 12.7934 5.75183 12.7934 5.93929 12.606L7.99988 10.5454L10.0605 12.606C10.2479 12.7934 10.5518 12.7934 10.7393 12.606C10.9267 12.4185 10.9267 12.1146 10.7393 11.9272L8.33929 9.52716C8.15184 9.33975 7.84792 9.33975 7.66046 9.52716L5.26045 11.9272Z"),p(s,"fill","currentColor"),p(e,"d","M10.7393 3.39387C10.9267 3.58132 10.9267 3.88524 10.7393 4.07269L8.33929 6.47269C8.24928 6.56271 8.12718 6.61328 7.99988 6.61328C7.87258 6.61328 7.75049 6.56271 7.66046 6.47269L5.26045 4.0727C5.07304 3.88524 5.07304 3.58132 5.26045 3.39387C5.44792 3.20642 5.75183 3.20642 5.93929 3.39387L7.99988 5.45447L10.0605 3.39387C10.2479 3.20642 10.5518 3.20642 10.7393 3.39387Z"),p(e,"fill","currentColor"),p(t,"width","16"),p(t,"height","16"),p(t,"viewBox","0 0 16 16"),p(t,"fill","none"),p(t,"xmlns","http://www.w3.org/2000/svg")},m(c,o){C(c,t,o),b(t,s),b(t,e)},p:k,i:k,o:k,d(c){c&&v(t)}}}class Bt extends T{constructor(t){super(),M(this,t,null,Mt,B,{})}}function It(n){let t,s;return t=new Bt({}),{c(){G(t.$$.fragment)},m(e,c){J(t,e,c),s=!0},i(e){s||(g(t.$$.fragment,e),s=!0)},o(e){y(t.$$.fragment,e),s=!1},d(e){K(t,e)}}}function zt(n){let t,s;return t=new Tt({}),{c(){G(t.$$.fragment)},m(e,c){J(t,e,c),s=!0},i(e){s||(g(t.$$.fragment,e),s=!0)},o(e){y(t.$$.fragment,e),s=!1},d(e){K(t,e)}}}function Ut(n){let t,s,e,c;const o=[zt,It],a=[];function i(l,r){return l[0]?0:1}return t=i(n),s=a[t]=o[t](n),{c(){s.c(),e=nt()},m(l,r){a[t].m(l,r),C(l,e,r),c=!0},p(l,r){let x=t;t=i(l),t!==x&&(q(),y(a[x],1,1,()=>{a[x]=null}),D(),s=a[t],s||(s=a[t]=o[t](l),s.c()),g(s,1),s.m(e.parentNode,e))},i(l){c||(g(s),c=!0)},o(l){y(s),c=!1},d(l){l&&v(e),a[t].d(l)}}}function Et(n){let t,s;const e=[{variant:"ghost-block"},{color:"neutral"},{size:1},n[3]];let c={$$slots:{default:[Ut]},$$scope:{ctx:n}};for(let o=0;o<e.length;o+=1)c=L(c,e[o]);return t=new vt({props:c}),t.$on("click",n[2]),{c(){G(t.$$.fragment)},m(o,a){J(t,o,a),s=!0},p(o,[a]){const i=8&a?et(e,[e[0],e[1],e[2],yt(o[3])]):{};33&a&&(i.$$scope={dirty:a,ctx:o}),t.$set(i)},i(o){s||(g(t.$$.fragment,o),s=!0)},o(o){y(t.$$.fragment,o),s=!1},d(o){K(t,o)}}}function Zt(n,t,s){const e=[];let c,o=U(t,e);const{collapsed:a,setCollapsed:i}=bt();return F(n,a,l=>s(0,c=l)),n.$$set=l=>{t=L(L({},t),z(l)),s(3,o=U(t,e))},[c,a,function(){i(!c)},o]}class Pt extends T{constructor(t){super(),M(this,t,Zt,Et,B,{})}}export{At as C,jt as T,Pt as a,Bt as b,Tt as c,bt as g};

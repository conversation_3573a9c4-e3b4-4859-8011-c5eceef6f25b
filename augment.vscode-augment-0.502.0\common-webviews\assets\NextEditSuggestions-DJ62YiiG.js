import{al as x0,A as k0,ai as b0,S as N,i as B,s as I,D as F,c as s,a2 as H,e as A,n as j,h as S,aj as e1,V as U,N as a3,a8 as Q2,a5 as M1,Q as W,af as d1,ac as j1,T as U1,q as l1,t as _,r as c1,u as x,a3 as U3,a as V1,j as _0,J as E1,a1 as n3,f as C,K as P1,L as O1,M as R1,g as P3,E as O,F as R,G as z,b as m,Y as G,P as q,at as G3,ay as Y2,U as A0,Z as y3,ag as X2,ah as e3,az as J,aC as S0,aa as t0,ae as F0,ak as O3,ad as M0,C as j0,a6 as E0,a7 as P0}from"./SpinnerAugment-CL9SZpf8.js";import{e as a1,u as R3,d as O0,g as z3,o as n0,W as V,h as n1}from"./IconButtonAugment-C4xMcLhX.js";import{S as z1,C as W3}from"./next-edit-types-904A5ehg.js";import{a as X,g as H3,b as J3,f as o3,c as v1,S as R0,d as Q3,I as e0,i as z0,e as o0,n as N1,A as i0,h as N0,s as T1,j as B0,_ as l3,k as r0,l as y1,m as Y3,o as c3,p as I0,q as X3,r as Z0,t as t2}from"./IconFilePath-j5ToM371.js";import{A as D0}from"./async-messaging-CtwQrvzD.js";import{r as K0,D as T0}from"./Drawer-DIQAznt-.js";import{o as b1}from"./keypress-DD1aQVr0.js";import{V as V0}from"./VSCodeCodicon-BxoMn_1r.js";import{e as q0,r as U0}from"./LanguageIcon-BQz1eaw5.js";import{a as G0,b as n2,M as W0}from"./index-iuo-Ho0S.js";import"./index-BskWw2a8.js";import{B as H0}from"./ButtonAugment-iwbEjzvh.js";import"./index-D2Ut0gK2.js";import"./ellipsis-B3ZqaMmA.js";import"./preload-helper-Dv6uf1Os.js";const s0=Symbol("code-roll-selection-context");function B1(){let n=b0(s0);return n||(n=a0({})),n}function a0(n){return x0(s0,k0(n))}function g1(n){return n.activeSuggestion??n.selectedSuggestion??n.nextSuggestion}function Y1(n){return n.activeSuggestion?"active":n.selectedSuggestion?"select":"next"}function w1(n,t,e){return t.activeSuggestion?X(t.activeSuggestion,n)?e?"select":"active":"none":t.selectedSuggestion?X(t.selectedSuggestion,n)?e?"active":"select":"none":t.nextSuggestion&&X(t.nextSuggestion,n)?"next":"none"}function i3(n){return["",`--augment-code-roll-selection-background: var(--augment-code-roll-item-background-${n})`,`--augment-code-roll-selection-color: var(--augment-code-roll-item-color-${n})`,"--augment-code-roll-selection-border: var(--augment-code-roll-selection-background)",""].join(";")}const C3={scrollContainer:document.documentElement,scrollIntoView:{behavior:"smooth",block:"nearest"},scrollDelayMS:100,useSmartBlockAlignment:!0,doScroll:!0};function N3(n,t=C3){let e,o=Object.assign({},C3,t);function i(r){let{doScroll:l,scrollIntoView:a,scrollDelayMS:c,useSmartBlockAlignment:u,scrollContainer:d}=Object.assign({},o,r);l&&(u&&n.getBoundingClientRect().height>((d==null?void 0:d.getBoundingClientRect().height)??1/0)&&(a=Object.assign({},a,{block:"start"})),e=setTimeout(()=>{const h=n.getBoundingClientRect();if(h.bottom===0&&h.top===0&&h.height===0&&h.width===0)return;const f=function(g,p,w){const $=g.getBoundingClientRect(),b=p.getBoundingClientRect();if(w==="nearest")if($.bottom>b.bottom)w="end";else{if(!($.top<b.top))return p.scrollTop;w="start"}return $.height>b.height||w==="start"?p.scrollTop+$.top:w==="end"?p.scrollTop+$.bottom-b.height:p.scrollTop+$.top-(b.height-$.height)/2}(n,d,(a==null?void 0:a.block)??C3.scrollIntoView.block);d.scrollTo({top:f,behavior:a==null?void 0:a.behavior})},c))}return i(o),{update:i,destroy(){clearTimeout(e)}}}const{Map:J0}=z3;function e2(n,t,e){const o=n.slice();return o[9]=t[e][0],o[10]=t[e][1],o}function o2(n,t,e){const o=n.slice();o[13]=t[e];const i=w1(o[13],o[4],!0);return o[14]=i,o}function i2(n){let t,e,o,i,r,l,a;function c(){return n[6](n[13])}function u(){return n[7](n[13])}return{c(){t=F("div"),s(t,"role","button"),s(t,"tabindex","0"),s(t,"title",e=n[13].lineRange.start+1+": "+n[13].result.changeDescription),s(t,"class",o="c-suggestion-tree__tick-mark "+n[14]+" svelte-ffrg54"),s(t,"style",i=s2(n[14]))},m(d,h){A(d,t,h),l||(a=[W(t,"click",c),W(t,"keydown",function(){d1(b1("Space",u))&&b1("Space",u).apply(this,arguments)}),j1(r=N3.call(null,t,{scrollContainer:n[3],doScroll:X(n[13],g1(n[4])),scrollIntoView:{behavior:"smooth",block:"nearest"}}))],l=!0)},p(d,h){n=d,2&h&&e!==(e=n[13].lineRange.start+1+": "+n[13].result.changeDescription)&&s(t,"title",e),18&h&&o!==(o="c-suggestion-tree__tick-mark "+n[14]+" svelte-ffrg54")&&s(t,"class",o),18&h&&i!==(i=s2(n[14]))&&s(t,"style",i),r&&d1(r.update)&&26&h&&r.update.call(null,{scrollContainer:n[3],doScroll:X(n[13],g1(n[4])),scrollIntoView:{behavior:"smooth",block:"nearest"}})},d(d){d&&S(t),l=!1,U1(a)}}}function r2(n,t){let e,o,i,r,l=a1(t[10]),a=[];for(let c=0;c<l.length;c+=1)a[c]=i2(o2(t,l,c));return{key:n,first:null,c(){e=F("div"),i=U();for(let c=0;c<a.length;c+=1)a[c].c();r=a3(),s(e,"class","c-suggestion-tree__file-divider svelte-ffrg54"),s(e,"title",o=t[9]),this.first=e},m(c,u){A(c,e,u),A(c,i,u);for(let d=0;d<a.length;d+=1)a[d]&&a[d].m(c,u);A(c,r,u)},p(c,u){if(t=c,2&u&&o!==(o=t[9])&&s(e,"title",o),27&u){let d;for(l=a1(t[10]),d=0;d<l.length;d+=1){const h=o2(t,l,d);a[d]?a[d].p(h,u):(a[d]=i2(h),a[d].c(),a[d].m(r.parentNode,r))}for(;d<a.length;d+=1)a[d].d(1);a.length=l.length}},d(c){c&&(S(e),S(i),S(r)),Q2(a,c)}}}function Q0(n){let t,e=[],o=new J0,i=a1(n[1].entries());const r=l=>l[9];for(let l=0;l<i.length;l+=1){let a=e2(n,i,l),c=r(a);o.set(c,e[l]=r2(c,a))}return{c(){t=F("div");for(let l=0;l<e.length;l+=1)e[l].c();s(t,"class","c-suggestion-tree__minimized svelte-ffrg54"),H(t,"hidden",!n[2])},m(l,a){A(l,t,a);for(let c=0;c<e.length;c+=1)e[c]&&e[c].m(t,null);n[8](t)},p(l,[a]){27&a&&(i=a1(l[1].entries()),e=R3(e,a,r,1,l,i,o,t,O0,r2,null,e2)),4&a&&H(t,"hidden",!l[2])},i:j,o:j,d(l){l&&S(t);for(let a=0;a<e.length;a+=1)e[a].d();n[8](null)}}}function s2(n){let t="var(--ds-color-neutral-7, currentColor)";return n==="active"&&(t="var(--augment-code-roll-item-background-active, currentColor)"),n==="select"&&(t="currentColor"),n==="next"&&(t="var(--ds-color-neutral-10, currentColor)"),`--augment-code-roll-selection-background: ${t}`}function Y0(n,t,e){let o,{onCodeAction:i}=t,{sortedPathSuggestionsMap:r=new Map}=t,{show:l=!0}=t;const a=B1();let c;return e1(n,a,u=>e(4,o=u)),n.$$set=u=>{"onCodeAction"in u&&e(0,i=u.onCodeAction),"sortedPathSuggestionsMap"in u&&e(1,r=u.sortedPathSuggestionsMap),"show"in u&&e(2,l=u.show)},[i,r,l,c,o,a,u=>i("select",u),u=>i("select",u),function(u){M1[u?"unshift":"push"](()=>{c=u,e(3,c)})}]}class X0 extends N{constructor(t){super(),B(this,t,Y0,Q0,I,{onCodeAction:0,sortedPathSuggestionsMap:1,show:2})}}const m3={duration:300,easing:"ease-out"},tt=(n,t=m3)=>{const e=n.querySelector("summary"),o=n.querySelector(".c-detail__content");if(!e||!o)return;t={...m3,...t};const i=/^(tb|vertical)/.test(getComputedStyle(o).writingMode);let r=!1;const l=d=>{r=!0,d&&(n.open=!0),n.dispatchEvent(new CustomEvent(d?"open-start":"close-start",{detail:n}));const h=o[i?"clientWidth":"clientHeight"],f=o.animate({blockSize:d?["0",`${h}px`]:[`${h}px`,"0"]},t);f.oncancel=f.onfinish=f.onremove=()=>{n.dispatchEvent(new CustomEvent(d?"open-end":"close-end",{detail:n})),d||(n.open=!1),r=!1}},a=new MutationObserver(d=>{for(const h of d)if(h.type==="attributes"&&h.attributeName==="open"){if(r)return;n.open&&l(!0)}});a.observe(n,{attributes:!0});const c=d=>{d.preventDefault(),r||l(!n.open)},u=b1("Enter",c);return e.addEventListener("click",c),e.addEventListener("keypress",u),{destroy(){a.disconnect(),e.removeEventListener("click",c),e.removeEventListener("keypress",u)},update(d=m3){t={...t,...d}}}},nt=n=>({}),a2=n=>({}),et=n=>({}),l2=n=>({});function ot(n){let t,e,o,i,r,l;const a=n[11].summary,c=E1(a,n,n[10],a2),u=n[11].default,d=E1(u,n,n[10],null);let h=[n[7],{class:r="c-detail "+n[2]}],f={};for(let g=0;g<h.length;g+=1)f=V1(f,h[g]);return{c(){t=F("div"),e=F("div"),c&&c.c(),o=U(),i=F("div"),d&&d.c(),s(e,"class","c-detail__summary svelte-hwtbr4"),s(i,"class","c-detail__content svelte-hwtbr4"),n3(t,f),H(t,"svelte-hwtbr4",!0)},m(g,p){A(g,t,p),C(t,e),c&&c.m(e,null),C(t,o),C(t,i),d&&d.m(i,null),l=!0},p(g,p){c&&c.p&&(!l||1024&p)&&P1(c,a,g,g[10],l?R1(a,g[10],p,nt):O1(g[10]),a2),d&&d.p&&(!l||1024&p)&&P1(d,u,g,g[10],l?R1(u,g[10],p,null):O1(g[10]),null),n3(t,f=P3(h,[128&p&&g[7],(!l||4&p&&r!==(r="c-detail "+g[2]))&&{class:r}])),H(t,"svelte-hwtbr4",!0)},i(g){l||(x(c,g),x(d,g),l=!0)},o(g){_(c,g),_(d,g),l=!1},d(g){g&&S(t),c&&c.d(g),d&&d.d(g)}}}function it(n){let t,e,o,i,r,l,a,c,u,d,h;o=new V0({props:{icon:"chevron-down",class:"c-detail__chevron"}});const f=n[11].summary,g=E1(f,n,n[10],l2),p=n[11].default,w=E1(p,n,n[10],null);let $=[n[7],{style:a="--au-detail-duration: "+n[4].duration+"ms"},{class:c="c-detail "+n[2]}],b={};for(let y=0;y<$.length;y+=1)b=V1(b,$[y]);return{c(){t=F("details"),e=F("summary"),O(o.$$.fragment),i=U(),g&&g.c(),r=U(),l=F("div"),w&&w.c(),s(e,"class","c-detail__summary svelte-hwtbr4"),s(l,"class","c-detail__content svelte-hwtbr4"),n3(t,b),H(t,"c-detail__rotate",n[3]),H(t,"svelte-hwtbr4",!0)},m(y,v){A(y,t,v),C(t,e),R(o,e,null),C(e,i),g&&g.m(e,null),C(t,r),C(t,l),w&&w.m(l,null),t.open=n[0],u=!0,d||(h=[W(t,"toggle",n[12]),j1(tt.call(null,t,n[4])),W(t,"close-start",n[6]("close")),W(t,"open-start",n[6]("open")),W(t,"close-end",n[5]("close")),W(t,"open-end",n[5]("open"))],d=!0)},p(y,v){g&&g.p&&(!u||1024&v)&&P1(g,f,y,y[10],u?R1(f,y[10],v,et):O1(y[10]),l2),w&&w.p&&(!u||1024&v)&&P1(w,p,y,y[10],u?R1(p,y[10],v,null):O1(y[10]),null),n3(t,b=P3($,[128&v&&y[7],{style:a},(!u||4&v&&c!==(c="c-detail "+y[2]))&&{class:c}])),1&v&&(t.open=y[0]),H(t,"c-detail__rotate",y[3]),H(t,"svelte-hwtbr4",!0)},i(y){u||(x(o.$$.fragment,y),x(g,y),x(w,y),u=!0)},o(y){_(o.$$.fragment,y),_(g,y),_(w,y),u=!1},d(y){y&&S(t),z(o),g&&g.d(y),w&&w.d(y),d=!1,U1(h)}}}function rt(n){let t,e,o,i;const r=[it,ot],l=[];function a(c,u){return c[1]?0:1}return t=a(n),e=l[t]=r[t](n),{c(){e.c(),o=a3()},m(c,u){l[t].m(c,u),A(c,o,u),i=!0},p(c,[u]){let d=t;t=a(c),t===d?l[t].p(c,u):(l1(),_(l[d],1,1,()=>{l[d]=null}),c1(),e=l[t],e?e.p(c,u):(e=l[t]=r[t](c),e.c()),x(e,1),e.m(o.parentNode,o))},i(c){i||(x(e),i=!0)},o(c){_(e),i=!1},d(c){c&&S(o),l[t].d(c)}}}function st(n,t,e){const o=["open","duration","expandable","onChangeOpen","class"];let i=U3(t,o),{$$slots:r={},$$scope:l}=t,{open:a=!0}=t,{duration:c=300}=t,{expandable:u=!0}=t,{onChangeOpen:d}=t,{class:h=""}=t;const f=typeof c=="number"?{duration:c}:c;let g=a;return n.$$set=p=>{t=V1(V1({},t),_0(p)),e(7,i=U3(t,o)),"open"in p&&e(0,a=p.open),"duration"in p&&e(8,c=p.duration),"expandable"in p&&e(1,u=p.expandable),"onChangeOpen"in p&&e(9,d=p.onChangeOpen),"class"in p&&e(2,h=p.class),"$$scope"in p&&e(10,l=p.$$scope)},n.$$.update=()=>{1&n.$$.dirty&&e(3,g=a)},[a,u,h,g,f,p=>()=>{e(3,g=p!=="close"),d==null||d(p==="open")},p=>()=>{e(3,g=p==="open")},i,c,d,l,r,function(){a=this.open,e(0,a)}]}class l0 extends N{constructor(t){super(),B(this,t,st,rt,I,{open:0,duration:8,expandable:1,onChangeOpen:9,class:2})}}const h1=Symbol.for("@ts-pattern/matcher"),at=Symbol.for("@ts-pattern/isVariadic"),r3="@ts-pattern/anonymous-select-key",L3=n=>!!(n&&typeof n=="object"),X1=n=>n&&!!n[h1],s1=(n,t,e)=>{if(X1(n)){const o=n[h1](),{matched:i,selections:r}=o.match(t);return i&&r&&Object.keys(r).forEach(l=>e(l,r[l])),i}if(L3(n)){if(!L3(t))return!1;if(Array.isArray(n)){if(!Array.isArray(t))return!1;let o=[],i=[],r=[];for(const l of n.keys()){const a=n[l];X1(a)&&a[at]?r.push(a):r.length?i.push(a):o.push(a)}if(r.length){if(r.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<o.length+i.length)return!1;const l=t.slice(0,o.length),a=i.length===0?[]:t.slice(-i.length),c=t.slice(o.length,i.length===0?1/0:-i.length);return o.every((u,d)=>s1(u,l[d],e))&&i.every((u,d)=>s1(u,a[d],e))&&(r.length===0||s1(r[0],c,e))}return n.length===t.length&&n.every((l,a)=>s1(l,t[a],e))}return Reflect.ownKeys(n).every(o=>{const i=n[o];return(o in t||X1(r=i)&&r[h1]().matcherType==="optional")&&s1(i,t[o],e);var r})}return Object.is(t,n)},$1=n=>{var t,e,o;return L3(n)?X1(n)?(t=(e=(o=n[h1]()).getSelectionKeys)==null?void 0:e.call(o))!=null?t:[]:Array.isArray(n)?q1(n,$1):q1(Object.values(n),$1):[]},q1=(n,t)=>n.reduce((e,o)=>e.concat(t(o)),[]);function Y(n){return Object.assign(n,{optional:()=>lt(n),and:t=>D(n,t),or:t=>ct(n,t),select:t=>t===void 0?c2(n):c2(t,n)})}function lt(n){return Y({[h1]:()=>({match:t=>{let e={};const o=(i,r)=>{e[i]=r};return t===void 0?($1(n).forEach(i=>o(i,void 0)),{matched:!0,selections:e}):{matched:s1(n,t,o),selections:e}},getSelectionKeys:()=>$1(n),matcherType:"optional"})})}function D(...n){return Y({[h1]:()=>({match:t=>{let e={};const o=(i,r)=>{e[i]=r};return{matched:n.every(i=>s1(i,t,o)),selections:e}},getSelectionKeys:()=>q1(n,$1),matcherType:"and"})})}function ct(...n){return Y({[h1]:()=>({match:t=>{let e={};const o=(i,r)=>{e[i]=r};return q1(n,$1).forEach(i=>o(i,void 0)),{matched:n.some(i=>s1(i,t,o)),selections:e}},getSelectionKeys:()=>q1(n,$1),matcherType:"or"})})}function P(n){return{[h1]:()=>({match:t=>({matched:!!n(t)})})}}function c2(...n){const t=typeof n[0]=="string"?n[0]:void 0,e=n.length===2?n[1]:typeof n[0]=="string"?void 0:n[0];return Y({[h1]:()=>({match:o=>{let i={[t??r3]:o};return{matched:e===void 0||s1(e,o,(r,l)=>{i[r]=l}),selections:i}},getSelectionKeys:()=>[t??r3].concat(e===void 0?[]:$1(e))})})}function i1(n){return typeof n=="number"}function f1(n){return typeof n=="string"}function p1(n){return typeof n=="bigint"}Y(P(function(n){return!0}));const C1=n=>Object.assign(Y(n),{startsWith:t=>{return C1(D(n,(e=t,P(o=>f1(o)&&o.startsWith(e)))));var e},endsWith:t=>{return C1(D(n,(e=t,P(o=>f1(o)&&o.endsWith(e)))));var e},minLength:t=>C1(D(n,(e=>P(o=>f1(o)&&o.length>=e))(t))),length:t=>C1(D(n,(e=>P(o=>f1(o)&&o.length===e))(t))),maxLength:t=>C1(D(n,(e=>P(o=>f1(o)&&o.length<=e))(t))),includes:t=>{return C1(D(n,(e=t,P(o=>f1(o)&&o.includes(e)))));var e},regex:t=>{return C1(D(n,(e=t,P(o=>f1(o)&&!!o.match(e)))));var e}});C1(P(f1));const r1=n=>Object.assign(Y(n),{between:(t,e)=>r1(D(n,((o,i)=>P(r=>i1(r)&&o<=r&&i>=r))(t,e))),lt:t=>r1(D(n,(e=>P(o=>i1(o)&&o<e))(t))),gt:t=>r1(D(n,(e=>P(o=>i1(o)&&o>e))(t))),lte:t=>r1(D(n,(e=>P(o=>i1(o)&&o<=e))(t))),gte:t=>r1(D(n,(e=>P(o=>i1(o)&&o>=e))(t))),int:()=>r1(D(n,P(t=>i1(t)&&Number.isInteger(t)))),finite:()=>r1(D(n,P(t=>i1(t)&&Number.isFinite(t)))),positive:()=>r1(D(n,P(t=>i1(t)&&t>0))),negative:()=>r1(D(n,P(t=>i1(t)&&t<0)))});r1(P(i1));const m1=n=>Object.assign(Y(n),{between:(t,e)=>m1(D(n,((o,i)=>P(r=>p1(r)&&o<=r&&i>=r))(t,e))),lt:t=>m1(D(n,(e=>P(o=>p1(o)&&o<e))(t))),gt:t=>m1(D(n,(e=>P(o=>p1(o)&&o>e))(t))),lte:t=>m1(D(n,(e=>P(o=>p1(o)&&o<=e))(t))),gte:t=>m1(D(n,(e=>P(o=>p1(o)&&o>=e))(t))),positive:()=>m1(D(n,P(t=>p1(t)&&t>0))),negative:()=>m1(D(n,P(t=>p1(t)&&t<0)))});m1(P(p1)),Y(P(function(n){return typeof n=="boolean"})),Y(P(function(n){return typeof n=="symbol"})),Y(P(function(n){return n==null})),Y(P(function(n){return n!=null}));class ut extends Error{constructor(t){let e;try{e=JSON.stringify(t)}catch{e=t}super(`Pattern matching error: no pattern matches value ${e}`),this.input=void 0,this.input=t}}const x3={matched:!1,value:void 0};class s3{constructor(t,e){this.input=void 0,this.state=void 0,this.input=t,this.state=e}with(...t){if(this.state.matched)return this;const e=t[t.length-1],o=[t[0]];let i;t.length===3&&typeof t[1]=="function"?i=t[1]:t.length>2&&o.push(...t.slice(1,t.length-1));let r=!1,l={};const a=(u,d)=>{r=!0,l[u]=d},c=!o.some(u=>s1(u,this.input,a))||i&&!i(this.input)?x3:{matched:!0,value:e(r?r3 in l?l[r3]:l:this.input,this.input)};return new s3(this.input,c)}when(t,e){if(this.state.matched)return this;const o=!!t(this.input);return new s3(this.input,o?{matched:!0,value:e(this.input,this.input)}:x3)}otherwise(t){return this.state.matched?this.state.value:t(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new ut(this.input)}run(){return this.exhaustive()}returnType(){return this}}function dt(n){let t,e,o,i,r,l,a,c,u,d,h,f;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_addition_light"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("rect"),h=m("rect"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#0A84FF"),s(d,"id","Rectangle"),s(d,"x","2.25"),s(d,"y","0"),s(d,"width","1.5"),s(d,"height","6"),s(d,"rx","0.75"),s(h,"id","Rectangle-Copy"),s(h,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),s(h,"x","2.25"),s(h,"y","1.13686838e-13"),s(h,"width","1.5"),s(h,"height","6"),s(h,"rx","0.75"),s(u,"id","Group"),s(u,"transform","translate(9, 1)"),s(u,"fill",f=n[0]?n[1]:"#34C759"),s(i,"id","nextedit_addition_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,p){A(g,t,p),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d),C(u,h)},p(g,[p]){3&p&&c!==(c=g[0]?g[1]:"#0A84FF")&&s(r,"fill",c),3&p&&f!==(f=g[0]?g[1]:"#34C759")&&s(u,"fill",f)},i:j,o:j,d(g){g&&S(t)}}}function gt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class ht extends N{constructor(t){super(),B(this,t,gt,dt,I,{mask:0,maskColor:1})}}function ft(n){let t,e,o,i,r,l,a,c,u,d,h,f;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_addition_dark"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("rect"),h=m("rect"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#168AFF"),s(d,"id","Rectangle"),s(d,"x","2.25"),s(d,"y","0"),s(d,"width","1.5"),s(d,"height","6"),s(d,"rx","0.75"),s(h,"id","Rectangle-Copy"),s(h,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),s(h,"x","2.25"),s(h,"y","1.13686838e-13"),s(h,"width","1.5"),s(h,"height","6"),s(h,"rx","0.75"),s(u,"id","Group"),s(u,"transform","translate(9, 1)"),s(u,"fill",f=n[0]?n[1]:"#30D158"),s(i,"id","nextedit_addition_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,p){A(g,t,p),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d),C(u,h)},p(g,[p]){3&p&&c!==(c=g[0]?g[1]:"#168AFF")&&s(r,"fill",c),3&p&&f!==(f=g[0]?g[1]:"#30D158")&&s(u,"fill",f)},i:j,o:j,d(g){g&&S(t)}}}function pt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Ct extends N{constructor(t){super(),B(this,t,pt,ft,I,{mask:0,maskColor:1})}}function mt(n){let t,e,o,i,r,l,a,c,u,d,h;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_deletion_light"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("rect"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#0A84FF"),s(d,"id","Rectangle-Copy"),s(d,"transform","translate(3, 0.75) rotate(90) translate(-3, -0.75)"),s(d,"x","2.25"),s(d,"y","-2.25"),s(d,"width","1.5"),s(d,"height","6"),s(d,"rx","0.75"),s(u,"id","Group"),s(u,"transform","translate(9, 3.25)"),s(u,"fill",h=n[0]?n[1]:"#FF5D4E"),s(i,"id","nextedit_deletion_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(f,g){A(f,t,g),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d)},p(f,[g]){3&g&&c!==(c=f[0]?f[1]:"#0A84FF")&&s(r,"fill",c),3&g&&h!==(h=f[0]?f[1]:"#FF5D4E")&&s(u,"fill",h)},i:j,o:j,d(f){f&&S(t)}}}function vt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class wt extends N{constructor(t){super(),B(this,t,vt,mt,I,{mask:0,maskColor:1})}}function $t(n){let t,e,o,i,r,l,a,c,u,d,h;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_deletion_dark"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("rect"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#168AFF"),s(d,"id","Rectangle-Copy"),s(d,"transform","translate(3, 0.75) rotate(90) translate(-3, -0.75)"),s(d,"x","2.25"),s(d,"y","-2.25"),s(d,"width","1.5"),s(d,"height","6"),s(d,"rx","0.75"),s(u,"id","Group"),s(u,"transform","translate(9, 3.25)"),s(u,"fill",h=n[0]?n[1]:"#FF7E72"),s(i,"id","nextedit_deletion_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(f,g){A(f,t,g),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d)},p(f,[g]){3&g&&c!==(c=f[0]?f[1]:"#168AFF")&&s(r,"fill",c),3&g&&h!==(h=f[0]?f[1]:"#FF7E72")&&s(u,"fill",h)},i:j,o:j,d(f){f&&S(t)}}}function yt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Lt extends N{constructor(t){super(),B(this,t,yt,$t,I,{mask:0,maskColor:1})}}function xt(n){let t,e,o,i,r,l,a,c,u,d,h,f,g,p,w;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_change_light"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("g"),h=m("rect"),f=m("rect"),g=m("g"),p=m("path"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#0A84FF"),s(h,"id","Rectangle-Copy"),s(h,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),s(h,"x","1.5"),s(h,"y","-1.5"),s(h,"width","1"),s(h,"height","4"),s(h,"rx","0.5"),s(f,"id","Rectangle-Copy"),s(f,"transform","translate(2, 4.5) rotate(90) translate(-2, -4.5)"),s(f,"x","1.5"),s(f,"y","2.5"),s(f,"width","1"),s(f,"height","4"),s(f,"rx","0.5"),s(d,"id","Group"),s(d,"transform","translate(0, 1.5)"),s(p,"d","M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z"),s(p,"id","Rectangle-Copy"),s(p,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),s(g,"id","Group"),s(g,"transform","translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"),s(u,"id","Group-2"),s(u,"transform","translate(10.5, 1.5)"),s(u,"fill",w=n[0]?n[1]:"#F4A414"),s(i,"id","nextedit_change_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m($,b){A($,t,b),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d),C(d,h),C(d,f),C(u,g),C(g,p)},p($,[b]){3&b&&c!==(c=$[0]?$[1]:"#0A84FF")&&s(r,"fill",c),3&b&&w!==(w=$[0]?$[1]:"#F4A414")&&s(u,"fill",w)},i:j,o:j,d($){$&&S(t)}}}function kt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class u2 extends N{constructor(t){super(),B(this,t,kt,xt,I,{mask:0,maskColor:1})}}function bt(n){let t,e,o,i,r,l,a,c,u,d,h,f,g,p,w;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_change_dark"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("g"),h=m("rect"),f=m("rect"),g=m("g"),p=m("path"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#168AFF"),s(h,"id","Rectangle-Copy"),s(h,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),s(h,"x","1.5"),s(h,"y","-1.5"),s(h,"width","1"),s(h,"height","4"),s(h,"rx","0.5"),s(f,"id","Rectangle-Copy"),s(f,"transform","translate(2, 4.5) rotate(90) translate(-2, -4.5)"),s(f,"x","1.5"),s(f,"y","2.5"),s(f,"width","1"),s(f,"height","4"),s(f,"rx","0.5"),s(d,"id","Group"),s(d,"transform","translate(0, 1.5)"),s(p,"d","M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z"),s(p,"id","Rectangle-Copy"),s(p,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),s(g,"id","Group"),s(g,"transform","translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"),s(u,"id","Group-2"),s(u,"transform","translate(10.5, 1.5)"),s(u,"fill",w=n[0]?n[1]:"#FFC255"),s(i,"id","nextedit_change_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m($,b){A($,t,b),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d),C(d,h),C(d,f),C(u,g),C(g,p)},p($,[b]){3&b&&c!==(c=$[0]?$[1]:"#168AFF")&&s(r,"fill",c),3&b&&w!==(w=$[0]?$[1]:"#FFC255")&&s(u,"fill",w)},i:j,o:j,d($){$&&S(t)}}}function _t(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class d2 extends N{constructor(t){super(),B(this,t,_t,bt,I,{mask:0,maskColor:1})}}function At(n){let t,e,o,i,r,l,a,c,u,d,h;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_applied_light"),i=m("g"),r=m("g"),l=m("path"),c=m("g"),u=m("path"),d=m("rect"),s(l,"d","M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z"),s(l,"id","Path"),s(r,"id","􀆅"),s(r,"transform","translate(8.5216, 0.8311)"),s(r,"fill",a=n[0]?n[1]:"#34C759"),s(r,"fill-rule","nonzero"),s(u,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(u,"id","Combined-Shape"),s(u,"fill-rule","nonzero"),s(d,"id","Rectangle"),s(d,"opacity","0.005"),s(d,"x","0"),s(d,"y","0"),s(d,"width","16"),s(d,"height","16"),s(c,"id","Pencil_Base"),s(c,"fill",h=n[0]?n[1]:"#000000"),q(c,"opacity",n[0]?"1":"0.2"),s(i,"id","nextedit_applied_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(f,g){A(f,t,g),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(i,c),C(c,u),C(c,d)},p(f,[g]){3&g&&a!==(a=f[0]?f[1]:"#34C759")&&s(r,"fill",a),3&g&&h!==(h=f[0]?f[1]:"#000000")&&s(c,"fill",h),1&g&&q(c,"opacity",f[0]?"1":"0.2")},i:j,o:j,d(f){f&&S(t)}}}function St(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Ft extends N{constructor(t){super(),B(this,t,St,At,I,{mask:0,maskColor:1})}}function Mt(n){let t,e,o,i,r,l,a,c,u,d,h;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_applied_dark"),i=m("g"),r=m("g"),l=m("path"),c=m("g"),u=m("path"),d=m("rect"),s(l,"d","M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z"),s(l,"id","Path"),s(r,"id","􀆅"),s(r,"transform","translate(8.5167, 0.8311)"),s(r,"fill",a=n[0]?n[1]:"#30D158"),s(r,"fill-rule","nonzero"),s(u,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(u,"id","Combined-Shape"),s(u,"fill-rule","nonzero"),s(d,"id","Rectangle"),s(d,"opacity","0.005"),s(d,"x","0"),s(d,"y","0"),s(d,"width","16"),s(d,"height","16"),s(c,"id","Pencil_Base"),s(c,"fill",h=n[0]?n[1]:"#FFFFFF"),q(c,"opacity",n[0]?"1":"0.4"),s(i,"id","nextedit_applied_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(f,g){A(f,t,g),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(i,c),C(c,u),C(c,d)},p(f,[g]){3&g&&a!==(a=f[0]?f[1]:"#30D158")&&s(r,"fill",a),3&g&&h!==(h=f[0]?f[1]:"#FFFFFF")&&s(c,"fill",h),1&g&&q(c,"opacity",f[0]?"1":"0.4")},i:j,o:j,d(f){f&&S(t)}}}function jt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Et extends N{constructor(t){super(),B(this,t,jt,Mt,I,{mask:0,maskColor:1})}}function Pt(n){let t,e,o,i,r,l,a,c,u,d,h,f;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_rejected_light"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("rect"),h=m("rect"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#0A84FF"),s(d,"id","Rectangle"),s(d,"x","2.25"),s(d,"y","0"),s(d,"width","1.5"),s(d,"height","6"),s(d,"rx","0.75"),s(h,"id","Rectangle-Copy"),s(h,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),s(h,"x","2.25"),s(h,"y","1.13686838e-13"),s(h,"width","1.5"),s(h,"height","6"),s(h,"rx","0.75"),s(u,"id","Group"),s(u,"transform","translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"),s(u,"fill",f=n[0]?n[1]:"#FF5D4E"),s(i,"id","nextedit_rejected_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,p){A(g,t,p),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d),C(u,h)},p(g,[p]){3&p&&c!==(c=g[0]?g[1]:"#0A84FF")&&s(r,"fill",c),3&p&&f!==(f=g[0]?g[1]:"#FF5D4E")&&s(u,"fill",f)},i:j,o:j,d(g){g&&S(t)}}}function Ot(n,t,e){let{mask:o=!0}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Rt extends N{constructor(t){super(),B(this,t,Ot,Pt,I,{mask:0,maskColor:1})}}function zt(n){let t,e,o,i,r,l,a,c,u,d,h,f;return{c(){t=m("svg"),e=m("title"),o=G("nextedit_rejected_dark"),i=m("g"),r=m("g"),l=m("path"),a=m("rect"),u=m("g"),d=m("rect"),h=m("rect"),s(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),s(l,"id","Combined-Shape"),s(l,"fill-rule","nonzero"),s(a,"id","Rectangle"),s(a,"opacity","0.005"),s(a,"x","0"),s(a,"y","0"),s(a,"width","16"),s(a,"height","16"),s(a,"rx","2"),s(r,"id","Pencil_Base"),s(r,"fill",c=n[0]?n[1]:"#168AFF"),s(d,"id","Rectangle"),s(d,"x","2.25"),s(d,"y","0"),s(d,"width","1.5"),s(d,"height","6"),s(d,"rx","0.75"),s(h,"id","Rectangle-Copy"),s(h,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),s(h,"x","2.25"),s(h,"y","1.13686838e-13"),s(h,"width","1.5"),s(h,"height","6"),s(h,"rx","0.75"),s(u,"id","Group"),s(u,"transform","translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"),s(u,"fill",f=n[0]?n[1]:"#FF7E72"),s(i,"id","nextedit_rejected_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,p){A(g,t,p),C(t,e),C(e,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,u),C(u,d),C(u,h)},p(g,[p]){3&p&&c!==(c=g[0]?g[1]:"#168AFF")&&s(r,"fill",c),3&p&&f!==(f=g[0]?g[1]:"#FF7E72")&&s(u,"fill",f)},i:j,o:j,d(g){g&&S(t)}}}function Nt(n,t,e){let{mask:o=!0}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Bt extends N{constructor(t){super(),B(this,t,Nt,zt,I,{mask:0,maskColor:1})}}function It(n){let t,e,o,i,r,l,a,c,u,d,h;return{c(){t=m("svg"),e=m("title"),o=G("Option 2_light"),i=m("g"),r=m("path"),a=m("g"),c=m("path"),d=m("path"),s(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),s(r,"id","Path"),s(r,"fill",l=n[0]?n[1]:"#007AFF"),s(c,"d","M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z"),s(c,"id","Combined-Shape"),s(c,"stroke",u=n[0]?n[1]:"#007AFF"),s(c,"stroke-width","1.21"),s(d,"d","M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z"),s(d,"id","Combined-Shape"),s(d,"fill",h=n[0]?n[1]:"#007AFF"),s(a,"id","Pencil"),s(a,"transform","translate(0.172, 2.224)"),s(i,"id","Option-2_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(f,g){A(f,t,g),C(t,e),C(e,o),C(t,i),C(i,r),C(i,a),C(a,c),C(a,d)},p(f,[g]){3&g&&l!==(l=f[0]?f[1]:"#007AFF")&&s(r,"fill",l),3&g&&u!==(u=f[0]?f[1]:"#007AFF")&&s(c,"stroke",u),3&g&&h!==(h=f[0]?f[1]:"#007AFF")&&s(d,"fill",h)},i:j,o:j,d(f){f&&S(t)}}}function Zt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Dt extends N{constructor(t){super(),B(this,t,Zt,It,I,{mask:0,maskColor:1})}}function Kt(n){let t,e,o,i,r,l,a,c,u,d,h;return{c(){t=m("svg"),e=m("title"),o=G("Option 2_dark"),i=m("g"),r=m("path"),a=m("g"),c=m("path"),d=m("path"),s(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),s(r,"id","Path"),s(r,"fill",l=n[0]?n[1]:"#BF5AF2"),s(c,"d","M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z"),s(c,"id","Combined-Shape"),s(c,"stroke",u=n[0]?n[1]:"#389BFF"),s(c,"stroke-width","1.21"),s(d,"d","M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z"),s(d,"id","Combined-Shape"),s(d,"fill",h=n[0]?n[1]:"#389BFF"),s(a,"id","Pencil"),s(a,"transform","translate(0.172, 2.224)"),s(i,"id","Option-2_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(f,g){A(f,t,g),C(t,e),C(e,o),C(t,i),C(i,r),C(i,a),C(a,c),C(a,d)},p(f,[g]){3&g&&l!==(l=f[0]?f[1]:"#BF5AF2")&&s(r,"fill",l),3&g&&u!==(u=f[0]?f[1]:"#389BFF")&&s(c,"stroke",u),3&g&&h!==(h=f[0]?f[1]:"#389BFF")&&s(d,"fill",h)},i:j,o:j,d(f){f&&S(t)}}}function Tt(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Vt extends N{constructor(t){super(),B(this,t,Tt,Kt,I,{mask:0,maskColor:1})}}function qt(n){let t,e,o,i,r,l,a,c,u;return{c(){t=m("svg"),e=m("title"),o=G("Option 2_Inactive_light"),i=m("g"),r=m("path"),a=m("g"),c=m("path"),s(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),s(r,"id","Path"),s(r,"fill",l=n[0]?n[1]:"#BF5AF2"),s(c,"d","M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z"),s(c,"id","Combined-Shape"),s(a,"id","Pencil"),s(a,"transform","translate(0.172, 2.224)"),s(a,"stroke",u=n[0]?n[1]:"#007AFF"),s(a,"stroke-width","1.21"),s(i,"id","Option-2_Inactive_light"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(d,h){A(d,t,h),C(t,e),C(e,o),C(t,i),C(i,r),C(i,a),C(a,c)},p(d,[h]){3&h&&l!==(l=d[0]?d[1]:"#BF5AF2")&&s(r,"fill",l),3&h&&u!==(u=d[0]?d[1]:"#007AFF")&&s(a,"stroke",u)},i:j,o:j,d(d){d&&S(t)}}}function Ut(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Gt extends N{constructor(t){super(),B(this,t,Ut,qt,I,{mask:0,maskColor:1})}}function Wt(n){let t,e,o,i,r,l,a,c,u;return{c(){t=m("svg"),e=m("title"),o=G("Option 2_inactive_dark"),i=m("g"),r=m("path"),a=m("g"),c=m("path"),s(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),s(r,"id","Path"),s(r,"fill",l=n[0]?n[1]:"#BF5AF2"),s(c,"d","M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z"),s(c,"id","Combined-Shape"),s(a,"id","Pencil"),s(a,"transform","translate(0.172, 2.224)"),s(a,"stroke",u=n[0]?n[1]:"#389BFF"),s(a,"stroke-width","1.21"),s(i,"id","Option-2_inactive_dark"),s(i,"stroke","none"),s(i,"stroke-width","1"),s(i,"fill","none"),s(i,"fill-rule","evenodd"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"version","1.1"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(d,h){A(d,t,h),C(t,e),C(e,o),C(t,i),C(i,r),C(i,a),C(a,c)},p(d,[h]){3&h&&l!==(l=d[0]?d[1]:"#BF5AF2")&&s(r,"fill",l),3&h&&u!==(u=d[0]?d[1]:"#389BFF")&&s(a,"stroke",u)},i:j,o:j,d(d){d&&S(t)}}}function Ht(n,t,e){let{mask:o=!1}=t,{maskColor:i="white"}=t;return n.$$set=r=>{"mask"in r&&e(0,o=r.mask),"maskColor"in r&&e(1,i=r.maskColor)},[o,i]}class Jt extends N{constructor(t){super(),B(this,t,Ht,Wt,I,{mask:0,maskColor:1})}}function Qt(n){let t,e,o,i;var r=n[2];function l(u,d){return{props:{mask:u[0],maskColor:u[1]}}}r&&(e=G3(r,l(n)));const a=n[9].default,c=E1(a,n,n[8],null);return{c(){t=F("span"),e&&O(e.$$.fragment),o=U(),c&&c.c(),s(t,"class","c-pencil-icon svelte-6fsamc")},m(u,d){A(u,t,d),e&&R(e,t,null),C(t,o),c&&c.m(t,null),i=!0},p(u,[d]){if(4&d&&r!==(r=u[2])){if(e){l1();const h=e;_(h.$$.fragment,1,0,()=>{z(h,1)}),c1()}r?(e=G3(r,l(u)),O(e.$$.fragment),x(e.$$.fragment,1),R(e,t,o)):e=null}else if(r){const h={};1&d&&(h.mask=u[0]),2&d&&(h.maskColor=u[1]),e.$set(h)}c&&c.p&&(!i||256&d)&&P1(c,a,u,u[8],i?R1(a,u[8],d,null):O1(u[8]),null)},i(u){i||(e&&x(e.$$.fragment,u),x(c,u),i=!0)},o(u){e&&_(e.$$.fragment,u),_(c,u),i=!1},d(u){u&&S(t),e&&z(e),c&&c.d(u)}}}function Yt(n,t,e){let o,i,r;e1(n,Y2,p=>e(7,r=p));let{$$slots:l={},$$scope:a}=t;const c={insertion:{light:ht,dark:Ct},deletion:{light:wt,dark:Lt},modification:{light:u2,dark:d2},noop:{light:u2,dark:d2},active:{light:Dt,dark:Vt},inactive:{light:Gt,dark:Jt},accepted:{light:Ft,dark:Et},rejected:{light:Rt,dark:Bt}};let u,{mask:d=!1}=t,{maskColor:h="currentColor"}=t,{suggestion:f}=t,{themeCategory:g}=t;return n.$$set=p=>{"mask"in p&&e(0,d=p.mask),"maskColor"in p&&e(1,h=p.maskColor),"suggestion"in p&&e(4,f=p.suggestion),"themeCategory"in p&&e(3,g=p.themeCategory),"$$scope"in p&&e(8,a=p.$$scope)},n.$$.update=()=>{136&n.$$.dirty&&(r!=null&&r.category)&&e(3,g??(g=r.category)),16&n.$$.dirty&&e(5,u=function(p){return new s3(p,x3)}(f).with({state:z1.stale},{state:z1.accepted},()=>"accepted").otherwise(({changeType:p})=>p)),32&n.$$.dirty&&e(6,o=c[u]??c.active),72&n.$$.dirty&&e(2,i=o[g??A0.light])},[d,h,i,g,f,u,o,r,a,l]}class c0 extends N{constructor(t){super(),B(this,t,Yt,Qt,I,{mask:0,maskColor:1,suggestion:4,themeCategory:3})}}function Xt(n){let t,e,o,i,r,l,a,c,u,d,h,f,g,p,w,$=n[0].lineRange.start+1+"",b=n[0].result.changeDescription+"";return e=new c0({props:{mask:n[2]!=="none",suggestion:n[0]}}),{c(){t=F("button"),O(e.$$.fragment),o=U(),i=F("span"),r=F("span"),l=G($),a=G(":"),c=U(),u=F("span"),d=G(b),s(r,"class","c-suggestion-tree-item__description__linenumber"),s(u,"class","c-suggestion-tree-item__description__path svelte-hekzdv"),s(i,"class","c-suggestion-tree-item-button__description svelte-hekzdv"),s(t,"tabindex",0),s(t,"title",h=H3(n[0],!0)+":"+J3(n[0])),s(t,"class",f="c-suggestion-tree-item-button "+n[2]+" svelte-hekzdv")},m(y,v){A(y,t,v),R(e,t,null),C(t,o),C(t,i),C(i,r),C(r,l),C(r,a),C(i,c),C(i,u),C(u,d),g=!0,p||(w=[W(t,"click",n[5]),W(t,"dblclick",n[4]),W(t,"keydown",function(){d1(b1("Space",n[7]))&&b1("Space",n[7]).apply(this,arguments)})],p=!0)},p(y,[v]){n=y;const K={};4&v&&(K.mask=n[2]!=="none"),1&v&&(K.suggestion=n[0]),e.$set(K),(!g||1&v)&&$!==($=n[0].lineRange.start+1+"")&&y3(l,$),(!g||1&v)&&b!==(b=n[0].result.changeDescription+"")&&y3(d,b),(!g||1&v&&h!==(h=H3(n[0],!0)+":"+J3(n[0])))&&s(t,"title",h),(!g||4&v&&f!==(f="c-suggestion-tree-item-button "+n[2]+" svelte-hekzdv"))&&s(t,"class",f)},i(y){g||(x(e.$$.fragment,y),g=!0)},o(y){_(e.$$.fragment,y),g=!1},d(y){y&&S(t),z(e),p=!1,U1(w)}}}function t4(n,t,e){let o,{suggestion:i}=t,{onCodeAction:r}=t;const l=B1();e1(n,l,c=>e(6,o=c));let a=w1(i,o,!0);return n.$$set=c=>{"suggestion"in c&&e(0,i=c.suggestion),"onCodeAction"in c&&e(1,r=c.onCodeAction)},n.$$.update=()=>{65&n.$$.dirty&&e(2,a=w1(i,o,!0))},[i,r,a,l,function(){X(i,o.activeSuggestion)?r("dismiss"):r("active",i)},function(){r("select",i)},o,()=>r("select",i)]}class n4 extends N{constructor(t){super(),B(this,t,t4,Xt,I,{suggestion:0,onCodeAction:1})}}function g2(n){let t,e;return t=new R0({props:{onCodeAction:n[2],codeActions:n[6],value:n[1]}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,i){const r={};4&i&&(r.onCodeAction=o[2]),64&i&&(r.codeActions=o[6]),2&i&&(r.value=o[1]),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function e4(n){let t,e,o,i,r,l,a,c,u;e=new n4({props:{suggestion:n[1],onCodeAction:n[2]}});let d=n[5]&&g2(n);return{c(){t=F("li"),O(e.$$.fragment),o=U(),d&&d.c(),s(t,"class","c-suggestion-tree-item__suggestion svelte-3abz9e"),s(t,"style",i=i3(n[0]))},m(h,f){A(h,t,f),R(e,t,null),C(t,o),d&&d.m(t,null),a=!0,c||(u=[j1(r=N3.call(null,t,{scrollContainer:n[4],doScroll:n[3],scrollIntoView:{behavior:"smooth",block:"nearest"}})),W(t,"mouseenter",n[9]),W(t,"mouseleave",n[10])],c=!0)},p(h,[f]){const g={};2&f&&(g.suggestion=h[1]),4&f&&(g.onCodeAction=h[2]),e.$set(g),h[5]?d?(d.p(h,f),32&f&&x(d,1)):(d=g2(h),d.c(),x(d,1),d.m(t,null)):d&&(l1(),_(d,1,1,()=>{d=null}),c1()),(!a||1&f&&i!==(i=i3(h[0])))&&s(t,"style",i),r&&d1(r.update)&&24&f&&r.update.call(null,{scrollContainer:h[4],doScroll:h[3],scrollIntoView:{behavior:"smooth",block:"nearest"}})},i(h){a||(x(e.$$.fragment,h),x(d),h&&X2(()=>{a&&(l||(l=e3(t,o3,{},!0)),l.run(1))}),a=!0)},o(h){_(e.$$.fragment,h),_(d),h&&(l||(l=e3(t,o3,{},!1)),l.run(0)),a=!1},d(h){h&&S(t),z(e),d&&d.d(),h&&l&&l.end(),c=!1,U1(u)}}}function o4(n,t,e){let o,i,{suggestion:r}=t,{onCodeAction:l}=t,{shouldScrollIntoView:a=!1}=t,{scrollContainer:c}=t,u=!1,d=B1();e1(n,d,f=>e(8,i=f));let{state:h=w1(r,i,!0)}=t;return n.$$set=f=>{"suggestion"in f&&e(1,r=f.suggestion),"onCodeAction"in f&&e(2,l=f.onCodeAction),"shouldScrollIntoView"in f&&e(3,a=f.shouldScrollIntoView),"scrollContainer"in f&&e(4,c=f.scrollContainer),"state"in f&&e(0,h=f.state)},n.$$.update=()=>{258&n.$$.dirty&&e(0,h=w1(r,i,!0)),2&n.$$.dirty&&e(6,o=r.state===z1.accepted?v1("reject","undo"):v1("reject","accept"))},[h,r,l,a,c,u,o,d,i,()=>e(5,u=!0),()=>e(5,u=!1)]}class i4 extends N{constructor(t){super(),B(this,t,o4,e4,I,{suggestion:1,onCodeAction:2,shouldScrollIntoView:3,scrollContainer:4,state:0})}}const{Map:u0}=z3;function h2(n,t,e){const o=n.slice();return o[8]=t[e][0],o[9]=t[e][1],o}function f2(n,t,e){const o=n.slice();return o[12]=t[e],o}function p2(n,t){let e,o,i;return o=new i4({props:{onCodeAction:t[0],suggestion:t[12],shouldScrollIntoView:X(t[12],g1(t[4])),scrollContainer:t[3]}}),{key:n,first:null,c(){e=a3(),O(o.$$.fragment),this.first=e},m(r,l){A(r,e,l),R(o,r,l),i=!0},p(r,l){t=r;const a={};1&l&&(a.onCodeAction=t[0]),2&l&&(a.suggestion=t[12]),18&l&&(a.shouldScrollIntoView=X(t[12],g1(t[4]))),8&l&&(a.scrollContainer=t[3]),o.$set(a)},i(r){i||(x(o.$$.fragment,r),i=!0)},o(r){_(o.$$.fragment,r),i=!1},d(r){r&&S(e),z(o,r)}}}function r4(n){let t,e,o=[],i=new u0,r=a1(n[9]);const l=a=>a[12].result.suggestionId;for(let a=0;a<r.length;a+=1){let c=f2(n,r,a),u=l(c);i.set(u,o[a]=p2(u,c))}return{c(){t=F("ul");for(let a=0;a<o.length;a+=1)o[a].c();s(t,"class","c-suggestion-tree-item__inner svelte-bnynfs")},m(a,c){A(a,t,c);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(t,null);e=!0},p(a,c){27&c&&(r=a1(a[9]),l1(),o=R3(o,c,l,1,a,r,i,t,n0,p2,null,f2),c1())},i(a){if(!e){for(let c=0;c<r.length;c+=1)x(o[c]);e=!0}},o(a){for(let c=0;c<o.length;c+=1)_(o[c]);e=!1},d(a){a&&S(t);for(let c=0;c<o.length;c+=1)o[c].d()}}}function s4(n){let t,e;return t=new e0({props:{filepath:n[8],slot:"summary",class:"c-suggestion-tree-item__lang-summary",value:n[9],onCodeAction:n[0],codeActions:n[9].every(m2)?v1("rejectAllInFile"):v1("rejectAllInFile","acceptAllInFile")}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,i){const r={};2&i&&(r.filepath=o[8]),2&i&&(r.value=o[9]),1&i&&(r.onCodeAction=o[0]),2&i&&(r.codeActions=o[9].every(m2)?v1("rejectAllInFile"):v1("rejectAllInFile","acceptAllInFile")),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function C2(n,t){var c;let e,o,i,r,l;function a(...u){return t[6](t[8],...u)}return o=new l0({props:{duration:Q3(t[9].length),class:"c-suggestion-tree-item",open:((c=t[4].isOpen)==null?void 0:c[t[8]])??!0,onChangeOpen:a,$$slots:{summary:[s4],default:[r4]},$$scope:{ctx:t}}}),{key:n,first:null,c(){e=F("li"),O(o.$$.fragment),i=U(),s(e,"class","svelte-bnynfs"),this.first=e},m(u,d){A(u,e,d),R(o,e,null),C(e,i),l=!0},p(u,d){var f;t=u;const h={};2&d&&(h.duration=Q3(t[9].length)),18&d&&(h.open=((f=t[4].isOpen)==null?void 0:f[t[8]])??!0),18&d&&(h.onChangeOpen=a),32795&d&&(h.$$scope={dirty:d,ctx:t}),o.$set(h)},i(u){l||(x(o.$$.fragment,u),u&&X2(()=>{l&&(r||(r=e3(e,o3,{},!0)),r.run(1))}),l=!0)},o(u){_(o.$$.fragment,u),u&&(r||(r=e3(e,o3,{},!1)),r.run(0)),l=!1},d(u){u&&S(e),z(o),u&&r&&r.end()}}}function a4(n){let t,e,o=[],i=new u0,r=a1(n[1]);const l=a=>a[8];for(let a=0;a<r.length;a+=1){let c=h2(n,r,a),u=l(c);i.set(u,o[a]=C2(u,c))}return{c(){t=F("ul");for(let a=0;a<o.length;a+=1)o[a].c();s(t,"class","c-suggestion-tree__maximized svelte-bnynfs"),H(t,"hidden",!n[2])},m(a,c){A(a,t,c);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(t,null);n[7](t),e=!0},p(a,[c]){27&c&&(r=a1(a[1]),l1(),o=R3(o,c,l,1,a,r,i,t,n0,C2,null,h2),c1()),(!e||4&c)&&H(t,"hidden",!a[2])},i(a){if(!e){for(let c=0;c<r.length;c+=1)x(o[c]);e=!0}},o(a){for(let c=0;c<o.length;c+=1)_(o[c]);e=!1},d(a){a&&S(t);for(let c=0;c<o.length;c+=1)o[c].d();n[7](null)}}}const m2=n=>n.state==="accepted";function l4(n,t,e){let o,i,{onCodeAction:r}=t,{sortedPathSuggestionsMap:l=new Map}=t,{show:a=!0}=t;const c=B1();return e1(n,c,u=>e(4,o=u)),n.$$set=u=>{"onCodeAction"in u&&e(0,r=u.onCodeAction),"sortedPathSuggestionsMap"in u&&e(1,l=u.sortedPathSuggestionsMap),"show"in u&&e(2,a=u.show)},[r,l,a,i,o,c,(u,d)=>J(c,o.isOpen={...o.isOpen,[u]:d},o),function(u){M1[u?"unshift":"push"](()=>{i=u,e(3,i)})}]}class c4 extends N{constructor(t){super(),B(this,t,l4,a4,I,{onCodeAction:0,sortedPathSuggestionsMap:1,show:2})}}const u4=n=>({}),v2=n=>({});function d4(n){let t,e,o,i,r,l,a,c,u;e=new X0({props:{sortedPathSuggestionsMap:n[1],onCodeAction:n[0],show:n[2]&&!!n[1].size}}),i=new c4({props:{sortedPathSuggestionsMap:n[1],onCodeAction:n[0],show:!n[2]&&!!n[1].size}});const d=n[8]["no-suggestions"],h=E1(d,n,n[7],v2);return{c(){t=U(),O(e.$$.fragment),o=U(),O(i.$$.fragment),r=U(),l=F("div"),h&&h.c(),s(l,"class","c-suggestion-tree__no-suggestions svelte-l320gs"),H(l,"hidden",n[1].size)},m(f,g){A(f,t,g),R(e,f,g),A(f,o,g),R(i,f,g),A(f,r,g),A(f,l,g),h&&h.m(l,null),a=!0,c||(u=W(document.body,"keydown",n[4]),c=!0)},p(f,[g]){const p={};2&g&&(p.sortedPathSuggestionsMap=f[1]),1&g&&(p.onCodeAction=f[0]),6&g&&(p.show=f[2]&&!!f[1].size),e.$set(p);const w={};2&g&&(w.sortedPathSuggestionsMap=f[1]),1&g&&(w.onCodeAction=f[0]),6&g&&(w.show=!f[2]&&!!f[1].size),i.$set(w),h&&h.p&&(!a||128&g)&&P1(h,d,f,f[7],a?R1(d,f[7],g,u4):O1(f[7]),v2),(!a||2&g)&&H(l,"hidden",f[1].size)},i(f){a||(x(e.$$.fragment,f),x(i.$$.fragment,f),x(h,f),a=!0)},o(f){_(e.$$.fragment,f),_(i.$$.fragment,f),_(h,f),a=!1},d(f){f&&(S(t),S(o),S(r),S(l)),z(e,f),z(i,f),h&&h.d(f),c=!1,u()}}}function g4(n,t,e){let o,i,{$$slots:r={},$$scope:l}=t,{onCodeAction:a}=t,{sortedPathSuggestionsMap:c=new Map}=t,{minimized:u=!1}=t;const d=B1();function h(g,p=!0){g!=null&&g.qualifiedPathName.relPath&&(J(d,o.nextSuggestion=g,o),J(d,o.isOpen={...o.isOpen,[g.qualifiedPathName.relPath]:p},o))}function f(g,p){g.preventDefault(),g.stopPropagation();const w=g1(o),$=z0(c,w),b=o0(c,$+(p?-1:1));b&&b!==w&&(h(b),Y1(o)==="active"?a("active",b):a("select",b))}return e1(n,d,g=>e(6,o=g)),n.$$set=g=>{"onCodeAction"in g&&e(0,a=g.onCodeAction),"sortedPathSuggestionsMap"in g&&e(1,c=g.sortedPathSuggestionsMap),"minimized"in g&&e(2,u=g.minimized),"$$scope"in g&&e(7,l=g.$$scope)},n.$$.update=()=>{96&n.$$.dirty&&o.nextSuggestion&&Y1(o)==="next"&&!X(o.nextSuggestion,i)&&(e(5,i=o.nextSuggestion),h(o.nextSuggestion))},[a,c,u,d,function(g){switch(g.code){case"KeyZ":if(!g.metaKey&&!g.ctrlKey||(g.preventDefault(),g.stopPropagation(),!o.nextSuggestion))return;h(o.nextSuggestion),a("undo",o.nextSuggestion);break;case"KeyK":case"ArrowUp":if(g.metaKey||g.altKey||g.ctrlKey||g.shiftKey)return;f(g,!0);break;case"KeyJ":case"ArrowDown":if(g.metaKey||g.altKey||g.ctrlKey||g.shiftKey)return;f(g);break;case"ArrowRight":case"Space":{if(g.metaKey||g.altKey||g.ctrlKey||g.shiftKey)return;g.preventDefault(),g.stopPropagation();const p=g1(o);h(p),a(Y1(o)==="select"?"active":"select",p);break}case"ArrowLeft":case"Escape":if(g.metaKey||g.altKey||g.ctrlKey||g.shiftKey)return;g.preventDefault(),g.stopPropagation(),a("dismiss");break;case"Enter":if(g.metaKey||g.altKey||g.ctrlKey||g.shiftKey||!o.nextSuggestion)return;g.preventDefault(),g.stopPropagation(),a("accept",o.nextSuggestion);break;case"Backspace":if(g.metaKey||g.altKey||g.ctrlKey||g.shiftKey||!o.nextSuggestion)return;g.preventDefault(),g.stopPropagation(),a("reject",o.nextSuggestion)}},i,o,l,r]}class h4 extends N{constructor(t){super(),B(this,t,g4,d4,I,{onCodeAction:0,sortedPathSuggestionsMap:1,minimized:2})}}function f4(n){let t,e,o,i,r;return e=new e0({props:{filepath:n[0].relPath,onCodeAction:N1,value:n[3]}}),i=new i0({props:{actions:n[2],onAction:n[1],value:n[0]}}),{c(){t=F("div"),O(e.$$.fragment),o=U(),O(i.$$.fragment),s(t,"class","c-code-roll-item-header svelte-3zqetr")},m(l,a){A(l,t,a),R(e,t,null),C(t,o),R(i,t,null),r=!0},p(l,[a]){const c={};1&a&&(c.filepath=l[0].relPath),8&a&&(c.value=l[3]),e.$set(c);const u={};4&a&&(u.actions=l[2]),2&a&&(u.onAction=l[1]),1&a&&(u.value=l[0]),i.$set(u)},i(l){r||(x(e.$$.fragment,l),x(i.$$.fragment,l),r=!0)},o(l){_(e.$$.fragment,l),_(i.$$.fragment,l),r=!1},d(l){l&&S(t),z(e),z(i)}}}function p4(n,t,e){let{filepath:o}=t,{onFileAction:i=N1}=t,{fileActions:r=[]}=t,{suggestions:l}=t;return n.$$set=a=>{"filepath"in a&&e(0,o=a.filepath),"onFileAction"in a&&e(1,i=a.onFileAction),"fileActions"in a&&e(2,r=a.fileActions),"suggestions"in a&&e(3,l=a.suggestions)},[o,i,r,l]}class C4 extends N{constructor(t){super(),B(this,t,p4,f4,I,{filepath:0,onFileAction:1,fileActions:2,suggestions:3})}}function w2(n,t,e,o){const i=n.split(""),r=t.toSorted(({lineRange:{start:l}},{lineRange:{start:a}})=>a-l);for(const l of r){const a=[l.result.charStart,e(l)],c=o(l);c&&a.push(...c.split("")),i.splice(...a)}return i.join("")}const $2=((n=0)=>()=>Date.now()+"-"+n++)(),m4=(n,t,e,o,i,r)=>{var u,d,h,f;if(!n||!e)return[];e=function(g,p){return w2(g,p,w=>w.result.suggestedCode.split("").length,w=>w.result.existingCode)}(e,t.filter(g=>g.state===z1.accepted));const l=function(g,p){return w2(g,p,w=>w.result.charEnd-w.result.charStart,w=>w.result.suggestedCode)}(e,t);(d=(u=n.getModel())==null?void 0:u.original)==null||d.dispose(),(f=(h=n.getModel())==null?void 0:h.modified)==null||f.dispose();const a=r.editor.createModel(e,o,r.Uri.parse("file://"+i+`#${$2()}`)),c=r.editor.createModel(l,o,r.Uri.parse("file://"+i+`#${$2()}`));return n.setModel({original:a,modified:c}),[a,c]};function v4(n){var t;return`${n.requestId}#${(t=n.result)==null?void 0:t.suggestionId}`}function y2(n){return n.map(v4).join(":")}function L2(n){const t=n.toSorted((i,r)=>i.start-r.start),e=[];let o=t.shift();for(;t.length;){const i=t.shift();i.start<=o.end+1?o.end=Math.max(o.end,i.end):(e.push(o),o=i)}return e.push(o),e}function x2(n){return n.reduce((t,e)=>t+=e.end-e.start+1,0)}function k2(n){return n.reduce((t,e,o)=>o===0?t:t+=e.start-n[o-1].end-1,0)}function k3(n){var o;let t,e;if(n.modifiedEndLineNumber===0)e=n.originalEndLineNumber-n.originalStartLineNumber+1,t=n.originalStartLineNumber-1;else if((o=n.charChanges)!=null&&o.length){const i=L2(n.charChanges.map(d=>({start:d.originalStartLineNumber,end:d.originalEndLineNumber}))),r=L2(n.charChanges.map(d=>({start:d.modifiedStartLineNumber,end:d.modifiedEndLineNumber}))),l=x2(i),a=x2(r),c=k2(i),u=k2(r);e=l+a+Math.max(c,u),t=n.modifiedStartLineNumber-1}else{if(n.originalEndLineNumber!==0)throw new Error("Unexpected line change");e=n.modifiedEndLineNumber-n.modifiedStartLineNumber+1,t=n.modifiedStartLineNumber-1}return{lineCount:e,afterLineNumber:t}}function w4(...n){return n.reduce((t,e)=>({...$4(t,e),charChanges:[...t.charChanges??[],...e.charChanges??[]]}))}function $4(...n){return n.reduce((t,e)=>({originalStartLineNumber:Math.min(t.originalStartLineNumber,e.originalStartLineNumber),originalEndLineNumber:Math.max(t.originalEndLineNumber,e.originalEndLineNumber),modifiedStartLineNumber:Math.min(t.modifiedStartLineNumber,e.modifiedStartLineNumber),modifiedEndLineNumber:Math.max(t.modifiedEndLineNumber,e.modifiedEndLineNumber)}))}function y4(n,t){if(t.originalStartLineNumber===n.lineRange.start+1)return!0;const e=Math.min(t.originalStartLineNumber,t.modifiedStartLineNumber),o=Math.max(t.originalEndLineNumber,t.modifiedEndLineNumber);return e>=n.lineRange.start&&e<=n.lineRange.stop||o>=n.lineRange.start&&o<=n.lineRange.stop||e<=n.lineRange.start&&o>=n.lineRange.stop}function b3(n,t){const e=new Map,o=n.toSorted(({lineRange:{start:i}},{lineRange:{start:r}})=>i-r);t:for(const i of t.toSorted(({modifiedStartLineNumber:r,originalStartLineNumber:l},{modifiedStartLineNumber:a,originalStartLineNumber:c})=>r-a||l-c))for(const r of o)if(y4(r,i)){const l=e.get(r);e.set(r,l?w4(l,i):i);continue t}return e}function L4(n,t){let e,o,i=t;const r=()=>i.editor.getModifiedEditor(),l=()=>{const{afterLineNumber:a}=i,c=r();if(a===void 0)return void c.changeViewZones(d=>{e&&c&&o&&d.removeZone(o)});const u={...i,afterLineNumber:a,domNode:n,suppressMouseDown:!0};c==null||c.changeViewZones(d=>{e&&o&&d.removeZone(o),o=d.addZone(u),e=u})};return l(),{update:a=>{i=a,l()},destroy:()=>{const a=r();a.changeViewZones(c=>{if(e&&a&&o)try{c.removeZone(o)}catch(u){if(u instanceof Error){if(u.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${u}`)}})}}}function x4(n){let t,e,o,i,r,l,a,c,u,d,h,f,g,p,w,$,b,y,v,K,t1,E=n[1].result.changeDescription+"";return c=new c0({props:{mask:n[11]!=="none",suggestion:n[1]}}),f=new i0({props:{compact:!0,actions:n[2],onAction:n[3],value:n[1]}}),{c(){t=F("div"),o=U(),i=F("div"),r=F("div"),l=F("div"),a=F("button"),O(c.$$.fragment),u=U(),d=G(E),h=U(),O(f.$$.fragment),g=U(),p=F("div"),w=F("div"),s(t,"class","c-code-roll-suggestion-window__view-zone"),q(t,"--augment-code-roll-left-alignment",n[9]+"px"),s(a,"class","c-code-roll-suggestion-window__item-title-text svelte-1ci0na9"),s(a,"tabindex","0"),s(l,"class","c-code-roll-suggestion-window__item-title svelte-1ci0na9"),q(l,"height",n[6]+"px"),s(w,"class","c-code-roll-suggestion-window__window svelte-1ci0na9"),q(w,"height",n[10]+"px"),s(p,"class","c-code-roll-suggestion-window__border svelte-1ci0na9"),s(r,"class","c-code-roll-suggestion-window__item svelte-1ci0na9"),s(r,"style",$=i3(w1(n[1],n[7],!1))),q(i,"--augment-code-roll-left-alignment",n[9]+"px"),q(i,"--augment-code-roll-suggestion-window-line-height",n[6]+"px"),q(i,"top",n[8]+"px"),s(i,"class","c-code-roll-suggestion-window svelte-1ci0na9"),s(i,"data-result-id",y=`${n[1].result.suggestionId}:${n[1].requestId}`)},m(L,M){A(L,t,M),A(L,o,M),A(L,i,M),C(i,r),C(r,l),C(l,a),R(c,a,null),C(a,u),C(a,d),C(l,h),R(f,l,null),C(r,g),C(r,p),C(p,w),v=!0,K||(t1=[j1(e=L4.call(null,t,{editor:n[0],afterLineNumber:n[4],heightInPx:n[6],onDomNodeTop:n[16]})),W(a,"keydown",function(){d1(b1("Enter",T1(n[3],"active",n[1])))&&b1("Enter",T1(n[3],"active",n[1])).apply(this,arguments)}),W(a,"click",function(){d1(T1(n[3],"active",n[1]))&&T1(n[3],"active",n[1]).apply(this,arguments)}),j1(b=N3.call(null,r,{scrollContainer:n[5],doScroll:X(n[1],g1(n[7])),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0}))],K=!0)},p(L,[M]){n=L,(!v||512&M)&&q(t,"--augment-code-roll-left-alignment",n[9]+"px"),e&&d1(e.update)&&337&M&&e.update.call(null,{editor:n[0],afterLineNumber:n[4],heightInPx:n[6],onDomNodeTop:n[16]});const T={};2048&M&&(T.mask=n[11]!=="none"),2&M&&(T.suggestion=n[1]),c.$set(T),(!v||2&M)&&E!==(E=n[1].result.changeDescription+"")&&y3(d,E);const L1={};4&M&&(L1.actions=n[2]),8&M&&(L1.onAction=n[3]),2&M&&(L1.value=n[1]),f.$set(L1),(!v||64&M)&&q(l,"height",n[6]+"px"),(!v||1024&M)&&q(w,"height",n[10]+"px"),(!v||130&M&&$!==($=i3(w1(n[1],n[7],!1))))&&s(r,"style",$),b&&d1(b.update)&&162&M&&b.update.call(null,{scrollContainer:n[5],doScroll:X(n[1],g1(n[7])),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0}),(!v||512&M)&&q(i,"--augment-code-roll-left-alignment",n[9]+"px"),(!v||64&M)&&q(i,"--augment-code-roll-suggestion-window-line-height",n[6]+"px"),(!v||256&M)&&q(i,"top",n[8]+"px"),(!v||2&M&&y!==(y=`${n[1].result.suggestionId}:${n[1].requestId}`))&&s(i,"data-result-id",y)},i(L){v||(x(c.$$.fragment,L),x(f.$$.fragment,L),v=!0)},o(L){_(c.$$.fragment,L),_(f.$$.fragment,L),v=!1},d(L){L&&(S(t),S(o),S(i)),z(c),z(f),K=!1,U1(t1)}}}function k4(n,t,e){let o,i,r,l,a,c,u,{diffEditor:d}=t,{suggestion:h}=t,{codeActions:f=[]}=t,{onCodeAction:g}=t,{afterLineNumber:p}=t,{lineCount:w=0}=t,{scrollContainer:$}=t,b=0;const y=B1();return e1(n,y,v=>e(7,u=v)),n.$$set=v=>{"diffEditor"in v&&e(0,d=v.diffEditor),"suggestion"in v&&e(1,h=v.suggestion),"codeActions"in v&&e(2,f=v.codeActions),"onCodeAction"in v&&e(3,g=v.onCodeAction),"afterLineNumber"in v&&e(4,p=v.afterLineNumber),"lineCount"in v&&e(13,w=v.lineCount),"scrollContainer"in v&&e(5,$=v.scrollContainer)},n.$$.update=()=>{130&n.$$.dirty&&e(11,o=w1(h,u,!1)),1&n.$$.dirty&&e(6,i=d.getModifiedEditor().getOption(q0.EditorOption.lineHeight)),8256&n.$$.dirty&&e(10,r=w*i),2&n.$$.dirty&&e(15,l=h.changeType===W3.insertion||h.changeType===W3.modification),40976&n.$$.dirty&&e(14,a=l?p+w:p),16384&n.$$.dirty&&e(9,c=a>999?0:5)},[d,h,f,g,p,$,i,u,b,c,r,o,y,w,a,l,function(v){e(8,b=v)}]}class b4 extends N{constructor(t){super(),B(this,t,k4,x4,I,{diffEditor:0,suggestion:1,codeActions:2,onCodeAction:3,afterLineNumber:4,lineCount:13,scrollContainer:5})}}function b2(n,t,e){const o=n.slice();return o[29]=t[e][0],o[30]=t[e][1],o}function _4(n){let t,e,o=a1(b3(n[2],n[8])),i=[];for(let l=0;l<o.length;l+=1)i[l]=_2(b2(n,o,l));const r=l=>_(i[l],1,1,()=>{i[l]=null});return{c(){for(let l=0;l<i.length;l+=1)i[l].c();t=a3()},m(l,a){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(l,a);A(l,t,a),e=!0},p(l,a){if(380&a[0]){let c;for(o=a1(b3(l[2],l[8])),c=0;c<o.length;c+=1){const u=b2(l,o,c);i[c]?(i[c].p(u,a),x(i[c],1)):(i[c]=_2(u),i[c].c(),x(i[c],1),i[c].m(t.parentNode,t))}for(l1(),c=o.length;c<i.length;c+=1)r(c);c1()}},i(l){if(!e){for(let a=0;a<o.length;a+=1)x(i[a]);e=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)_(i[a]);e=!1},d(l){l&&S(t),Q2(i,l)}}}function A4(n){let t,e,o;return e=new O3({}),{c(){t=F("div"),O(e.$$.fragment),s(t,"class","c-diff-view__loading")},m(i,r){A(i,t,r),R(e,t,null),o=!0},p:j,i(i){o||(x(e.$$.fragment,i),o=!0)},o(i){_(e.$$.fragment,i),o=!1},d(i){i&&S(t),z(e)}}}function _2(n){let t,e;const o=[{diffEditor:n[6]},{suggestion:n[29]},{codeActions:n[3]},{onCodeAction:n[4]},k3(n[30]),{scrollContainer:n[5]}];let i={};for(let r=0;r<o.length;r+=1)i=V1(i,o[r]);return t=new b4({props:i}),{c(){O(t.$$.fragment)},m(r,l){R(t,r,l),e=!0},p(r,l){const a=380&l[0]?P3(o,[64&l[0]&&{diffEditor:r[6]},260&l[0]&&{suggestion:r[29]},8&l[0]&&{codeActions:r[3]},16&l[0]&&{onCodeAction:r[4]},260&l[0]&&M0(k3(r[30])),32&l[0]&&{scrollContainer:r[5]}]):{};t.$set(a)},i(r){e||(x(t.$$.fragment,r),e=!0)},o(r){_(t.$$.fragment,r),e=!1},d(r){z(t,r)}}}function S4(n){let t,e,o,i,r,l,a,c,u;const d=[A4,_4],h=[];function f(g,p){return g[1]||!g[6]?0:1}return r=f(n),l=h[r]=d[r](n),{c(){t=F("div"),e=F("div"),i=U(),l.c(),s(e,"class","c-diff-view svelte-syu9kz"),q(e,"display",n[1]?"none":"flex"),s(t,"class","c-diff-view__container svelte-syu9kz"),q(t,"--augment-codeblock-min-height",n[0]+"px"),H(t,"has-top-decorations",n[9])},m(g,p){A(g,t,p),C(t,e),n[21](e),C(t,i),h[r].m(t,null),a=!0,c||(u=j1(o=K0.call(null,e,{onResize:n[20]})),c=!0)},p(g,p){o&&d1(o.update)&&64&p[0]&&o.update.call(null,{onResize:g[20]}),2&p[0]&&q(e,"display",g[1]?"none":"flex");let w=r;r=f(g),r===w?h[r].p(g,p):(l1(),_(h[w],1,1,()=>{h[w]=null}),c1(),l=h[r],l?l.p(g,p):(l=h[r]=d[r](g),l.c()),x(l,1),l.m(t,null)),(!a||1&p[0])&&q(t,"--augment-codeblock-min-height",g[0]+"px"),(!a||512&p[0])&&H(t,"has-top-decorations",g[9])},i(g){a||(x(l),a=!0)},o(g){_(l),a=!1},d(g){g&&S(t),n[21](null),h[r].d(),c=!1,u()}}}function F4(n,t,e){let o,i,r;e1(n,Y2,k=>e(19,r=k));let{height:l=500}=t,{language:a}=t,{originalCode:c}=t,{suggestions:u}=t,{codeActions:d=[]}=t,{onCodeAction:h=N1}=t,{path:f}=t,{busy:g=!0}=t,{expanded:p=!1}=t,{scrollContainer:w}=t,{options:$={enableSplitViewResizing:!1,automaticLayout:!0,readOnly:!0,overviewRulerLanes:0,lineHeight:20,renderLineHighlight:"none",contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,maxComputationTime:3e3,scrollBeyondLastColumn:0,scrollBeyondLastLine:!1,scrollPredominantAxis:!1,scrollbar:{alwaysConsumeMouseWheel:!1,vertical:"hidden",horizontal:"hidden"},cursorSurroundingLines:0,cursorSurroundingLinesStyle:"all",hideUnchangedRegions:{enabled:!p,revealLineCount:5,minimumLineCount:3,contextLineCount:5},lineNumbers:String,hover:{enabled:!1}}}=t;const b=G0.getContext().monaco;e1(n,b,k=>e(18,i=k));let y,v,K,t1=y2(u),E="",L=[],M=document.createElement("div");M.classList.add("c-diff-view__editor");let T=[],L1=!1;function Z3(k){const Q=function(D1,{enabled:G1=!0,revealLineCount:x1=5,minimumLineCount:y0=3,contextLineCount:f3=5}={enabled:!0,revealLineCount:5,minimumLineCount:3,contextLineCount:5},K3,W1){const L0={lines:0,decorations:0,hasTopDecorations:!1};if(!D1.length||!G1)return L0;let H1=0,T3=!1;const p3=[...D1].sort((K1,J1)=>K1.lineRange.start-J1.lineRange.start);let o1=1;for(let K1=0,J1=1;K1<p3.length;K1++,J1++){const u1=p3[K1],V3=p3[J1];if(o1+=Math.min(u1.lineRange.start+1,x1),o1+=W1!=null&&W1.get(u1)?k3(W1.get(u1)).lineCount:N0(u1),u1.lineRange.start-x1>1?(T3=!0,H1++):u1.lineRange.start-x1==1&&o1++,V3){const q3=V3.lineRange.start-u1.lineRange.start;q3>f3+x1?(H1++,o1+=f3,o1+=x1):o1+=q3}else u1.lineRange.stop<K3?(H1++,o1+=Math.min(K3-u1.lineRange.stop,x1)):(o1+=f3,o1+=x1)}return{lines:Math.max(o1,y0),decorations:H1,hasTopDecorations:T3}}(u,$.hideUnchangedRegions,B0(c),T.length>0?b3(u,T):void 0);e(0,l=p&&k?k.getModifiedEditor().getContentHeight():Q.lines*($.lineHeight??20)+24*Q.decorations),e(9,L1=Q.hasTopDecorations)}function D3(){var k,Q,D1,G1;(Q=(k=v==null?void 0:v.getModel())==null?void 0:k.original)==null||Q.dispose(),(G1=(D1=v==null?void 0:v.getModel())==null?void 0:D1.modified)==null||G1.dispose(),i&&(v||(e(6,v=i.editor.createDiffEditor(M,o)),L.push(v)),v.onDidDispose(()=>e(6,v=void 0)),L.push(...m4(v,u,c,a,f,i)),Z3(v),K==null||K.dispose(),K=v.onDidUpdateDiff(()=>{e(1,g=!1),e(8,T=(v==null?void 0:v.getLineChanges())??[]),Z3(v)}))}return t0(()=>{y.appendChild(M),D3()}),F0(()=>{L.forEach(k=>{var Q;return(Q=k==null?void 0:k.dispose)==null?void 0:Q.call(k)}),M.remove()}),n.$$set=k=>{"height"in k&&e(0,l=k.height),"language"in k&&e(11,a=k.language),"originalCode"in k&&e(12,c=k.originalCode),"suggestions"in k&&e(2,u=k.suggestions),"codeActions"in k&&e(3,d=k.codeActions),"onCodeAction"in k&&e(4,h=k.onCodeAction),"path"in k&&e(13,f=k.path),"busy"in k&&e(1,g=k.busy),"expanded"in k&&e(14,p=k.expanded),"scrollContainer"in k&&e(5,w=k.scrollContainer),"options"in k&&e(15,$=k.options)},n.$$.update=()=>{if(557056&n.$$.dirty[0]&&(o={...$,theme:n2(r==null?void 0:r.category,r==null?void 0:r.intensity)}),16448&n.$$.dirty[0]&&(v==null||v.updateOptions({hideUnchangedRegions:{enabled:!p,revealLineCount:5,minimumLineCount:3,contextLineCount:5}})),786496&n.$$.dirty[0]){const k=r,Q=n2(k==null?void 0:k.category,k==null?void 0:k.intensity);i==null||i.editor.setTheme(Q),v==null||v.getModifiedEditor().updateOptions({theme:Q}),v==null||v.getOriginalEditor().updateOptions({theme:Q}),v==null||v.layout()}if(200708&n.$$.dirty[0]){const k=y2(u);t1===k&&c===E||(e(16,t1=k),e(17,E=c),D3())}},[l,g,u,d,h,w,v,y,T,L1,b,a,c,f,p,$,t1,E,i,r,()=>v==null?void 0:v.layout(),function(k){M1[k?"unshift":"push"](()=>{y=k,e(7,y)})}]}class M4 extends N{constructor(t){super(),B(this,t,F4,S4,I,{height:0,language:11,originalCode:12,suggestions:2,codeActions:3,onCodeAction:4,path:13,busy:1,expanded:14,scrollContainer:5,options:15},null,[-1,-1])}}function j4(n){let t,e,o;return e=new O3({}),{c(){t=F("div"),O(e.$$.fragment),s(t,"class","c-code-roll-item__loading svelte-1i59d33")},m(i,r){A(i,t,r),R(e,t,null),o=!0},p:j,i(i){o||(x(e.$$.fragment,i),o=!0)},o(i){_(e.$$.fragment,i),o=!1},d(i){i&&S(t),z(e)}}}function E4(n){let t,e;return t=new M4({props:{onCodeAction:n[5],codeActions:n[6],suggestions:n[0],originalCode:n[2],language:n[4],path:n[1].relPath,scrollContainer:n[7]}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,i){const r={};32&i&&(r.onCodeAction=o[5]),64&i&&(r.codeActions=o[6]),1&i&&(r.suggestions=o[0]),4&i&&(r.originalCode=o[2]),16&i&&(r.language=o[4]),2&i&&(r.path=o[1].relPath),128&i&&(r.scrollContainer=o[7]),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function P4(n){let t,e,o,i;const r=[E4,j4],l=[];function a(c,u){return c[3]?0:1}return e=a(n),o=l[e]=r[e](n),{c(){t=F("div"),o.c(),s(t,"class","c-code-roll-item-diff svelte-1i59d33")},m(c,u){A(c,t,u),l[e].m(t,null),i=!0},p(c,[u]){let d=e;e=a(c),e===d?l[e].p(c,u):(l1(),_(l[d],1,1,()=>{l[d]=null}),c1(),o=l[e],o?o.p(c,u):(o=l[e]=r[e](c),o.c()),x(o,1),o.m(t,null))},i(c){i||(x(o),i=!0)},o(c){_(o),i=!1},d(c){c&&S(t),l[e].d()}}}function O4(n,t,e){let{suggestions:o}=t,{filepath:i}=t,{originalCode:r}=t,{loaded:l=!1}=t,{language:a=U0(i.relPath)}=t,{onCodeAction:c=N1}=t,{codeActions:u=[]}=t,{scrollContainer:d}=t;return n.$$set=h=>{"suggestions"in h&&e(0,o=h.suggestions),"filepath"in h&&e(1,i=h.filepath),"originalCode"in h&&e(2,r=h.originalCode),"loaded"in h&&e(3,l=h.loaded),"language"in h&&e(4,a=h.language),"onCodeAction"in h&&e(5,c=h.onCodeAction),"codeActions"in h&&e(6,u=h.codeActions),"scrollContainer"in h&&e(7,d=h.scrollContainer)},[o,i,r,l,a,c,u,d]}class R4 extends N{constructor(t){super(),B(this,t,O4,P4,I,{suggestions:0,filepath:1,originalCode:2,loaded:3,language:4,onCodeAction:5,codeActions:6,scrollContainer:7})}}var z4=function(){this.__data__=[],this.size=0},d0=function(n,t){return n===t||n!=n&&t!=t},N4=d0,u3=function(n,t){for(var e=n.length;e--;)if(N4(n[e][0],t))return e;return-1},B4=u3,I4=Array.prototype.splice,Z4=function(n){var t=this.__data__,e=B4(t,n);return!(e<0)&&(e==t.length-1?t.pop():I4.call(t,e,1),--this.size,!0)},D4=u3,K4=function(n){var t=this.__data__,e=D4(t,n);return e<0?void 0:t[e][1]},T4=u3,V4=function(n){return T4(this.__data__,n)>-1},q4=u3,U4=function(n,t){var e=this.__data__,o=q4(e,n);return o<0?(++this.size,e.push([n,t])):e[o][1]=t,this},G4=z4,W4=Z4,H4=K4,J4=V4,Q4=U4;function _1(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var o=n[t];this.set(o[0],o[1])}}_1.prototype.clear=G4,_1.prototype.delete=W4,_1.prototype.get=H4,_1.prototype.has=J4,_1.prototype.set=Q4;var d3=_1,Y4=d3,X4=function(){this.__data__=new Y4,this.size=0},t5=function(n){var t=this.__data__,e=t.delete(n);return this.size=t.size,e},n5=function(n){return this.__data__.get(n)},e5=function(n){return this.__data__.has(n)},o5=l3,i5=r0,A2,g0=function(n){if(!i5(n))return!1;var t=o5(n);return t=="[object Function]"||t=="[object GeneratorFunction]"||t=="[object AsyncFunction]"||t=="[object Proxy]"},v3=y1["__core-js_shared__"],S2=(A2=/[^.]+$/.exec(v3&&v3.keys&&v3.keys.IE_PROTO||""))?"Symbol(src)_1."+A2:"",r5=function(n){return!!S2&&S2 in n},s5=Function.prototype.toString,h0=function(n){if(n!=null){try{return s5.call(n)}catch{}try{return n+""}catch{}}return""},a5=g0,l5=r5,c5=r0,u5=h0,d5=/^\[object .+?Constructor\]$/,g5=Function.prototype,h5=Object.prototype,f5=g5.toString,p5=h5.hasOwnProperty,C5=RegExp("^"+f5.call(p5).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),m5=function(n,t){return n==null?void 0:n[t]},v5=function(n){return!(!c5(n)||l5(n))&&(a5(n)?C5:d5).test(u5(n))},w5=m5,I1=function(n,t){var e=w5(n,t);return v5(e)?e:void 0},B3=I1(y1,"Map"),g3=I1(Object,"create"),F2=g3,$5=function(){this.__data__=F2?F2(null):{},this.size=0},y5=function(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t},L5=g3,x5=Object.prototype.hasOwnProperty,k5=function(n){var t=this.__data__;if(L5){var e=t[n];return e==="__lodash_hash_undefined__"?void 0:e}return x5.call(t,n)?t[n]:void 0},b5=g3,_5=Object.prototype.hasOwnProperty,A5=function(n){var t=this.__data__;return b5?t[n]!==void 0:_5.call(t,n)},S5=g3,F5=function(n,t){var e=this.__data__;return this.size+=this.has(n)?0:1,e[n]=S5&&t===void 0?"__lodash_hash_undefined__":t,this},M5=$5,j5=y5,E5=k5,P5=A5,O5=F5;function A1(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var o=n[t];this.set(o[0],o[1])}}A1.prototype.clear=M5,A1.prototype.delete=j5,A1.prototype.get=E5,A1.prototype.has=P5,A1.prototype.set=O5;var M2=A1,R5=d3,z5=B3,N5=function(n){var t=typeof n;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?n!=="__proto__":n===null},h3=function(n,t){var e=n.__data__;return N5(t)?e[typeof t=="string"?"string":"hash"]:e.map},B5=h3,I5=function(n){var t=B5(this,n).delete(n);return this.size-=t?1:0,t},Z5=h3,D5=function(n){return Z5(this,n).get(n)},K5=h3,T5=function(n){return K5(this,n).has(n)},V5=h3,q5=function(n,t){var e=V5(this,n),o=e.size;return e.set(n,t),this.size+=e.size==o?0:1,this},U5=function(){this.size=0,this.__data__={hash:new M2,map:new(z5||R5),string:new M2}},G5=I5,W5=D5,H5=T5,J5=q5;function S1(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var o=n[t];this.set(o[0],o[1])}}S1.prototype.clear=U5,S1.prototype.delete=G5,S1.prototype.get=W5,S1.prototype.has=H5,S1.prototype.set=J5;var f0=S1,Q5=d3,Y5=B3,X5=f0,t9=function(n,t){var e=this.__data__;if(e instanceof Q5){var o=e.__data__;if(!Y5||o.length<199)return o.push([n,t]),this.size=++e.size,this;e=this.__data__=new X5(o)}return e.set(n,t),this.size=e.size,this},n9=d3,e9=X4,o9=t5,i9=n5,r9=e5,s9=t9;function F1(n){var t=this.__data__=new n9(n);this.size=t.size}F1.prototype.clear=e9,F1.prototype.delete=o9,F1.prototype.get=i9,F1.prototype.has=r9,F1.prototype.set=s9;var a9=F1,l9=f0,c9=function(n){return this.__data__.set(n,"__lodash_hash_undefined__"),this},u9=function(n){return this.__data__.has(n)};function t3(n){var t=-1,e=n==null?0:n.length;for(this.__data__=new l9;++t<e;)this.add(n[t])}t3.prototype.add=t3.prototype.push=c9,t3.prototype.has=u9;var d9=function(n,t){return n.has(t)},g9=t3,h9=function(n,t){for(var e=-1,o=n==null?0:n.length;++e<o;)if(t(n[e],e,n))return!0;return!1},f9=d9,p0=function(n,t,e,o,i,r){var l=1&e,a=n.length,c=t.length;if(a!=c&&!(l&&c>a))return!1;var u=r.get(n),d=r.get(t);if(u&&d)return u==t&&d==n;var h=-1,f=!0,g=2&e?new g9:void 0;for(r.set(n,t),r.set(t,n);++h<a;){var p=n[h],w=t[h];if(o)var $=l?o(w,p,h,t,n,r):o(p,w,h,n,t,r);if($!==void 0){if($)continue;f=!1;break}if(g){if(!h9(t,function(b,y){if(!f9(g,y)&&(p===b||i(p,b,e,o,r)))return g.push(y)})){f=!1;break}}else if(p!==w&&!i(p,w,e,o,r)){f=!1;break}}return r.delete(n),r.delete(t),f},p9=function(n){var t=-1,e=Array(n.size);return n.forEach(function(o,i){e[++t]=[i,o]}),e},j2=y1.Uint8Array,C9=d0,m9=p0,v9=p9,w9=function(n){var t=-1,e=Array(n.size);return n.forEach(function(o){e[++t]=o}),e},E2=Y3?Y3.prototype:void 0,w3=E2?E2.valueOf:void 0,$9=function(n,t,e,o,i,r,l){switch(e){case"[object DataView]":if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;n=n.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(n.byteLength!=t.byteLength||!r(new j2(n),new j2(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return C9(+n,+t);case"[object Error]":return n.name==t.name&&n.message==t.message;case"[object RegExp]":case"[object String]":return n==t+"";case"[object Map]":var a=v9;case"[object Set]":var c=1&o;if(a||(a=w9),n.size!=t.size&&!c)return!1;var u=l.get(n);if(u)return u==t;o|=2,l.set(n,t);var d=m9(a(n),a(t),o,i,r,l);return l.delete(n),d;case"[object Symbol]":if(w3)return w3.call(n)==w3.call(t)}return!1},y9=function(n,t){for(var e=-1,o=t.length,i=n.length;++e<o;)n[i+e]=t[e];return n},I3=Array.isArray,L9=y9,x9=I3,k9=function(n,t,e){var o=t(n);return x9(n)?o:L9(o,e(n))},b9=function(n,t){for(var e=-1,o=n==null?0:n.length,i=0,r=[];++e<o;){var l=n[e];t(l,e,n)&&(r[i++]=l)}return r},_9=function(){return[]},A9=Object.prototype.propertyIsEnumerable,P2=Object.getOwnPropertySymbols,S9=P2?function(n){return n==null?[]:(n=Object(n),b9(P2(n),function(t){return A9.call(n,t)}))}:_9,F9=function(n,t){for(var e=-1,o=Array(n);++e<n;)o[e]=t(e);return o},M9=l3,j9=c3,O2=function(n){return j9(n)&&M9(n)=="[object Arguments]"},E9=c3,C0=Object.prototype,P9=C0.hasOwnProperty,O9=C0.propertyIsEnumerable,R9=O2(function(){return arguments}())?O2:function(n){return E9(n)&&P9.call(n,"callee")&&!O9.call(n,"callee")},_3={exports:{}},z9=function(){return!1};(function(n,t){var e=y1,o=z9,i=t&&!t.nodeType&&t,r=i&&n&&!n.nodeType&&n,l=r&&r.exports===i?e.Buffer:void 0,a=(l?l.isBuffer:void 0)||o;n.exports=a})(_3,_3.exports);var m0=_3.exports,N9=/^(?:0|[1-9]\d*)$/,B9=function(n,t){var e=typeof n;return!!(t=t??9007199254740991)&&(e=="number"||e!="symbol"&&N9.test(n))&&n>-1&&n%1==0&&n<t},v0=function(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=9007199254740991},I9=l3,Z9=v0,D9=c3,Z={};Z["[object Float32Array]"]=Z["[object Float64Array]"]=Z["[object Int8Array]"]=Z["[object Int16Array]"]=Z["[object Int32Array]"]=Z["[object Uint8Array]"]=Z["[object Uint8ClampedArray]"]=Z["[object Uint16Array]"]=Z["[object Uint32Array]"]=!0,Z["[object Arguments]"]=Z["[object Array]"]=Z["[object ArrayBuffer]"]=Z["[object Boolean]"]=Z["[object DataView]"]=Z["[object Date]"]=Z["[object Error]"]=Z["[object Function]"]=Z["[object Map]"]=Z["[object Number]"]=Z["[object Object]"]=Z["[object RegExp]"]=Z["[object Set]"]=Z["[object String]"]=Z["[object WeakMap]"]=!1;var K9=function(n){return D9(n)&&Z9(n.length)&&!!Z[I9(n)]},T9=function(n){return function(t){return n(t)}},A3={exports:{}};(function(n,t){var e=I0,o=t&&!t.nodeType&&t,i=o&&n&&!n.nodeType&&n,r=i&&i.exports===o&&e.process,l=function(){try{var a=i&&i.require&&i.require("util").types;return a||r&&r.binding&&r.binding("util")}catch{}}();n.exports=l})(A3,A3.exports);var R2=A3.exports,V9=K9,q9=T9,z2=R2&&R2.isTypedArray,w0=z2?q9(z2):V9,U9=F9,G9=R9,W9=I3,H9=m0,J9=B9,Q9=w0,Y9=Object.prototype.hasOwnProperty,X9=function(n,t){var e=W9(n),o=!e&&G9(n),i=!e&&!o&&H9(n),r=!e&&!o&&!i&&Q9(n),l=e||o||i||r,a=l?U9(n.length,String):[],c=a.length;for(var u in n)!t&&!Y9.call(n,u)||l&&(u=="length"||i&&(u=="offset"||u=="parent")||r&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||J9(u,c))||a.push(u);return a},t6=Object.prototype,n6=function(n){var t=n&&n.constructor;return n===(typeof t=="function"&&t.prototype||t6)},e6=function(n,t){return function(e){return n(t(e))}},o6=e6(Object.keys,Object),i6=n6,r6=o6,s6=Object.prototype.hasOwnProperty,a6=function(n){if(!i6(n))return r6(n);var t=[];for(var e in Object(n))s6.call(n,e)&&e!="constructor"&&t.push(e);return t},l6=g0,c6=v0,u6=X9,d6=a6,g6=function(n){return n!=null&&c6(n.length)&&!l6(n)},h6=k9,f6=S9,p6=function(n){return g6(n)?u6(n):d6(n)},N2=function(n){return h6(n,p6,f6)},C6=Object.prototype.hasOwnProperty,m6=function(n,t,e,o,i,r){var l=1&e,a=N2(n),c=a.length;if(c!=N2(t).length&&!l)return!1;for(var u=c;u--;){var d=a[u];if(!(l?d in t:C6.call(t,d)))return!1}var h=r.get(n),f=r.get(t);if(h&&f)return h==t&&f==n;var g=!0;r.set(n,t),r.set(t,n);for(var p=l;++u<c;){var w=n[d=a[u]],$=t[d];if(o)var b=l?o($,w,d,t,n,r):o(w,$,d,n,t,r);if(!(b===void 0?w===$||i(w,$,e,o,r):b)){g=!1;break}p||(p=d=="constructor")}if(g&&!p){var y=n.constructor,v=t.constructor;y==v||!("constructor"in n)||!("constructor"in t)||typeof y=="function"&&y instanceof y&&typeof v=="function"&&v instanceof v||(g=!1)}return r.delete(n),r.delete(t),g},S3=I1(y1,"DataView"),F3=B3,M3=I1(y1,"Promise"),j3=I1(y1,"Set"),E3=I1(y1,"WeakMap"),$0=l3,Z1=h0,B2="[object Map]",I2="[object Promise]",Z2="[object Set]",D2="[object WeakMap]",K2="[object DataView]",v6=Z1(S3),w6=Z1(F3),$6=Z1(M3),y6=Z1(j3),L6=Z1(E3),k1=$0;(S3&&k1(new S3(new ArrayBuffer(1)))!=K2||F3&&k1(new F3)!=B2||M3&&k1(M3.resolve())!=I2||j3&&k1(new j3)!=Z2||E3&&k1(new E3)!=D2)&&(k1=function(n){var t=$0(n),e=t=="[object Object]"?n.constructor:void 0,o=e?Z1(e):"";if(o)switch(o){case v6:return K2;case w6:return B2;case $6:return I2;case y6:return Z2;case L6:return D2}return t});var $3=a9,x6=p0,k6=$9,b6=m6,T2=k1,V2=I3,q2=m0,_6=w0,U2="[object Arguments]",G2="[object Array]",Q1="[object Object]",W2=Object.prototype.hasOwnProperty,A6=function(n,t,e,o,i,r){var l=V2(n),a=V2(t),c=l?G2:T2(n),u=a?G2:T2(t),d=(c=c==U2?Q1:c)==Q1,h=(u=u==U2?Q1:u)==Q1,f=c==u;if(f&&q2(n)){if(!q2(t))return!1;l=!0,d=!1}if(f&&!d)return r||(r=new $3),l||_6(n)?x6(n,t,e,o,i,r):k6(n,t,c,e,o,i,r);if(!(1&e)){var g=d&&W2.call(n,"__wrapped__"),p=h&&W2.call(t,"__wrapped__");if(g||p){var w=g?n.value():n,$=p?t.value():t;return r||(r=new $3),i(w,$,e,o,r)}}return!!f&&(r||(r=new $3),b6(n,t,e,o,i,r))},H2=c3,S6=function n(t,e,o,i,r){return t===e||(t==null||e==null||!H2(t)&&!H2(e)?t!=t&&e!=e:A6(t,e,o,i,n,r))},F6=S6;const M6=j0(function(n,t){return F6(n,t)});function j6(n){let t,e;return t=new R4({props:{codeActions:n[5],loaded:n[9],suggestions:n[0],filepath:n[1],originalCode:n[8],onCodeAction:n[3],scrollContainer:n[7]}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,i){const r={};32&i&&(r.codeActions=o[5]),512&i&&(r.loaded=o[9]),1&i&&(r.suggestions=o[0]),2&i&&(r.filepath=o[1]),256&i&&(r.originalCode=o[8]),8&i&&(r.onCodeAction=o[3]),128&i&&(r.scrollContainer=o[7]),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function E6(n){let t,e;return t=new C4({props:{filepath:n[1],onFileAction:n[2],fileActions:n[4],slot:"summary",suggestions:n[0]}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,i){const r={};2&i&&(r.filepath=o[1]),4&i&&(r.onFileAction=o[2]),16&i&&(r.fileActions=o[4]),1&i&&(r.suggestions=o[0]),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function P6(n){let t,e;return t=new l0({props:{open:!0,class:"c-code-roll-item ",expandable:n[6],$$slots:{summary:[E6],default:[j6]},$$scope:{ctx:n}}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,[i]){const r={};64&i&&(r.expandable=o[6]),17343&i&&(r.$$scope={dirty:i,ctx:o}),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function O6(n,t,e){let{suggestions:o}=t,{filepath:i}=t,{readFile:r}=t,{onFileAction:l=N1}=t,{onCodeAction:a=N1}=t,{fileActions:c=[]}=t,{codeActions:u=[]}=t,{expandable:d=!0}=t,{scrollContainer:h}=t,f="",g=!1,p=null,w=null;return n.$$set=$=>{"suggestions"in $&&e(0,o=$.suggestions),"filepath"in $&&e(1,i=$.filepath),"readFile"in $&&e(10,r=$.readFile),"onFileAction"in $&&e(2,l=$.onFileAction),"onCodeAction"in $&&e(3,a=$.onCodeAction),"fileActions"in $&&e(4,c=$.fileActions),"codeActions"in $&&e(5,u=$.codeActions),"expandable"in $&&e(6,d=$.expandable),"scrollContainer"in $&&e(7,h=$.scrollContainer)},n.$$.update=()=>{6147&n.$$.dirty&&(M6(o,p)&&w!==null&&X3(i,w)||(w!==null&&X3(i,w)||e(9,g=!1),e(12,w=i),e(11,p=o),async function($){e(8,f=await r($)),e(9,g=!0)}(i)))},[o,i,l,a,c,u,d,h,f,g,r,p,w]}class R6 extends N{constructor(t){super(),B(this,t,O6,P6,I,{suggestions:0,filepath:1,readFile:10,onFileAction:2,onCodeAction:3,fileActions:4,codeActions:5,expandable:6,scrollContainer:7})}}function J2(n,t,e){return n.addEventListener(t,e),()=>{n.removeEventListener(t,e)}}function z6(n){n()}const{window:N6}=z3;function B6(n){let t,e,o;function i(l){n[18](l)}let r={showButton:!1,class:"c-next-edit-suggestions__drawer",initialWidth:300,expandedMinWidth:150,deadzone:50,minimizedWidth:40,$$slots:{right:[V6],left:[D6]},$$scope:{ctx:n}};return n[4]!==void 0&&(r.minimized=n[4]),t=new T0({props:r}),M1.push(()=>E0(t,"minimized",i)),{c(){O(t.$$.fragment)},m(l,a){R(t,l,a),o=!0},p(l,a){const c={};33554546&a&&(c.$$scope={dirty:a,ctx:l}),!e&&16&a&&(e=!0,c.minimized=l[4],P0(()=>e=!1)),t.$set(c)},i(l){o||(x(t.$$.fragment,l),o=!0)},o(l){_(t.$$.fragment,l),o=!1},d(l){z(t,l)}}}function I6(n){let t,e,o;return e=new O3({}),{c(){t=F("div"),O(e.$$.fragment),s(t,"class","c-next-edit-suggestions--empty svelte-xgtx0g")},m(i,r){A(i,t,r),R(e,t,null),o=!0},p:j,i(i){o||(x(e.$$.fragment,i),o=!0)},o(i){_(e.$$.fragment,i),o=!1},d(i){i&&S(t),z(e)}}}function Z6(n){let t,e,o,i,r;return i=new H0({props:{loading:n[3],$$slots:{default:[q6]},$$scope:{ctx:n}}}),i.$on("click",T1(n[14],"refresh")),{c(){t=F("div"),e=F("p"),e.textContent="No Suggestions",o=U(),O(i.$$.fragment),s(t,"class","c-next-edit-suggestions--empty svelte-xgtx0g")},m(l,a){A(l,t,a),C(t,e),C(t,o),R(i,t,null),r=!0},p(l,a){const c={};8&a&&(c.loading=l[3]),33554432&a&&(c.$$scope={dirty:a,ctx:l}),i.$set(c)},i(l){r||(x(i.$$.fragment,l),r=!0)},o(l){_(i.$$.fragment,l),r=!1},d(l){l&&S(t),z(i)}}}function D6(n){let t,e,o;return e=new h4({props:{sortedPathSuggestionsMap:n[1],onCodeAction:n[14],minimized:n[4]}}),{c(){t=F("div"),O(e.$$.fragment),s(t,"slot","left"),s(t,"class","c-next-edit-suggestions__left svelte-xgtx0g")},m(i,r){A(i,t,r),R(e,t,null),o=!0},p(i,r){const l={};2&r&&(l.sortedPathSuggestionsMap=i[1]),16&r&&(l.minimized=i[4]),e.$set(l)},i(i){o||(x(e.$$.fragment,i),o=!0)},o(i){_(e.$$.fragment,i),o=!1},d(i){i&&S(t),z(e)}}}function K6(n){let t;return{c(){t=F("div"),s(t,"class","c-next-edit-suggestions__right--empty svelte-xgtx0g")},m(e,o){A(e,t,o)},p:j,i:j,o:j,d(e){e&&S(t)}}}function T6(n){let t,e;return t=new R6({props:{filepath:n[6].qualifiedPathName,suggestions:[n[6]],onCodeAction:n[14],codeActions:n[6].state===z1.accepted?n[12]:n[11],readFile:n[8],expandable:!1,scrollContainer:n[5]}}),{c(){O(t.$$.fragment)},m(o,i){R(t,o,i),e=!0},p(o,i){const r={};64&i&&(r.filepath=o[6].qualifiedPathName),64&i&&(r.suggestions=[o[6]]),64&i&&(r.codeActions=o[6].state===z1.accepted?o[12]:o[11]),32&i&&(r.scrollContainer=o[5]),t.$set(r)},i(o){e||(x(t.$$.fragment,o),e=!0)},o(o){_(t.$$.fragment,o),e=!1},d(o){z(t,o)}}}function V6(n){let t,e,o,i;const r=[T6,K6],l=[];function a(c,u){return c[6]?0:1}return e=a(n),o=l[e]=r[e](n),{c(){t=F("div"),o.c(),s(t,"class","c-next-edit-suggestions__right svelte-xgtx0g"),s(t,"slot","right")},m(c,u){A(c,t,u),l[e].m(t,null),n[17](t),i=!0},p(c,u){let d=e;e=a(c),e===d?l[e].p(c,u):(l1(),_(l[d],1,1,()=>{l[d]=null}),c1(),o=l[e],o?o.p(c,u):(o=l[e]=r[e](c),o.c()),x(o,1),o.m(t,null))},i(c){i||(x(o),i=!0)},o(c){_(o),i=!1},d(c){c&&S(t),l[e].d(),n[17](null)}}}function q6(n){let t;return{c(){t=G("Refresh")},m(e,o){A(e,t,o)},d(e){e&&S(t)}}}function U6(n){let t,e,o,i,r;const l=[Z6,I6,B6],a=[];function c(u,d){return u[2].length===0?0:u[3]?1:2}return o=c(n),i=a[o]=l[o](n),{c(){t=F("main"),e=F("div"),i.c(),s(e,"class","c-next-edit-suggestions__container svelte-xgtx0g"),s(e,"tabindex","0"),s(e,"role","button"),s(t,"class","c-next-edit-suggestions svelte-xgtx0g"),H(t,"c-next-edit-suggestions__narrow",!n[7])},m(u,d){A(u,t,d),C(t,e),a[o].m(e,null),n[19](e),r=!0},p(u,d){let h=o;o=c(u),o===h?a[o].p(u,d):(l1(),_(a[h],1,1,()=>{a[h]=null}),c1(),i=a[o],i?i.p(u,d):(i=a[o]=l[o](u),i.c()),x(i,1),i.m(e,null)),(!r||128&d)&&H(t,"c-next-edit-suggestions__narrow",!u[7])},i(u){r||(x(i),r=!0)},o(u){_(i),r=!1},d(u){u&&S(t),a[o].d(),n[19](null)}}}function G6(n){let t,e,o,i;return t=new W0.Root({props:{$$slots:{default:[U6]},$$scope:{ctx:n}}}),{c(){O(t.$$.fragment)},m(r,l){R(t,r,l),e=!0,o||(i=W(N6,"message",n[13]),o=!0)},p(r,[l]){const a={};33554687&l&&(a.$$scope={dirty:l,ctx:r}),t.$set(a)},i(r){e||(x(t.$$.fragment,r),e=!0)},o(r){_(t.$$.fragment,r),e=!1},d(r){z(t,r),o=!1,i()}}}function W6(n,t,e){let o,i,r;const l=function(E){return async function(L){const M=await E.send({type:V.readFileRequest,data:{pathName:L}});if("error"in M.data)throw new Error(M.data.error);return M.data.content}}(new D0(n1.postMessage,3e3)),a=(c="(min-width: 500px)",S0(!1,E=>{const L=window.matchMedia(c);E((L==null?void 0:L.matches)??!1);const M=T=>E(T.matches);return L.addEventListener("change",M),()=>{L.removeEventListener("change",M)}}));var c;e1(n,a,E=>e(7,r=E));const u=a0({});e1(n,u,E=>e(16,i=E));const d=v1("active","|","reject","accept"),h=v1("active","|","reject","undo");let f,g=new Map,p=[],w=!0,$=!1;function b(E){const L=function(M){if(M===-1)return-1;let T=M;do T--;while(T>=0&&p[M].state==="stale");if(T!==-1)return T;T=M;do T++;while(T<p.length&&p[M].state==="stale");return T===p.length?-1:T}(p.findIndex(X.bind(null,E)));return o0(g,L)}function y(E){e(15,t1=!0)}function v(E){J(u,i.selectedSuggestion=void 0,i),e(15,t1=!1)}let K;t0(()=>(n1.postMessage({type:V.nextEditLoaded}),function(...E){return function(){E.forEach(z6)}}(J2(window,"focus",y),J2(window,"blur",v))));let t1=!1;return n.$$.update=()=>{1&n.$$.dirty&&K&&K.focus(),98304&n.$$.dirty&&t1&&i.nextSuggestion&&i.selectedSuggestion===void 0&&i.nextSuggestion!==i.selectedSuggestion&&J(u,i.selectedSuggestion=i.nextSuggestion,i),65536&n.$$.dirty&&e(6,o=g1(i))},[K,g,p,w,$,f,o,r,l,a,u,d,h,function(E){const L=E.data;switch(L.type){case V.nextEditPreviewActive:J(u,i={...i,activeSuggestion:L.data,nextSuggestion:L.data},i);break;case V.nextEditDismiss:J(u,i={...i,activeSuggestion:void 0},i);break;case V.nextEditActiveSuggestionChanged:J(u,i.activeSuggestion=L.data,i);break;case V.nextEditToggleSuggestionTree:e(4,$=!$);break;case V.nextEditRefreshStarted:e(3,w=!0);break;case V.nextEditRefreshFinished:e(3,w=!1);break;case V.nextEditSuggestionsChanged:e(3,w=!1),e(1,g=new Map(Z0(L.data.suggestions??[]))),e(2,p=[...g.values()].flat()),t2(p,i.nextSuggestion)||J(u,i={...i,nextSuggestion:void 0},i),t2(p,i.activeSuggestion)||J(u,i={...i,activeSuggestion:void 0},i);break;case V.nextEditNextSuggestionChanged:J(u,i={...i,nextSuggestion:L.data},i);break;case V.nextEditPanelFocus:K&&K.focus()}},(E,L)=>{switch(E){case"acceptAllInFile":return Array.isArray(L)?void n1.postMessage({type:V.nextEditSuggestionsAction,data:{acceptAllInFile:L}}):void 0;case"rejectAllInFile":return Array.isArray(L)?void n1.postMessage({type:V.nextEditSuggestionsAction,data:{rejectAllInFile:L}}):void 0;case"undoAllInFile":return Array.isArray(L)?void n1.postMessage({type:V.nextEditSuggestionsAction,data:{undoAllInFile:L}}):void 0;case"refresh":return e(3,w=!0),void n1.postMessage({type:V.nextEditRefreshStarted,data:"refresh"});case"accept":return!L||Array.isArray(L)?void 0:(J(u,i.selectedSuggestion=b(L),i),void n1.postMessage({type:V.nextEditSuggestionsAction,data:{accept:L}}));case"reject":return!L||Array.isArray(L)?void 0:void n1.postMessage({type:V.nextEditSuggestionsAction,data:{reject:L}});case"active":return!L||Array.isArray(L)?void 0:(n1.postMessage({type:V.nextEditOpenSuggestion,data:L}),void J(u,i={...i,activeSuggestion:L,selectedSuggestion:L},i));case"select":return!L||Array.isArray(L)?void 0:void J(u,i={...i,activeSuggestion:void 0,selectedSuggestion:L},i);case"dismiss":return Y1(i)==="active"?(J(u,i={...i,activeSuggestion:void 0},i),void n1.postMessage({type:V.nextEditDismiss})):void 0;case"undo":return!L||Array.isArray(L)?void 0:void n1.postMessage({type:V.nextEditSuggestionsAction,data:{undo:L}})}},t1,i,function(E){M1[E?"unshift":"push"](()=>{f=E,e(5,f)})},function(E){$=E,e(4,$)},function(E){M1[E?"unshift":"push"](()=>{K=E,e(0,K)})}]}class u8 extends N{constructor(t){super(),B(this,t,W6,G6,I,{})}}export{u8 as default};

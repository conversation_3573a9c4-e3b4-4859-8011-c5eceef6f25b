var xi=Object.defineProperty;var en=r=>{throw TypeError(r)};var wi=(r,e,t)=>e in r?xi(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var me=(r,e,t)=>wi(r,typeof e!="symbol"?e+"":e,t),ki=(r,e,t)=>e.has(r)||en("Cannot "+t);var tn=(r,e,t)=>e.has(r)?en("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t);var Dt=(r,e,t)=>(ki(r,e,"access private method"),t);import{A as Ye,S as te,i as ne,s as K,D as A,c as C,a2 as Ae,e as g,f as T,n as X,h,a5 as Te,Y as N,Z as oe,E as z,F as L,u as f,t as $,G as M,V as I,P as Ee,q as H,r as j,O as Ss,aj as Re,ae as Ps,aa as ot,a8 as Me,N as we,az as wt,al as St,ai as Xe,a6 as Ke,a7 as et,ay as Is,X as he,av as Oe,Q as dt,T as Us,ag as yi,ah as nn,b as kt,J as _e,a as Zs,as as yt,K as Be,L as ze,M as Le,g as Vs,at as sn,au as vi,W as Ai,ak as zt,af as bi}from"./SpinnerAugment-CL9SZpf8.js";import"./design-system-init-SyQ8NwYv.js";import{g as Ft,p as rn,a as Ei}from"./index-APxr5XPC.js";import"./design-system-init-BIfzX7Ug.js";import{W as Ue,I as pt,e as pe,u as Hs,o as js,h as Ws,g as _i}from"./IconButtonAugment-C4xMcLhX.js";import{M as Bi}from"./message-broker-SEbJxN6J.js";import{s as on}from"./index-D2Ut0gK2.js";import{c as vt,p as zi,g as He,M as Li,O as Ht,P as ft,a as Mi,i as Ri,b as Ti,C as Ni}from"./diff-utils-CRbaKECg.js";import{C as Qs,a as Gs,T as Js,b as qi}from"./CollapseButtonAugment-Ba7Np-LE.js";import{a as jt,g as Wt,b as Ys,S as Oi,M as Si}from"./index-iuo-Ho0S.js";import{V as $t}from"./VSCodeCodicon-BxoMn_1r.js";import{T as Ve,a as st,C as Pi}from"./CardAugment-bwPj7Y67.js";import{B as Pe}from"./ButtonAugment-iwbEjzvh.js";import{M as Lt}from"./MaterialIcon-Bh8QWD0w.js";import{i as Xs,b as We,c as Ks,d as At,n as ei,a as tt,g as le,M as ti}from"./file-paths-BPg3etNg.js";import{F as Ii}from"./types-DDm27S8B.js";import{L as ni}from"./LanguageIcon-BQz1eaw5.js";import{R as Ui}from"./ra-diff-ops-model-DUTpCop3.js";import{A as Zi}from"./async-messaging-CtwQrvzD.js";import{E as si}from"./exclamation-triangle-5FhabZKw.js";import"./toggleHighContrast-Th-X2FgN.js";import{F as Vi}from"./Filespan-C3pDA31_.js";import{M as Hi}from"./ModalAugment-DoMcZLcQ.js";import"./index-BskWw2a8.js";import"./preload-helper-Dv6uf1Os.js";class bt{constructor(e){me(this,"_opts",null);me(this,"_subscribers",new Set);this._asyncMsgSender=e}subscribe(e){return this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}}notifySubscribers(){this._subscribers.forEach(e=>e(this))}get opts(){return this._opts}updateOpts(e){this._opts=e,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const e=await this._asyncMsgSender.send({type:Ue.remoteAgentDiffPanelLoaded});this.updateOpts(e.data)}catch(e){console.error("Failed to load diff panel:",e),this.updateOpts(null)}}handleMessageFromExtension(e){const t=e.data;return!(!t||!t.type)&&t.type===Ue.remoteAgentDiffPanelSetOpts&&(this.updateOpts(t.data),!0)}}me(bt,"key","remoteAgentDiffModel");class it{constructor(e){me(this,"_applyingFilePaths",Ye([]));me(this,"_appliedFilePaths",Ye([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,n=3e4){try{return(await this._asyncMsgSender.send({type:Ue.diffExplanationRequest,data:{changedFiles:e,apikey:t}},n)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(e,t=!1,n){try{return(await this._asyncMsgSender.send({type:Ue.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:n}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(e,t){try{const n=await this._asyncMsgSender.send({type:Ue.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}},1e5);return{explanation:n.data.explanation,error:n.data.error}}catch(n){return console.error("Failed to get descriptions:",n),{explanation:[],error:`Failed to get descriptions: ${n instanceof Error?n.message:String(n)}`}}}async canApplyChanges(){try{return(await this._asyncMsgSender.send({type:Ue.canApplyChangesRequest},1e4)).data}catch(e){return console.error("Failed to check if can apply changes:",e),{canApply:!1,hasUnstagedChanges:!1,error:`Failed to check if can apply changes: ${e instanceof Error?e.message:String(e)}`}}}async applyChanges(e,t,n){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==e),e]);try{const s=await this._asyncMsgSender.send({type:Ue.applyChangesRequest,data:{path:e,originalCode:t,newCode:n}},3e4),{success:i,hasConflicts:o,error:l}=s.data;return i?this._appliedFilePaths.update(a=>[...a.filter(c=>c!==e),e]):l&&console.error("Failed to apply changes:",l),{success:i,hasConflicts:o,error:l}}catch(s){return console.error("applyChanges error",s),{success:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==e))}}async previewApplyChanges(e,t,n){try{return(await this._asyncMsgSender.send({type:Ue.previewApplyChangesRequest,data:{path:e,originalCode:t,newCode:n}},3e4)).data}catch(s){return console.error("previewApplyChanges error",s),{mergedContent:"",hasConflicts:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async openFile(e){try{const t=await this._asyncMsgSender.send({type:Ue.openFileRequest,data:{path:e}},1e4);return t.data.success||console.error("Failed to open file:",t.data.error),t.data.success}catch(t){console.error("openFile error",t)}return!1}async stashUnstagedChanges(){try{const e=await this._asyncMsgSender.send({type:Ue.stashUnstagedChangesRequest},1e4);return e.data.success||console.error("Failed to stash unstaged changes:",e.data.error),e.data.success}catch(e){console.error("stashUnstagedChanges error",e)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:Ue.reportAgentChangesApplied})}}me(it,"key","remoteAgentsDiffOpsModel");function ln(r,e,t,n,s={}){const{context:i=3,generateId:o=!0}=s,l=vt(r,e,t,n,"","",{context:i}),a=e||r;let c;return o?c=`${He(a)}-${He(t+n)}`:c=Math.random().toString(36).substring(2,15),{id:c,path:a,diff:l,originalCode:t,modifiedCode:n}}function Pt(r){const e=r.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function ii(r){return!r.originalCode||r.originalCode.trim()===""}function ri(r){return!r.modifiedCode||r.modifiedCode.trim()===""}class ji{static generateDiff(e,t,n,s){return ln(e,t,n,s)}static generateDiffs(e){return function(t,n={}){return t.map(s=>ln(s.oldPath,s.newPath,s.oldContent,s.newContent,n))}(e)}static getDiffStats(e){return Pt(e)}static getDiffObjectStats(e){return Pt(e.diff)}static isNewFile(e){return ii(e)}static isDeletedFile(e){return ri(e)}}function Wi(r){let e;return{c(){e=N(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&oe(e,t[1])},d(t){t&&h(e)}}}function Qi(r){let e;return{c(){e=N(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&oe(e,t[1])},d(t){t&&h(e)}}}function Gi(r){let e,t,n;function s(l,a){return l[2]?Qi:Wi}let i=s(r),o=i(r);return{c(){e=A("span"),t=A("code"),o.c(),C(t,"class","markdown-codespan svelte-11ta4gi"),C(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ae(t,"markdown-string",r[4])},m(l,a){g(l,e,a),T(e,t),o.m(t,null),r[6](e)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&C(t,"style",n),16&a&&Ae(t,"markdown-string",l[4])},i:X,o:X,d(l){l&&h(e),o.d(),r[6](null)}}}function Ji(r,e,t){let n,s,i,o,{token:l}=e,{element:a}=e;return r.$$set=c=>{"token"in c&&t(5,l=c.token),"element"in c&&t(0,a=c.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(c){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(c))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let u,d,p;return c.length===4?(u=parseInt(c.charAt(1),16),d=parseInt(c.charAt(2),16),p=parseInt(c.charAt(3),16),u*=17,d*=17,p*=17):(u=parseInt(c.slice(1,3),16),d=parseInt(c.slice(3,5),16),p=parseInt(c.slice(5,7),16)),.299*u+.587*d+.114*p<130}(n))},[a,n,i,o,s,l,function(c){Te[c?"unshift":"push"](()=>{a=c,t(0,a)})}]}let Yi=class extends te{constructor(r){super(),ne(this,r,Ji,Gi,K,{token:5,element:0})}};function Xi(r){let e,t;return e=new Li({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ki(r,e,t){let{markdown:n}=e;const s={codespan:Yi};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}let oi=class extends te{constructor(r){super(),ne(this,r,Ki,Xi,K,{markdown:0})}};function an(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function cn(r){let e,t,n,s,i;t=new pt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[nr]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let c=0;c<o.length;c+=1)l[c]=un(an(r,o,c));const a=c=>$(l[c],1,1,()=>{l[c]=null});return{c(){e=A("div"),z(t.$$.fragment),n=I(),s=A("div");for(let c=0;c<l.length;c+=1)l[c].c();C(e,"class","toggle-button svelte-14s1ghg"),C(s,"class","descriptions svelte-14s1ghg"),Ee(s,"transform","translateY("+-r[4]+"px)")},m(c,u){g(c,e,u),L(t,e,null),g(c,n,u),g(c,s,u);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(c,u){const d={};if(1&u[0]|524288&u[1]&&(d.$$scope={dirty:u,ctx:c}),t.$set(d),546&u[0]){let p;for(o=pe(c[1]),p=0;p<o.length;p+=1){const w=an(c,o,p);l[p]?(l[p].p(w,u),f(l[p],1)):(l[p]=un(w),l[p].c(),f(l[p],1),l[p].m(s,null))}for(H(),p=o.length;p<l.length;p+=1)a(p);j()}(!i||16&u[0])&&Ee(s,"transform","translateY("+-c[4]+"px)")},i(c){if(!i){f(t.$$.fragment,c);for(let u=0;u<o.length;u+=1)f(l[u]);i=!0}},o(c){$(t.$$.fragment,c),l=l.filter(Boolean);for(let u=0;u<l.length;u+=1)$(l[u]);i=!1},d(c){c&&(h(e),h(n),h(s)),M(t),Me(l,c)}}}function er(r){let e,t;return e=new $t({props:{icon:"book"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function tr(r){let e,t;return e=new $t({props:{icon:"x"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function nr(r){let e,t,n,s;const i=[tr,er],o=[];function l(a,c){return a[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e!==u&&(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function un(r){let e,t,n,s;return t=new oi({props:{markdown:r[47].text}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),C(e,"class","description svelte-14s1ghg"),Ee(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ee(e,"--user-theme-sidebar-background","transparent")},m(i,o){g(i,e,o),L(t,e,null),T(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[47].text),t.$set(l),(!s||34&o[0])&&Ee(e,"top",(i[5][i[49]]||i[9](i[47]))+"px")},i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&h(e),M(t)}}}function sr(r){let e,t,n,s,i=r[1].length>0&&cn(r);return{c(){e=A("div"),t=A("div"),n=I(),i&&i.c(),C(t,"class","editor-container svelte-14s1ghg"),Ee(t,"height",r[3]+"px"),C(e,"class","monaco-diff-container svelte-14s1ghg"),Ae(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),T(e,t),r[23](t),T(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&Ee(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&f(i,1)):(i=cn(o),i.c(),f(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),j()),(!s||3&l[0])&&Ae(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(f(i),s=!0)},o(o){$(i),s=!1},d(o){o&&h(e),r[23](null),i&&i.d()}}}function ir(r,e,t){let n,s,i;const o=Ss();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:c}=e,{descriptions:u=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:w=[]}=e,{theme:k}=e,{areDescriptionsVisible:x=!0}=e,{isNewFile:E=!1}=e,{isDeletedFile:_=!1}=e;const B=jt.getContext().monaco;let m,D,F,y;Re(r,B,v=>t(22,n=v));let R,q=[];const O=Wt();let V,W=Ye(0);Re(r,W,v=>t(4,s=v));let ee=E?20*a.split(`
`).length+40:100;const de=n?n.languages.getLanguages().map(v=>v.id):[];function ce(v,S){var b,U;if(S){const Z=(b=S.split(".").pop())==null?void 0:b.toLowerCase();if(Z){const Q=(U=n==null?void 0:n.languages.getLanguages().find(se=>{var G;return(G=se.extensions)==null?void 0:G.includes("."+Z)}))==null?void 0:U.id;if(Q&&de.includes(Q))return Q}}return"plaintext"}const ae=Ye({});Re(r,ae,v=>t(5,i=v));let J=null;function $e(){if(!m)return;q=q.filter(b=>(b.dispose(),!1));const v=m.getOriginalEditor(),S=m.getModifiedEditor();q.push(v.onDidScrollChange(()=>{wt(W,s=v.getScrollTop(),s)}),S.onDidScrollChange(()=>{wt(W,s=S.getScrollTop(),s)}))}function De(){if(!m||!R)return;const v=m.getOriginalEditor(),S=m.getModifiedEditor();q.push(S.onDidContentSizeChange(()=>O.requestLayout()),v.onDidContentSizeChange(()=>O.requestLayout()),m.onDidUpdateDiff(()=>O.requestLayout()),S.onDidChangeHiddenAreas(()=>O.requestLayout()),v.onDidChangeHiddenAreas(()=>O.requestLayout()),S.onDidLayoutChange(()=>O.requestLayout()),v.onDidLayoutChange(()=>O.requestLayout()),S.onDidFocusEditorWidget(()=>{ye(!0)}),v.onDidFocusEditorWidget(()=>{ye(!0)}),S.onDidBlurEditorWidget(()=>{ye(!1)}),v.onDidBlurEditorWidget(()=>{ye(!1)}),S.onDidChangeModelContent(()=>{xe=!0,Ne=Date.now();const b=(y==null?void 0:y.getValue())||"";if(b===a)return;const U=b.replace(p.join(""),"").replace(w.join(""),"");o("codeChange",{modifiedCode:U});const Z=setTimeout(()=>{xe=!1},500);q.push({dispose:()=>clearTimeout(Z)})})),function(){!R||!m||(J&&clearTimeout(J),J=setTimeout(()=>{if(!R.__hasClickListener){const b=U=>{const Z=U.target;Z&&(Z.closest('[title="Show Unchanged Region"]')||Z.closest('[title="Hide Unchanged Region"]'))&&ve()};R.addEventListener("click",b),t(2,R.__hasClickListener=!0,R),q.push({dispose:()=>{R.removeEventListener("click",b)}})}m&&q.push(m.onDidUpdateDiff(()=>{ve()}))},300))}()}Ps(()=>{m==null||m.dispose(),D==null||D.dispose(),F==null||F.dispose(),y==null||y.dispose(),q.forEach(v=>v.dispose()),J&&clearTimeout(J),V==null||V()});let ie=null;function ve(){ie&&clearTimeout(ie),ie=setTimeout(()=>{O.requestLayout(),ie=null},100),ie&&q.push({dispose:()=>{ie&&(clearTimeout(ie),ie=null)}})}function ke(v,S,b,U=[],Z=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");F==null||F.dispose(),y==null||y.dispose(),S=S||"",b=b||"";const Q=U.join(""),se=Z.join("");if(S=E?b.split(`
`).map(()=>" ").join(`
`):Q+S+se,b=Q+b+se,F=n.editor.createModel(S,void 0,v!==void 0?n.Uri.parse("file://"+v+`#${crypto.randomUUID()}`):void 0),_&&(b=b.split(`
`).map(()=>" ").join(`
`)),t(21,y=n.editor.createModel(b,void 0,v!==void 0?n.Uri.parse("file://"+v+`#${crypto.randomUUID()}`):void 0)),m){m.setModel({original:F,modified:y});const G=m.getOriginalEditor();G&&G.updateOptions({lineNumbers:"off"}),$e(),J&&clearTimeout(J),J=setTimeout(()=>{De(),J=null},300)}}ot(()=>{if(n)if(E){t(20,D=n.editor.create(R,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:k,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:U=>`${d-p.length+U}`}));const v=ce(0,c);t(21,y=n.editor.createModel(a,v,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),D.setModel(y),q.push(D.onDidChangeModelContent(()=>{xe=!0,Ne=Date.now();const U=(y==null?void 0:y.getValue())||"";if(U===a)return;o("codeChange",{modifiedCode:U});const Z=setTimeout(()=>{xe=!1},500);q.push({dispose:()=>clearTimeout(Z)})})),q.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const S=D.getContentHeight();t(3,ee=Math.max(S,60));const b=setTimeout(()=>{D==null||D.layout()},0);q.push({dispose:()=>clearTimeout(b)})}else t(19,m=n.editor.createDiffEditor(R,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:k,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:v=>`${d-p.length+v}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),V&&V(),V=O.registerEditor({editor:m,updateHeight:be,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ke(c,l,a,p,w),$e(),De(),J&&clearTimeout(J),J=setTimeout(()=>{O.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let xe=!1,Ne=0;function fe(v,S=!0){return m?(S?m.getModifiedEditor():m.getOriginalEditor()).getTopForLineNumber(v):18*v}function be(){if(!m)return;const v=m.getModel(),S=v==null?void 0:v.original,b=v==null?void 0:v.modified;if(!S||!b)return;const U=m.getOriginalEditor(),Z=m.getModifiedEditor(),Q=m.getLineChanges()||[];let se;if(Q.length===0){const G=U.getContentHeight(),re=Z.getContentHeight();se=Math.max(100,G,re)}else{let G=0,re=0;for(const ue of Q)ue.originalEndLineNumber>0&&(G=Math.max(G,ue.originalEndLineNumber)),ue.modifiedEndLineNumber>0&&(re=Math.max(re,ue.modifiedEndLineNumber));G=Math.min(G+3,S.getLineCount()),re=Math.min(re+3,b.getLineCount());const Y=U.getTopForLineNumber(G),ge=Z.getTopForLineNumber(re);se=Math.max(Y,ge)+60}t(3,ee=Math.min(se,2e4)),m.layout(),Ge()}function ye(v){if(!m)return;const S=m.getOriginalEditor(),b=m.getModifiedEditor();S.updateOptions({scrollbar:{handleMouseWheel:v}}),b.updateOptions({scrollbar:{handleMouseWheel:v}})}function P(v){if(!m)return D?D.getTopForLineNumber(v.range.start+1):0;const S=m.getModel(),b=S==null?void 0:S.original,U=S==null?void 0:S.modified;if(!b||!U)return 0;const Z=fe(v.range.start+1,!1),Q=fe(v.range.start+1,!0);return Z&&!Q?Z:!Z&&Q?Q:Math.min(Z,Q)}function Ge(){if(!m&&!D||u.length===0)return;const v={};u.forEach((S,b)=>{v[b]=P(S)}),ae.set(v)}return r.$$set=v=>{"originalCode"in v&&t(10,l=v.originalCode),"modifiedCode"in v&&t(11,a=v.modifiedCode),"path"in v&&t(12,c=v.path),"descriptions"in v&&t(1,u=v.descriptions),"lineOffset"in v&&t(13,d=v.lineOffset),"extraPrefixLines"in v&&t(14,p=v.extraPrefixLines),"extraSuffixLines"in v&&t(15,w=v.extraSuffixLines),"theme"in v&&t(16,k=v.theme),"areDescriptionsVisible"in v&&t(0,x=v.areDescriptionsVisible),"isNewFile"in v&&t(17,E=v.isNewFile),"isDeletedFile"in v&&t(18,_=v.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(v=a,!(xe||Date.now()-Ne<1e3||y&&y.getValue()===p.join("")+v+w.join(""))))if(E&&D){if(y)y.setValue(a);else{const S=ce(0,c);n&&t(21,y=n.editor.createModel(a,S,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),y&&D.setModel(y)}t(3,ee=20*a.split(`
`).length+40),D.layout()}else!E&&m&&(ke(c,l,a,p,w),O.requestLayout());var v;if(1572866&r.$$.dirty[0]&&(m||D)&&u.length>0&&Ge(),1181696&r.$$.dirty[0]&&E&&a&&D){const S=D.getContentHeight();t(3,ee=Math.max(S,60)),D.layout()}},[x,u,R,ee,s,i,B,W,ae,P,l,a,c,d,p,w,k,E,_,m,D,y,n,function(v){Te[v?"unshift":"push"](()=>{R=v,t(2,R)})},()=>t(0,x=!x)]}let rr=class extends te{constructor(r){super(),ne(this,r,ir,sr,K,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}};const li=Symbol("focusedPath");function ai(){return Xe(li)}function It(r){return`file-diff-${He(r)}`}function or(r){let e,t,n;function s(o){r[41](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),e=new rr({props:i}),Te.push(()=>Ke(e,"areDescriptionsVisible",s)),e.$on("codeChange",r[26]),{c(){z(e.$$.fragment)},m(o,l){L(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],et(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){$(e.$$.fragment,o),n=!1},d(o){M(e,o)}}}function lr(r){let e,t,n;return t=new he({props:{size:1,$$slots:{default:[ur]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","too-large-message svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};5888&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function ar(r){let e,t,n;return t=new he({props:{$$slots:{default:[$r]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","binary-file-message svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};2101632&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function cr(r){let e,t,n,s;const i=[hr,gr],o=[];function l(a,c){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=i[t](r)),{c(){e=A("div"),n&&n.c(),C(e,"class","image-container svelte-1536g7w")},m(a,c){g(a,e,c),~t&&o[t].m(e,null),s=!0},p(a,c){let u=t;t=l(a),t===u?~t&&o[t].p(a,c):(n&&(H(),$(o[u],1,1,()=>{o[u]=null}),j()),~t?(n=o[t],n?n.p(a,c):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){s||(f(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),~t&&o[t].d()}}}function ur(r){let e,t,n,s,i,o,l,a=le(r[12])+"",c=(r[8]?r[10]:r[9])+"";return{c(){e=N('File "'),t=N(a),n=N('" is too large to display a diff (size: '),s=N(c),i=N(" bytes, max: "),o=N(ti),l=N(" bytes).")},m(u,d){g(u,e,d),g(u,t,d),g(u,n,d),g(u,s,d),g(u,i,d),g(u,o,d),g(u,l,d)},p(u,d){4096&d[0]&&a!==(a=le(u[12])+"")&&oe(t,a),1792&d[0]&&c!==(c=(u[8]?u[10]:u[9])+"")&&oe(s,c)},d(u){u&&(h(e),h(t),h(n),h(s),h(i),h(o),h(l))}}}function dr(r){let e,t,n,s=le(r[12])+"";return{c(){e=N("Binary file modified: "),t=N(s),n=N(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){4096&o[0]&&s!==(s=le(i[12])+"")&&oe(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function pr(r){let e,t,n,s=le(r[12])+"";return{c(){e=N("Binary file deleted: "),t=N(s),n=N(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){4096&o[0]&&s!==(s=le(i[12])+"")&&oe(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function fr(r){let e,t,n,s=le(r[12])+"";return{c(){e=N("Binary file added: "),t=N(s),n=N(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){4096&o[0]&&s!==(s=le(i[12])+"")&&oe(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function $r(r){let e;function t(i,o){return i[21]||i[7]?fr:i[8]?pr:dr}let n=t(r),s=n(r);return{c(){s.c(),e=N(`
            No text preview available.`)},m(i,o){s.m(i,o),g(i,e,o)},p(i,o){n===(n=t(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},d(i){i&&h(e),s.d(i)}}}function gr(r){let e,t,n,s,i,o,l,a;e=new he({props:{class:"image-info-text",$$slots:{default:[Fr]},$$scope:{ctx:r}}});let c=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&dn(r);return{c(){z(e.$$.fragment),t=I(),n=A("img"),o=I(),c&&c.c(),l=we(),Oe(n.src,s="data:"+r[19]+";base64,"+btoa(r[6]))||C(n,"src",s),C(n,"alt",i="Current "+le(r[12])),C(n,"class","image-preview svelte-1536g7w")},m(u,d){L(e,u,d),g(u,t,d),g(u,n,d),g(u,o,d),c&&c.m(u,d),g(u,l,d),a=!0},p(u,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:u}),e.$set(p),(!a||524352&d[0]&&!Oe(n.src,s="data:"+u[19]+";base64,"+btoa(u[6])))&&C(n,"src",s),(!a||4096&d[0]&&i!==(i="Current "+le(u[12])))&&C(n,"alt",i),u[0].originalCode&&u[6]!==u[0].originalCode&&!u[21]?c?(c.p(u,d),2097217&d[0]&&f(c,1)):(c=dn(u),c.c(),f(c,1),c.m(l.parentNode,l)):c&&(H(),$(c,1,1,()=>{c=null}),j())},i(u){a||(f(e.$$.fragment,u),f(c),a=!0)},o(u){$(e.$$.fragment,u),$(c),a=!1},d(u){u&&(h(t),h(n),h(o),h(l)),M(e,u),c&&c.d(u)}}}function hr(r){let e,t,n,s;e=new he({props:{class:"image-info-text",$$slots:{default:[xr]},$$scope:{ctx:r}}});let i=r[0].originalCode&&pn(r);return{c(){z(e.$$.fragment),t=I(),i&&i.c(),n=we()},m(o,l){L(e,o,l),g(o,t,l),i&&i.m(o,l),g(o,n,l),s=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&f(i,1)):(i=pn(o),i.c(),f(i,1),i.m(n.parentNode,n)):i&&(H(),$(i,1,1,()=>{i=null}),j())},i(o){s||(f(e.$$.fragment,o),f(i),s=!0)},o(o){$(e.$$.fragment,o),$(i),s=!1},d(o){o&&(h(t),h(n)),M(e,o),i&&i.d(o)}}}function mr(r){let e;return{c(){e=N("Image modified")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Dr(r){let e;return{c(){e=N("New image added")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Fr(r){let e,t,n=le(r[12])+"";function s(l,a){return l[21]||l[7]?Dr:mr}let i=s(r),o=i(r);return{c(){o.c(),e=N(": "),t=N(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=le(l[12])+"")&&oe(t,n)},d(l){l&&(h(e),h(t)),o.d(l)}}}function dn(r){let e,t,n,s,i,o;return e=new he({props:{class:"image-info-text",$$slots:{default:[Cr]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=I(),n=A("img"),Oe(n.src,s="data:"+We(r[3])+";base64,"+btoa(r[0].originalCode))||C(n,"src",s),C(n,"alt",i="Original "+le(r[12])),C(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){L(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),e.$set(c),(!o||9&a[0]&&!Oe(n.src,s="data:"+We(l[3])+";base64,"+btoa(l[0].originalCode)))&&C(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+le(l[12])))&&C(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),M(e,l)}}}function Cr(r){let e;return{c(){e=N("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function xr(r){let e,t,n=le(r[12])+"";return{c(){e=N("Image deleted: "),t=N(n)},m(s,i){g(s,e,i),g(s,t,i)},p(s,i){4096&i[0]&&n!==(n=le(s[12])+"")&&oe(t,n)},d(s){s&&(h(e),h(t))}}}function pn(r){let e,t,n,s,i,o;return e=new he({props:{class:"image-info-text",$$slots:{default:[wr]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=I(),n=A("img"),Oe(n.src,s="data:"+We(r[3])+";base64,"+btoa(r[0].originalCode))||C(n,"src",s),C(n,"alt",i="Original "+le(r[12])),C(n,"class","image-preview svelte-1536g7w")},m(l,a){L(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),e.$set(c),(!o||9&a[0]&&!Oe(n.src,s="data:"+We(l[3])+";base64,"+btoa(l[0].originalCode)))&&C(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+le(l[12])))&&C(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),M(e,l)}}}function wr(r){let e;return{c(){e=N("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function kr(r){let e,t,n,s;const i=[cr,ar,lr,or],o=[];function l(a,c){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=i[t](r),{c(){e=A("div"),n.c(),C(e,"class","changes svelte-1536g7w")},m(a,c){g(a,e,c),o[t].m(e,null),s=!0},p(a,c){let u=t;t=l(a),t===u?o[t].p(a,c):(H(),$(o[u],1,1,()=>{o[u]=null}),j(),n=o[t],n?n.p(a,c):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null))},i(a){s||(f(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),o[t].d()}}}function yr(r){let e,t=le(r[12])+"";return{c(){e=N(t)},m(n,s){g(n,e,s)},p(n,s){4096&s[0]&&t!==(t=le(n[12])+"")&&oe(e,t)},d(n){n&&h(e)}}}function vr(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[yr]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};4096&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function fn(r){let e,t,n=tt(r[12])+"";return{c(){e=A("span"),t=N(n),C(e,"class","c-directory svelte-1536g7w")},m(s,i){g(s,e,i),T(e,t)},p(s,i){4096&i[0]&&n!==(n=tt(s[12])+"")&&oe(t,n)},d(s){s&&h(e)}}}function Ar(r){let e,t,n,s=r[23]>0&&$n(r),i=r[22]>0&&gn(r);return{c(){e=A("div"),s&&s.c(),t=I(),i&&i.c(),C(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),s&&s.m(e,null),T(e,t),i&&i.m(e,null),n=!0},p(o,l){o[23]>0?s?(s.p(o,l),8388608&l[0]&&f(s,1)):(s=$n(o),s.c(),f(s,1),s.m(e,t)):s&&(H(),$(s,1,1,()=>{s=null}),j()),o[22]>0?i?(i.p(o,l),4194304&l[0]&&f(i,1)):(i=gn(o),i.c(),f(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),j())},i(o){n||(f(s),f(i),n=!0)},o(o){$(s),$(i),n=!1},d(o){o&&h(e),s&&s.d(),i&&i.d()}}}function br(r){let e;return{c(){e=A("span"),e.textContent="New File",C(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&h(e)}}}function $n(r){let e,t,n;return t=new he({props:{size:1,$$slots:{default:[Er]},$$scope:{ctx:r}}}),{c(){e=A("span"),z(t.$$.fragment),C(e,"class","additions svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};8388608&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Er(r){let e,t;return{c(){e=N("+"),t=N(r[23])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){8388608&s[0]&&oe(t,n[23])},d(n){n&&(h(e),h(t))}}}function gn(r){let e,t,n;return t=new he({props:{size:1,$$slots:{default:[_r]},$$scope:{ctx:r}}}),{c(){e=A("span"),z(t.$$.fragment),C(e,"class","deletions svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};4194304&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function _r(r){let e,t;return{c(){e=N("-"),t=N(r[22])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){4194304&s[0]&&oe(t,n[22])},d(n){n&&(h(e),h(t))}}}function Br(r){let e;return{c(){e=N("Apply")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function zr(r){let e;return{c(){e=N("Applied")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Lr(r){let e,t,n;return t=new ft({}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","applied__icon svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Mr(r){let e,t,n;return t=new Lt({props:{iconName:"check"}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","applied svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Rr(r){let e,t,n,s,i;function o(p,w){return p[5]?zr:Br}let l=o(r),a=l(r);const c=[Mr,Lr],u=[];function d(p,w){return p[5]?0:1}return t=d(r),n=u[t]=c[t](r),{c(){a.c(),e=I(),n.c(),s=we()},m(p,w){a.m(p,w),g(p,e,w),u[t].m(p,w),g(p,s,w),i=!0},p(p,w){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let k=t;t=d(p),t!==k&&(H(),$(u[k],1,1,()=>{u[k]=null}),j(),n=u[t],n||(n=u[t]=c[t](p),n.c()),f(n,1),n.m(s.parentNode,s))},i(p){i||(f(n),i=!0)},o(p){$(n),i=!1},d(p){p&&(h(e),h(s)),a.d(p),u[t].d(p)}}}function Tr(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Rr]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[0]&&(i.disabled=n[14]),32&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function hn(r){let e,t;return e=new Ve({props:{content:r[11],triggerOn:[st.Hover],delayDurationMs:300,$$slots:{default:[qr]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Nr(r){let e,t;return e=new Ht({}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function qr(r){let e,t;return e=new pt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Nr]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Or(r){let e,t,n,s,i,o,l,a,c,u,d,p,w,k=tt(r[12]);t=new Gs({}),i=new Ve({props:{content:r[11],triggerOn:[st.Hover],delayDurationMs:300,$$slots:{default:[vr]},$$scope:{ctx:r}}});let x=k&&fn(r);const E=[br,Ar],_=[];function B(D,F){return D[21]?0:1}a=B(r),c=_[a]=E[a](r),d=new Ve({props:{content:r[13],triggerOn:[st.Hover],delayDurationMs:300,$$slots:{default:[Tr]},$$scope:{ctx:r}}});let m=r[5]&&hn(r);return{c(){e=A("div"),z(t.$$.fragment),n=I(),s=A("div"),z(i.$$.fragment),o=I(),x&&x.c(),l=I(),c.c(),u=I(),z(d.$$.fragment),p=I(),m&&m.c(),C(s,"class","c-path svelte-1536g7w"),C(e,"slot","header"),C(e,"class","header svelte-1536g7w")},m(D,F){g(D,e,F),L(t,e,null),T(e,n),T(e,s),L(i,s,null),T(s,o),x&&x.m(s,null),T(e,l),_[a].m(e,null),T(e,u),L(d,e,null),T(e,p),m&&m.m(e,null),w=!0},p(D,F){const y={};2048&F[0]&&(y.content=D[11]),4096&F[0]|16384&F[1]&&(y.$$scope={dirty:F,ctx:D}),i.$set(y),4096&F[0]&&(k=tt(D[12])),k?x?x.p(D,F):(x=fn(D),x.c(),x.m(s,null)):x&&(x.d(1),x=null);let R=a;a=B(D),a===R?_[a].p(D,F):(H(),$(_[R],1,1,()=>{_[R]=null}),j(),c=_[a],c?c.p(D,F):(c=_[a]=E[a](D),c.c()),f(c,1),c.m(e,u));const q={};8192&F[0]&&(q.content=D[13]),16416&F[0]|16384&F[1]&&(q.$$scope={dirty:F,ctx:D}),d.$set(q),D[5]?m?(m.p(D,F),32&F[0]&&f(m,1)):(m=hn(D),m.c(),f(m,1),m.m(e,null)):m&&(H(),$(m,1,1,()=>{m=null}),j())},i(D){w||(f(t.$$.fragment,D),f(i.$$.fragment,D),f(c),f(d.$$.fragment,D),f(m),w=!0)},o(D){$(t.$$.fragment,D),$(i.$$.fragment,D),$(c),$(d.$$.fragment,D),$(m),w=!1},d(D){D&&h(e),M(t),M(i),x&&x.d(),_[a].d(),M(d),m&&m.d()}}}function Sr(r){let e,t,n,s,i;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[Or],default:[kr]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Qs({props:l}),Te.push(()=>Ke(t,"collapsed",o)),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c svelte-1536g7w"),C(e,"id",s=It(r[3])),Ae(e,"focused",r[24]===r[3])},m(a,c){g(a,e,c),L(t,e,null),i=!0},p(a,c){const u={};16777211&c[0]|16384&c[1]&&(u.$$scope={dirty:c,ctx:a}),!n&&4&c[0]&&(n=!0,u.collapsed=a[2],et(()=>n=!1)),t.$set(u),(!i||8&c[0]&&s!==(s=It(a[3])))&&C(e,"id",s),(!i||16777224&c[0])&&Ae(e,"focused",a[24]===a[3])},i(a){i||(f(t.$$.fragment,a),i=!0)},o(a){$(t.$$.fragment,a),i=!1},d(a){a&&h(e),M(t)}}}function Pr(r,e,t){let n,s,i,o,l,a,c,u,d,p,w,k,x,E,_,B,m,D,F,y,R,q,O;Re(r,Is,P=>t(40,q=P));let{path:V}=e,{change:W}=e,{descriptions:ee=[]}=e,{areDescriptionsVisible:de=!0}=e,{isExpandedDefault:ce}=e,{isCollapsed:ae=!ce}=e,{isApplying:J}=e,{hasApplied:$e}=e,{onApplyChanges:De}=e,{onCodeChange:ie}=e,{onOpenFile:ve}=e,{isAgentFromDifferentRepo:ke=!1}=e;const xe=ai();Re(r,xe,P=>t(24,O=P));const Ne=Xe(it.key);let fe=W.modifiedCode,be=F;function ye(){t(11,be=`Open ${F??"file"}`)}return ot(()=>{ye()}),r.$$set=P=>{"path"in P&&t(3,V=P.path),"change"in P&&t(0,W=P.change),"descriptions"in P&&t(4,ee=P.descriptions),"areDescriptionsVisible"in P&&t(1,de=P.areDescriptionsVisible),"isExpandedDefault"in P&&t(29,ce=P.isExpandedDefault),"isCollapsed"in P&&t(2,ae=P.isCollapsed),"isApplying"in P&&t(30,J=P.isApplying),"hasApplied"in P&&t(5,$e=P.hasApplied),"onApplyChanges"in P&&t(31,De=P.onApplyChanges),"onCodeChange"in P&&t(32,ie=P.onCodeChange),"onOpenFile"in P&&t(33,ve=P.onOpenFile),"isAgentFromDifferentRepo"in P&&t(34,ke=P.isAgentFromDifferentRepo)},r.$$.update=()=>{var P;1&r.$$.dirty[0]&&t(6,fe=W.modifiedCode),1&r.$$.dirty[0]&&t(39,n=Pt(W.diff)),256&r.$$.dirty[1]&&t(23,s=n.additions),256&r.$$.dirty[1]&&t(22,i=n.deletions),1&r.$$.dirty[0]&&t(21,o=ii(W)),1&r.$$.dirty[0]&&t(20,l=ri(W)),8&r.$$.dirty[0]&&t(38,a=Xs(V)),8&r.$$.dirty[0]&&t(19,c=We(V)),8&r.$$.dirty[0]&&t(37,u=Ks(V)),1&r.$$.dirty[0]&&t(10,d=((P=W.originalCode)==null?void 0:P.length)||0),64&r.$$.dirty[0]&&t(9,p=(fe==null?void 0:fe.length)||0),1024&r.$$.dirty[0]&&t(36,w=At(d)),512&r.$$.dirty[0]&&t(35,k=At(p)),65&r.$$.dirty[0]&&t(8,x=!fe&&!!W.originalCode),65&r.$$.dirty[0]&&t(7,E=!!fe&&!W.originalCode),128&r.$$.dirty[1]&&t(18,_=a),192&r.$$.dirty[1]&&t(17,B=!a&&u),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,m=!a&&!u&&(k||x&&w||E&&k)),512&r.$$.dirty[1]&&t(15,D=Ys(q==null?void 0:q.category,q==null?void 0:q.intensity)),8&r.$$.dirty[0]&&t(12,F=ei(V)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,y=J||ke),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,R=J?"Applying changes...":$e?"Reapply changes to local file":ke?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[W,de,ae,V,ee,$e,fe,E,x,p,d,be,F,R,y,D,m,B,_,c,l,o,i,s,O,xe,function(P){t(6,fe=P.detail.modifiedCode),ie==null||ie(fe)},function(){Ne.reportApplyChangesEvent(),t(0,W.modifiedCode=fe,W),ie==null||ie(fe),De==null||De()},async function(){ve&&(t(11,be="Opening file..."),await ve()?ye():(t(11,be="Failed to open file. Does the file exist?"),setTimeout(()=>{ye()},2e3)))},ce,J,De,ie,ve,ke,k,w,u,a,n,q,function(P){de=P,t(1,de)},function(P){ae=P,t(2,ae)}]}let Ir=class extends te{constructor(r){super(),ne(this,r,Pr,Sr,K,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}};function mn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Ur(r){let e,t;return e=new ni({props:{filename:r[0].name}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.filename=n[0].name),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Zr(r){let e,t;return e=new $t({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.icon=n[0].isExpanded?"chevron-down":"chevron-right"),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Vr(r){let e,t,n=(r[0].displayName||r[0].name)+"";return{c(){e=A("span"),t=N(n),C(e,"class","full-path-text svelte-qnxoj")},m(s,i){g(s,e,i),T(e,t)},p(s,i){1&i&&n!==(n=(s[0].displayName||s[0].name)+"")&&oe(t,n)},d(s){s&&h(e)}}}function Dn(r){let e,t,n=pe(Array.from(r[0].children.values()).sort(Cn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=Fn(mn(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){e=A("div");for(let o=0;o<s.length;o+=1)s[o].c();C(e,"class","tree-node__children svelte-qnxoj"),C(e,"role","group")},m(o,l){g(o,e,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(o,l){if(3&l){let a;for(n=pe(Array.from(o[0].children.values()).sort(Cn)),a=0;a<n.length;a+=1){const c=mn(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=Fn(c),s[a].c(),f(s[a],1),s[a].m(e,null))}for(H(),a=n.length;a<s.length;a+=1)i(a);j()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Me(s,o)}}}function Fn(r){let e,t;return e=new ci({props:{node:r[6],indentLevel:r[1]+1}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.node=n[6]),2&s&&(i.indentLevel=n[1]+1),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Hr(r){let e,t,n,s,i,o,l,a,c,u,d,p,w,k,x,E,_;const B=[Zr,Ur],m=[];function D(y,R){return y[0].isFile?1:0}o=D(r),l=m[o]=B[o](r),u=new he({props:{size:1,$$slots:{default:[Vr]},$$scope:{ctx:r}}});let F=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&Dn(r);return{c(){e=A("div"),t=A("div"),n=A("div"),s=I(),i=A("div"),l.c(),a=I(),c=A("span"),z(u.$$.fragment),k=I(),F&&F.c(),C(n,"class","tree-node__indent svelte-qnxoj"),Ee(n,"width",6*r[1]+"px"),C(i,"class","tree-node__icon-container svelte-qnxoj"),C(c,"class","tree-node__label svelte-qnxoj"),C(c,"title",d=r[0].displayName||r[0].name),Ae(c,"full-path",r[0].displayName),C(t,"class","tree-node__content svelte-qnxoj"),C(t,"role","treeitem"),C(t,"tabindex","0"),C(t,"aria-selected",p=r[0].path===r[2]),C(t,"aria-expanded",w=r[0].isFile?void 0:r[0].isExpanded),Ae(t,"selected",r[0].path===r[2]),Ae(t,"collapsed-folder",r[0].displayName&&!r[0].isFile),C(e,"class","tree-node svelte-qnxoj")},m(y,R){g(y,e,R),T(e,t),T(t,n),T(t,s),T(t,i),m[o].m(i,null),T(t,a),T(t,c),L(u,c,null),T(e,k),F&&F.m(e,null),x=!0,E||(_=[dt(t,"click",r[4]),dt(t,"keydown",r[5])],E=!0)},p(y,[R]){(!x||2&R)&&Ee(n,"width",6*y[1]+"px");let q=o;o=D(y),o===q?m[o].p(y,R):(H(),$(m[q],1,1,()=>{m[q]=null}),j(),l=m[o],l?l.p(y,R):(l=m[o]=B[o](y),l.c()),f(l,1),l.m(i,null));const O={};513&R&&(O.$$scope={dirty:R,ctx:y}),u.$set(O),(!x||1&R&&d!==(d=y[0].displayName||y[0].name))&&C(c,"title",d),(!x||1&R)&&Ae(c,"full-path",y[0].displayName),(!x||5&R&&p!==(p=y[0].path===y[2]))&&C(t,"aria-selected",p),(!x||1&R&&w!==(w=y[0].isFile?void 0:y[0].isExpanded))&&C(t,"aria-expanded",w),(!x||5&R)&&Ae(t,"selected",y[0].path===y[2]),(!x||1&R)&&Ae(t,"collapsed-folder",y[0].displayName&&!y[0].isFile),!y[0].isFile&&y[0].isExpanded&&y[0].children.size>0?F?(F.p(y,R),1&R&&f(F,1)):(F=Dn(y),F.c(),f(F,1),F.m(e,null)):F&&(H(),$(F,1,1,()=>{F=null}),j())},i(y){x||(f(l),f(u.$$.fragment,y),f(F),x=!0)},o(y){$(l),$(u.$$.fragment,y),$(F),x=!1},d(y){y&&h(e),m[o].d(),M(u),F&&F.d(),E=!1,Us(_)}}}const Cn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function jr(r,e,t){let n,{node:s}=e,{indentLevel:i=0}=e;const o=ai();function l(){s.isFile?o.set(s.path):t(0,s.isExpanded=!s.isExpanded,s)}return Re(r,o,a=>t(2,n=a)),r.$$set=a=>{"node"in a&&t(0,s=a.node),"indentLevel"in a&&t(1,i=a.indentLevel)},[s,i,n,o,l,a=>a.key==="Enter"&&l()]}class ci extends te{constructor(e){super(),ne(this,e,jr,Hr,K,{node:0,indentLevel:1})}}function xn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Wr(r){let e,t,n=pe(Array.from(r[1].children.values()).sort(kn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=wn(xn(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=we()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(2&l){let a;for(n=pe(Array.from(o[1].children.values()).sort(kn)),a=0;a<n.length;a+=1){const c=xn(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=wn(c),s[a].c(),f(s[a],1),s[a].m(e.parentNode,e))}for(H(),a=n.length;a<s.length;a+=1)i(a);j()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Me(s,o)}}}function Qr(r){let e,t,n;return t=new he({props:{size:1,color:"neutral",$$slots:{default:[Jr]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","tree-view__empty svelte-1tnd9l7")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Gr(r){let e;return{c(){e=A("div"),e.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',C(e,"class","tree-view__loading svelte-1tnd9l7")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&h(e)}}}function wn(r){let e,t;return e=new ci({props:{node:r[4],indentLevel:0}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};2&s&&(i.node=n[4]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Jr(r){let e;return{c(){e=N("No changed files")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Yr(r){let e,t,n,s,i;const o=[Gr,Qr,Wr],l=[];function a(c,u){return c[0]?0:c[1].children.size===0?1:2}return n=a(r),s=l[n]=o[n](r),{c(){e=A("div"),t=A("div"),s.c(),C(t,"class","tree-view__content svelte-1tnd9l7"),C(t,"role","tree"),C(t,"aria-label","Changed Files"),C(e,"class","tree-view svelte-1tnd9l7")},m(c,u){g(c,e,u),T(e,t),l[n].m(t,null),i=!0},p(c,[u]){let d=n;n=a(c),n===d?l[n].p(c,u):(H(),$(l[d],1,1,()=>{l[d]=null}),j(),s=l[n],s?s.p(c,u):(s=l[n]=o[n](c),s.c()),f(s,1),s.m(t,null))},i(c){i||(f(s),i=!0)},o(c){$(s),i=!1},d(c){c&&h(e),l[n].d()}}}function Ut(r,e=!1){if(r.isFile)return;let t="";e&&(t=function(o){let l=o.path.split("/"),a=o;for(;;){const c=Array.from(a.children.values()).filter(d=>!d.isFile),u=Array.from(a.children.values()).filter(d=>d.isFile);if(c.length!==1||u.length!==0)break;a=c[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)Ut(o);const s=Array.from(r.children.values()).filter(o=>!o.isFile),i=Array.from(r.children.values()).filter(o=>o.isFile);if(s.length===1&&i.length===0){const o=s[0],l=o.name;if(e){r.displayName=t||`${r.name}/${l}`;for(const[a,c]of o.children.entries()){const u=`${a}`;r.children.set(u,c)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,c]of o.children.entries()){const u=`${l}/${a}`;r.children.set(u,c)}r.children.delete(l)}}}const kn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Xr(r,e,t){let n,{changedFiles:s=[]}=e,{isLoading:i=!1}=e;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(c=>{const u=c.change_type===Ii.deleted?c.old_path:c.new_path;u&&function(d,p){const w=p.split("/");let k=d;for(let x=0;x<w.length;x++){const E=w[x],_=x===w.length-1,B=w.slice(0,x+1).join("/");k.children.has(E)||k.children.set(E,{name:E,path:B,isFile:_,children:new Map,isExpanded:!0}),k=k.children.get(E)}}(a,u)}),function(c){if(!c.isFile)if(c.path!=="")Ut(c);else{const u=Array.from(c.children.values()).filter(d=>!d.isFile);for(const d of u)Ut(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&t(2,s=l.changedFiles),"isLoading"in l&&t(0,i=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o(s))},[i,n,s]}class ui extends te{constructor(e){super(),ne(this,e,Xr,Yr,K,{changedFiles:2,isLoading:0})}}function yn(r,e,t){const n=r.slice();return n[19]=e[t],n}function Kr(r){let e;return{c(){e=N("Changed files")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function eo(r){let e,t,n;return t=new he({props:{size:1,color:"neutral",$$slots:{default:[no]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};4194304&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function to(r){let e,t,n,s,i,o,l=[],a=new Map,c=r[9].length>0&&vn(r),u=pe(r[9]);const d=p=>p[19].qualifiedPathName.relPath;for(let p=0;p<u.length;p+=1){let w=yn(r,u,p),k=d(w);a.set(k,l[p]=An(k,w))}return{c(){e=A("div"),t=A("div"),c&&c.c(),n=I(),s=A("div"),i=A("div");for(let p=0;p<l.length;p+=1)l[p].c();C(t,"class","c-edits-list-controls svelte-6iqvaj"),C(e,"class","c-edits-list-header svelte-6iqvaj"),C(i,"class","c-edits-section svelte-6iqvaj"),C(s,"class","c-edits-list svelte-6iqvaj")},m(p,w){g(p,e,w),T(e,t),c&&c.m(t,null),g(p,n,w),g(p,s,w),T(s,i);for(let k=0;k<l.length;k+=1)l[k]&&l[k].m(i,null);o=!0},p(p,w){p[9].length>0?c?(c.p(p,w),512&w&&f(c,1)):(c=vn(p),c.c(),f(c,1),c.m(t,null)):c&&(H(),$(c,1,1,()=>{c=null}),j()),2654&w&&(u=pe(p[9]),H(),l=Hs(l,w,d,1,p,u,a,i,js,An,null,yn),j())},i(p){if(!o){f(c);for(let w=0;w<u.length;w+=1)f(l[w]);o=!0}},o(p){$(c);for(let w=0;w<l.length;w+=1)$(l[w]);o=!1},d(p){p&&(h(e),h(n),h(s)),c&&c.d();for(let w=0;w<l.length;w+=1)l[w].d()}}}function no(r){let e;return{c(){e=N("No changes to show")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function vn(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[7]||r[8]||r[3].length>0||!r[10],$$slots:{default:[oo]},$$scope:{ctx:r}}}),e.$on("click",r[12]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1416&s&&(i.disabled=n[7]||n[8]||n[3].length>0||!n[10]),4194688&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function so(r){let e;return{c(){e=N("Apply all")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function io(r){let e;return{c(){e=N("All applied")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function ro(r){let e;return{c(){e=N("Applying...")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function oo(r){let e,t,n,s;function i(a,c){return a[7]?ro:a[8]?io:so}let o=i(r),l=o(r);return n=new ft({}),{c(){l.c(),e=I(),t=A("div"),z(n.$$.fragment),C(t,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,c){l.m(a,c),g(a,e,c),g(a,t,c),L(n,t,null),s=!0},p(a,c){o!==(o=i(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(e.parentNode,e)))},i(a){s||(f(n.$$.fragment,a),s=!0)},o(a){$(n.$$.fragment,a),s=!1},d(a){a&&(h(e),h(t)),l.d(a),M(n)}}}function An(r,e){let t,n,s,i,o;function l(...u){return e[16](e[19],...u)}function a(){return e[17](e[19])}function c(){return e[18](e[19])}return n=new Ir({props:{path:e[19].qualifiedPathName.relPath,change:e[19].diff,isApplying:e[3].includes(e[19].qualifiedPathName.relPath),hasApplied:e[4].includes(e[19].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,onOpenFile:e[2]?c:void 0,isExpandedDefault:!0}}),{key:r,first:null,c(){t=A("div"),z(n.$$.fragment),s=I(),C(t,"class",""),this.first=t},m(u,d){g(u,t,d),L(n,t,null),T(t,s),o=!0},p(u,d){e=u;const p={};512&d&&(p.path=e[19].qualifiedPathName.relPath),512&d&&(p.change=e[19].diff),520&d&&(p.isApplying=e[3].includes(e[19].qualifiedPathName.relPath)),528&d&&(p.hasApplied=e[4].includes(e[19].qualifiedPathName.relPath)),512&d&&(p.onCodeChange=l),578&d&&(p.onApplyChanges=a),516&d&&(p.onOpenFile=e[2]?c:void 0),n.$set(p)},i(u){o||(f(n.$$.fragment,u),u&&yi(()=>{o&&(i||(i=nn(t,on,{},!0)),i.run(1))}),o=!0)},o(u){$(n.$$.fragment,u),u&&(i||(i=nn(t,on,{},!1)),i.run(0)),o=!1},d(u){u&&h(t),M(n),u&&i&&i.end()}}}function lo(r){let e,t,n,s,i,o,l,a,c,u,d,p;i=new he({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[Kr]},$$scope:{ctx:r}}}),l=new ui({props:{changedFiles:r[0],isLoading:r[5]}});const w=[to,eo],k=[];function x(E,_){return E[9].length>0?0:1}return u=x(r),d=k[u]=w[u](r),{c(){e=A("div"),t=A("div"),n=A("div"),s=A("div"),z(i.$$.fragment),o=I(),z(l.$$.fragment),a=I(),c=A("div"),d.c(),C(s,"class","c-file-explorer__tree__header svelte-6iqvaj"),C(n,"class","c-file-explorer__tree svelte-6iqvaj"),C(c,"class","c-file-explorer__details svelte-6iqvaj"),C(t,"class","c-file-explorer__layout svelte-6iqvaj"),C(e,"class","c-edits-list-container svelte-6iqvaj")},m(E,_){g(E,e,_),T(e,t),T(t,n),T(n,s),L(i,s,null),T(s,o),L(l,s,null),T(t,a),T(t,c),k[u].m(c,null),p=!0},p(E,[_]){const B={};4194304&_&&(B.$$scope={dirty:_,ctx:E}),i.$set(B);const m={};1&_&&(m.changedFiles=E[0]),32&_&&(m.isLoading=E[5]),l.$set(m);let D=u;u=x(E),u===D?k[u].p(E,_):(H(),$(k[D],1,1,()=>{k[D]=null}),j(),d=k[u],d?d.p(E,_):(d=k[u]=w[u](E),d.c()),f(d,1),d.m(c,null))},i(E){p||(f(i.$$.fragment,E),f(l.$$.fragment,E),f(d),p=!0)},o(E){$(i.$$.fragment,E),$(l.$$.fragment,E),$(d),p=!1},d(E){E&&h(e),M(i),M(l),k[u].d()}}}function ao(r,e,t){let n,s,i,o,l,{changedFiles:a}=e,{onApplyChanges:c}=e,{onOpenFile:u}=e,{pendingFiles:d=[]}=e,{appliedFiles:p=[]}=e,{isLoadingTreeView:w=!1}=e,k={},x=!1,E=!1;function _(B,m){t(6,k[B]=m,k)}return r.$$set=B=>{"changedFiles"in B&&t(0,a=B.changedFiles),"onApplyChanges"in B&&t(1,c=B.onApplyChanges),"onOpenFile"in B&&t(2,u=B.onOpenFile),"pendingFiles"in B&&t(3,d=B.pendingFiles),"appliedFiles"in B&&t(4,p=B.appliedFiles),"isLoadingTreeView"in B&&t(5,w=B.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&t(15,n=JSON.stringify(a)),16&r.$$.dirty&&t(13,s=JSON.stringify(p)),8&r.$$.dirty&&t(14,i=JSON.stringify(d)),32768&r.$$.dirty&&n&&(t(6,k={}),t(7,x=!1),t(8,E=!1)),65&r.$$.dirty&&t(9,l=a.map(B=>{const m=B.new_path||B.old_path,D=B.old_contents||"",F=B.new_contents||"",y=ji.generateDiff(B.old_path,B.new_path,D,F),R=function(q,O){const V=vt("oldFile","newFile",q,O,"","",{context:3}),W=zi(V);let ee=0,de=0,ce=[];for(const ae of W)for(const J of ae.hunks)for(const $e of J.lines){const De=$e.startsWith("+"),ie=$e.startsWith("-");De&&ee++,ie&&de++,ce.push({value:$e,added:De,removed:ie})}return{totalAddedLines:ee,totalRemovedLines:de,changes:ce,diff:V}}(D,F);return k[m]||t(6,k[m]=F,k),{qualifiedPathName:{rootPath:"",relPath:m},lineChanges:R,oldContents:D,newContents:F,diff:y}})),57880&r.$$.dirty&&t(10,o=(()=>{if(n&&s&&i){const B=l.map(m=>m.qualifiedPathName.relPath);return B.length!==0&&B.some(m=>!p.includes(m)&&!d.includes(m))}return!1})()),664&r.$$.dirty&&x){const B=l.map(m=>m.qualifiedPathName.relPath);B.filter(m=>!p.includes(m)&&!d.includes(m)).length===0&&B.every(m=>p.includes(m)||d.includes(m))&&d.length===0&&p.length>0&&(t(7,x=!1),t(8,E=!0))}if(9104&r.$$.dirty&&l.length>0&&!x&&s){const B=l.map(m=>m.qualifiedPathName.relPath);if(B.length>0){const m=B.every(D=>p.includes(D));m&&p.length>0?t(8,E=!0):!m&&E&&t(8,E=!1)}}},[a,c,u,d,p,w,k,x,E,l,o,_,function(){if(!c)return;const B=l.map(D=>D.qualifiedPathName.relPath);if(B.every(D=>p.includes(D)))return void t(8,E=!0);const m=B.filter(D=>!p.includes(D)&&!d.includes(D));m.length!==0&&(t(7,x=!0),t(8,E=!1),m.forEach(D=>{const F=l.find(y=>y.qualifiedPathName.relPath===D);if(F){const y=k[D]||F.newContents;c(D,F.oldContents,y)}}))},s,i,n,(B,m)=>{_(B.qualifiedPathName.relPath,m)},B=>{const m=k[B.qualifiedPathName.relPath]||B.newContents;c(B.qualifiedPathName.relPath,B.oldContents,m)},B=>u(B.qualifiedPathName.relPath)]}class co extends te{constructor(e){super(),ne(this,e,ao,lo,K,{changedFiles:0,onApplyChanges:1,onOpenFile:2,pendingFiles:3,appliedFiles:4,isLoadingTreeView:5})}}function bn(r,e,t){const n=r.slice();return n[3]=e[t],n}function En(r){let e,t=pe(r[1].paths),n=[];for(let s=0;s<t.length;s+=1)n[s]=_n(bn(r,t,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();e=we()},m(s,i){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(s,i);g(s,e,i)},p(s,i){if(2&i){let o;for(t=pe(s[1].paths),o=0;o<t.length;o+=1){const l=bn(s,t,o);n[o]?n[o].p(l,i):(n[o]=_n(l),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(s){s&&h(e),Me(n,s)}}}function _n(r){let e,t;return{c(){e=kt("path"),C(e,"d",t=r[3]),C(e,"fill-rule","evenodd"),C(e,"clip-rule","evenodd")},m(n,s){g(n,e,s)},p(n,s){2&s&&t!==(t=n[3])&&C(e,"d",t)},d(n){n&&h(e)}}}function uo(r){let e,t=r[1]&&En(r);return{c(){e=kt("svg"),t&&t.c(),C(e,"width","14"),C(e,"viewBox","0 0 20 20"),C(e,"fill","currentColor"),C(e,"class","svelte-10h4f31")},m(n,s){g(n,e,s),t&&t.m(e,null)},p(n,s){n[1]?t?t.p(n,s):(t=En(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&h(e),t&&t.d()}}}function po(r){let e,t;return e=new Ve({props:{content:`This is a ${r[0]} change`,triggerOn:[st.Hover],$$slots:{default:[uo]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.content=`This is a ${n[0]} change`),66&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function fo(r,e,t){let n,{type:s}=e;const i={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&t(0,s=o.type)},r.$$.update=()=>{1&r.$$.dirty&&t(1,n=i[s]??i.other)},[s,n]}class $o extends te{constructor(e){super(),ne(this,e,fo,po,K,{type:0})}}function Bn(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function zn(r){let e,t,n,s,i;t=new pt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[mo]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let c=0;c<o.length;c+=1)l[c]=Ln(Bn(r,o,c));const a=c=>$(l[c],1,1,()=>{l[c]=null});return{c(){e=A("div"),z(t.$$.fragment),n=I(),s=A("div");for(let c=0;c<l.length;c+=1)l[c].c();C(e,"class","toggle-button svelte-14s1ghg"),C(s,"class","descriptions svelte-14s1ghg"),Ee(s,"transform","translateY("+-r[4]+"px)")},m(c,u){g(c,e,u),L(t,e,null),g(c,n,u),g(c,s,u);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(c,u){const d={};if(1&u[0]|524288&u[1]&&(d.$$scope={dirty:u,ctx:c}),t.$set(d),546&u[0]){let p;for(o=pe(c[1]),p=0;p<o.length;p+=1){const w=Bn(c,o,p);l[p]?(l[p].p(w,u),f(l[p],1)):(l[p]=Ln(w),l[p].c(),f(l[p],1),l[p].m(s,null))}for(H(),p=o.length;p<l.length;p+=1)a(p);j()}(!i||16&u[0])&&Ee(s,"transform","translateY("+-c[4]+"px)")},i(c){if(!i){f(t.$$.fragment,c);for(let u=0;u<o.length;u+=1)f(l[u]);i=!0}},o(c){$(t.$$.fragment,c),l=l.filter(Boolean);for(let u=0;u<l.length;u+=1)$(l[u]);i=!1},d(c){c&&(h(e),h(n),h(s)),M(t),Me(l,c)}}}function go(r){let e,t;return e=new $t({props:{icon:"book"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function ho(r){let e,t;return e=new $t({props:{icon:"x"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function mo(r){let e,t,n,s;const i=[ho,go],o=[];function l(a,c){return a[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e!==u&&(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function Ln(r){let e,t,n,s;return t=new oi({props:{markdown:r[47].text}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),C(e,"class","description svelte-14s1ghg"),Ee(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),Ee(e,"--user-theme-sidebar-background","transparent")},m(i,o){g(i,e,o),L(t,e,null),T(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[47].text),t.$set(l),(!s||34&o[0])&&Ee(e,"top",(i[5][i[49]]||i[9](i[47]))+"px")},i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&h(e),M(t)}}}function Do(r){let e,t,n,s,i=r[1].length>0&&zn(r);return{c(){e=A("div"),t=A("div"),n=I(),i&&i.c(),C(t,"class","editor-container svelte-14s1ghg"),Ee(t,"height",r[3]+"px"),C(e,"class","monaco-diff-container svelte-14s1ghg"),Ae(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),T(e,t),r[23](t),T(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&Ee(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&f(i,1)):(i=zn(o),i.c(),f(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),j()),(!s||3&l[0])&&Ae(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(f(i),s=!0)},o(o){$(i),s=!1},d(o){o&&h(e),r[23](null),i&&i.d()}}}function Fo(r,e,t){let n,s,i;const o=Ss();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:c}=e,{descriptions:u=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:w=[]}=e,{theme:k}=e,{areDescriptionsVisible:x=!0}=e,{isNewFile:E=!1}=e,{isDeletedFile:_=!1}=e;const B=jt.getContext().monaco;let m,D,F,y;Re(r,B,v=>t(22,n=v));let R,q=[];const O=Wt();let V,W=Ye(0);Re(r,W,v=>t(4,s=v));let ee=E?20*a.split(`
`).length+40:100;const de=n?n.languages.getLanguages().map(v=>v.id):[];function ce(v,S){var b,U;if(S){const Z=(b=S.split(".").pop())==null?void 0:b.toLowerCase();if(Z){const Q=(U=n==null?void 0:n.languages.getLanguages().find(se=>{var G;return(G=se.extensions)==null?void 0:G.includes("."+Z)}))==null?void 0:U.id;if(Q&&de.includes(Q))return Q}}return"plaintext"}const ae=Ye({});Re(r,ae,v=>t(5,i=v));let J=null;function $e(){if(!m)return;q=q.filter(b=>(b.dispose(),!1));const v=m.getOriginalEditor(),S=m.getModifiedEditor();q.push(v.onDidScrollChange(()=>{wt(W,s=v.getScrollTop(),s)}),S.onDidScrollChange(()=>{wt(W,s=S.getScrollTop(),s)}))}function De(){if(!m||!R)return;const v=m.getOriginalEditor(),S=m.getModifiedEditor();q.push(S.onDidContentSizeChange(()=>O.requestLayout()),v.onDidContentSizeChange(()=>O.requestLayout()),m.onDidUpdateDiff(()=>O.requestLayout()),S.onDidChangeHiddenAreas(()=>O.requestLayout()),v.onDidChangeHiddenAreas(()=>O.requestLayout()),S.onDidLayoutChange(()=>O.requestLayout()),v.onDidLayoutChange(()=>O.requestLayout()),S.onDidFocusEditorWidget(()=>{ye(!0)}),v.onDidFocusEditorWidget(()=>{ye(!0)}),S.onDidBlurEditorWidget(()=>{ye(!1)}),v.onDidBlurEditorWidget(()=>{ye(!1)}),S.onDidChangeModelContent(()=>{xe=!0,Ne=Date.now();const b=(y==null?void 0:y.getValue())||"";if(b===a)return;const U=b.replace(p.join(""),"").replace(w.join(""),"");o("codeChange",{modifiedCode:U});const Z=setTimeout(()=>{xe=!1},500);q.push({dispose:()=>clearTimeout(Z)})})),function(){!R||!m||(J&&clearTimeout(J),J=setTimeout(()=>{if(!R.__hasClickListener){const b=U=>{const Z=U.target;Z&&(Z.closest('[title="Show Unchanged Region"]')||Z.closest('[title="Hide Unchanged Region"]'))&&ve()};R.addEventListener("click",b),t(2,R.__hasClickListener=!0,R),q.push({dispose:()=>{R.removeEventListener("click",b)}})}m&&q.push(m.onDidUpdateDiff(()=>{ve()}))},300))}()}Ps(()=>{m==null||m.dispose(),D==null||D.dispose(),F==null||F.dispose(),y==null||y.dispose(),q.forEach(v=>v.dispose()),J&&clearTimeout(J),V==null||V()});let ie=null;function ve(){ie&&clearTimeout(ie),ie=setTimeout(()=>{O.requestLayout(),ie=null},100),ie&&q.push({dispose:()=>{ie&&(clearTimeout(ie),ie=null)}})}function ke(v,S,b,U=[],Z=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");F==null||F.dispose(),y==null||y.dispose(),S=S||"",b=b||"";const Q=U.join(""),se=Z.join("");if(S=E?b.split(`
`).map(()=>" ").join(`
`):Q+S+se,b=Q+b+se,F=n.editor.createModel(S,void 0,v!==void 0?n.Uri.parse("file://"+v+`#${crypto.randomUUID()}`):void 0),_&&(b=b.split(`
`).map(()=>" ").join(`
`)),t(21,y=n.editor.createModel(b,void 0,v!==void 0?n.Uri.parse("file://"+v+`#${crypto.randomUUID()}`):void 0)),m){m.setModel({original:F,modified:y});const G=m.getOriginalEditor();G&&G.updateOptions({lineNumbers:"off"}),$e(),J&&clearTimeout(J),J=setTimeout(()=>{De(),J=null},300)}}ot(()=>{if(n)if(E){t(20,D=n.editor.create(R,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:k,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:U=>`${d-p.length+U}`}));const v=ce(0,c);t(21,y=n.editor.createModel(a,v,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),D.setModel(y),q.push(D.onDidChangeModelContent(()=>{xe=!0,Ne=Date.now();const U=(y==null?void 0:y.getValue())||"";if(U===a)return;o("codeChange",{modifiedCode:U});const Z=setTimeout(()=>{xe=!1},500);q.push({dispose:()=>clearTimeout(Z)})})),q.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const S=D.getContentHeight();t(3,ee=Math.max(S,60));const b=setTimeout(()=>{D==null||D.layout()},0);q.push({dispose:()=>clearTimeout(b)})}else t(19,m=n.editor.createDiffEditor(R,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:k,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:v=>`${d-p.length+v}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),V&&V(),V=O.registerEditor({editor:m,updateHeight:be,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ke(c,l,a,p,w),$e(),De(),J&&clearTimeout(J),J=setTimeout(()=>{O.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let xe=!1,Ne=0;function fe(v,S=!0){return m?(S?m.getModifiedEditor():m.getOriginalEditor()).getTopForLineNumber(v):18*v}function be(){if(!m)return;const v=m.getModel(),S=v==null?void 0:v.original,b=v==null?void 0:v.modified;if(!S||!b)return;const U=m.getOriginalEditor(),Z=m.getModifiedEditor(),Q=m.getLineChanges()||[];let se;if(Q.length===0){const G=U.getContentHeight(),re=Z.getContentHeight();se=Math.max(100,G,re)}else{let G=0,re=0;for(const ue of Q)ue.originalEndLineNumber>0&&(G=Math.max(G,ue.originalEndLineNumber)),ue.modifiedEndLineNumber>0&&(re=Math.max(re,ue.modifiedEndLineNumber));G=Math.min(G+3,S.getLineCount()),re=Math.min(re+3,b.getLineCount());const Y=U.getTopForLineNumber(G),ge=Z.getTopForLineNumber(re);se=Math.max(Y,ge)+60}t(3,ee=Math.min(se,2e4)),m.layout(),Ge()}function ye(v){if(!m)return;const S=m.getOriginalEditor(),b=m.getModifiedEditor();S.updateOptions({scrollbar:{handleMouseWheel:v}}),b.updateOptions({scrollbar:{handleMouseWheel:v}})}function P(v){if(!m)return D?D.getTopForLineNumber(v.range.start+1):0;const S=m.getModel(),b=S==null?void 0:S.original,U=S==null?void 0:S.modified;if(!b||!U)return 0;const Z=fe(v.range.start+1,!1),Q=fe(v.range.start+1,!0);return Z&&!Q?Z:!Z&&Q?Q:Math.min(Z,Q)}function Ge(){if(!m&&!D||u.length===0)return;const v={};u.forEach((S,b)=>{v[b]=P(S)}),ae.set(v)}return r.$$set=v=>{"originalCode"in v&&t(10,l=v.originalCode),"modifiedCode"in v&&t(11,a=v.modifiedCode),"path"in v&&t(12,c=v.path),"descriptions"in v&&t(1,u=v.descriptions),"lineOffset"in v&&t(13,d=v.lineOffset),"extraPrefixLines"in v&&t(14,p=v.extraPrefixLines),"extraSuffixLines"in v&&t(15,w=v.extraSuffixLines),"theme"in v&&t(16,k=v.theme),"areDescriptionsVisible"in v&&t(0,x=v.areDescriptionsVisible),"isNewFile"in v&&t(17,E=v.isNewFile),"isDeletedFile"in v&&t(18,_=v.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(v=a,!(xe||Date.now()-Ne<1e3||y&&y.getValue()===p.join("")+v+w.join(""))))if(E&&D){if(y)y.setValue(a);else{const S=ce(0,c);n&&t(21,y=n.editor.createModel(a,S,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),y&&D.setModel(y)}t(3,ee=20*a.split(`
`).length+40),D.layout()}else!E&&m&&(ke(c,l,a,p,w),O.requestLayout());var v;if(1572866&r.$$.dirty[0]&&(m||D)&&u.length>0&&Ge(),1181696&r.$$.dirty[0]&&E&&a&&D){const S=D.getContentHeight();t(3,ee=Math.max(S,60)),D.layout()}},[x,u,R,ee,s,i,B,W,ae,P,l,a,c,d,p,w,k,E,_,m,D,y,n,function(v){Te[v?"unshift":"push"](()=>{R=v,t(2,R)})},()=>t(0,x=!x)]}class Co extends te{constructor(e){super(),ne(this,e,Fo,Do,K,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}}const xo=Symbol("focusedPath");function Mn(r){return`file-diff-${He(r)}`}function wo(r){let e,t,n;function s(o){r[41](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),e=new Co({props:i}),Te.push(()=>Ke(e,"areDescriptionsVisible",s)),e.$on("codeChange",r[26]),{c(){z(e.$$.fragment)},m(o,l){L(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],et(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){$(e.$$.fragment,o),n=!1},d(o){M(e,o)}}}function ko(r){let e,t,n;return t=new he({props:{size:1,$$slots:{default:[Ao]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","too-large-message svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};5888&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function yo(r){let e,t,n;return t=new he({props:{$$slots:{default:[Bo]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","binary-file-message svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};2101632&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function vo(r){let e,t,n,s;const i=[Lo,zo],o=[];function l(a,c){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=i[t](r)),{c(){e=A("div"),n&&n.c(),C(e,"class","image-container svelte-1536g7w")},m(a,c){g(a,e,c),~t&&o[t].m(e,null),s=!0},p(a,c){let u=t;t=l(a),t===u?~t&&o[t].p(a,c):(n&&(H(),$(o[u],1,1,()=>{o[u]=null}),j()),~t?(n=o[t],n?n.p(a,c):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){s||(f(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),~t&&o[t].d()}}}function Ao(r){let e,t,n,s,i,o,l,a=le(r[12])+"",c=(r[8]?r[10]:r[9])+"";return{c(){e=N('File "'),t=N(a),n=N('" is too large to display a diff (size: '),s=N(c),i=N(" bytes, max: "),o=N(ti),l=N(" bytes).")},m(u,d){g(u,e,d),g(u,t,d),g(u,n,d),g(u,s,d),g(u,i,d),g(u,o,d),g(u,l,d)},p(u,d){4096&d[0]&&a!==(a=le(u[12])+"")&&oe(t,a),1792&d[0]&&c!==(c=(u[8]?u[10]:u[9])+"")&&oe(s,c)},d(u){u&&(h(e),h(t),h(n),h(s),h(i),h(o),h(l))}}}function bo(r){let e,t,n,s=le(r[12])+"";return{c(){e=N("Binary file modified: "),t=N(s),n=N(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){4096&o[0]&&s!==(s=le(i[12])+"")&&oe(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function Eo(r){let e,t,n,s=le(r[12])+"";return{c(){e=N("Binary file deleted: "),t=N(s),n=N(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){4096&o[0]&&s!==(s=le(i[12])+"")&&oe(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function _o(r){let e,t,n,s=le(r[12])+"";return{c(){e=N("Binary file added: "),t=N(s),n=N(".")},m(i,o){g(i,e,o),g(i,t,o),g(i,n,o)},p(i,o){4096&o[0]&&s!==(s=le(i[12])+"")&&oe(t,s)},d(i){i&&(h(e),h(t),h(n))}}}function Bo(r){let e;function t(i,o){return i[21]||i[7]?_o:i[8]?Eo:bo}let n=t(r),s=n(r);return{c(){s.c(),e=N(`
            No text preview available.`)},m(i,o){s.m(i,o),g(i,e,o)},p(i,o){n===(n=t(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},d(i){i&&h(e),s.d(i)}}}function zo(r){let e,t,n,s,i,o,l,a;e=new he({props:{class:"image-info-text",$$slots:{default:[To]},$$scope:{ctx:r}}});let c=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&Rn(r);return{c(){z(e.$$.fragment),t=I(),n=A("img"),o=I(),c&&c.c(),l=we(),Oe(n.src,s="data:"+r[19]+";base64,"+btoa(r[6]))||C(n,"src",s),C(n,"alt",i="Current "+le(r[12])),C(n,"class","image-preview svelte-1536g7w")},m(u,d){L(e,u,d),g(u,t,d),g(u,n,d),g(u,o,d),c&&c.m(u,d),g(u,l,d),a=!0},p(u,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:u}),e.$set(p),(!a||524352&d[0]&&!Oe(n.src,s="data:"+u[19]+";base64,"+btoa(u[6])))&&C(n,"src",s),(!a||4096&d[0]&&i!==(i="Current "+le(u[12])))&&C(n,"alt",i),u[0].originalCode&&u[6]!==u[0].originalCode&&!u[21]?c?(c.p(u,d),2097217&d[0]&&f(c,1)):(c=Rn(u),c.c(),f(c,1),c.m(l.parentNode,l)):c&&(H(),$(c,1,1,()=>{c=null}),j())},i(u){a||(f(e.$$.fragment,u),f(c),a=!0)},o(u){$(e.$$.fragment,u),$(c),a=!1},d(u){u&&(h(t),h(n),h(o),h(l)),M(e,u),c&&c.d(u)}}}function Lo(r){let e,t,n,s;e=new he({props:{class:"image-info-text",$$slots:{default:[qo]},$$scope:{ctx:r}}});let i=r[0].originalCode&&Tn(r);return{c(){z(e.$$.fragment),t=I(),i&&i.c(),n=we()},m(o,l){L(e,o,l),g(o,t,l),i&&i.m(o,l),g(o,n,l),s=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&f(i,1)):(i=Tn(o),i.c(),f(i,1),i.m(n.parentNode,n)):i&&(H(),$(i,1,1,()=>{i=null}),j())},i(o){s||(f(e.$$.fragment,o),f(i),s=!0)},o(o){$(e.$$.fragment,o),$(i),s=!1},d(o){o&&(h(t),h(n)),M(e,o),i&&i.d(o)}}}function Mo(r){let e;return{c(){e=N("Image modified")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Ro(r){let e;return{c(){e=N("New image added")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function To(r){let e,t,n=le(r[12])+"";function s(l,a){return l[21]||l[7]?Ro:Mo}let i=s(r),o=i(r);return{c(){o.c(),e=N(": "),t=N(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=le(l[12])+"")&&oe(t,n)},d(l){l&&(h(e),h(t)),o.d(l)}}}function Rn(r){let e,t,n,s,i,o;return e=new he({props:{class:"image-info-text",$$slots:{default:[No]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=I(),n=A("img"),Oe(n.src,s="data:"+We(r[3])+";base64,"+btoa(r[0].originalCode))||C(n,"src",s),C(n,"alt",i="Original "+le(r[12])),C(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){L(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),e.$set(c),(!o||9&a[0]&&!Oe(n.src,s="data:"+We(l[3])+";base64,"+btoa(l[0].originalCode)))&&C(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+le(l[12])))&&C(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),M(e,l)}}}function No(r){let e;return{c(){e=N("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function qo(r){let e,t,n=le(r[12])+"";return{c(){e=N("Image deleted: "),t=N(n)},m(s,i){g(s,e,i),g(s,t,i)},p(s,i){4096&i[0]&&n!==(n=le(s[12])+"")&&oe(t,n)},d(s){s&&(h(e),h(t))}}}function Tn(r){let e,t,n,s,i,o;return e=new he({props:{class:"image-info-text",$$slots:{default:[Oo]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=I(),n=A("img"),Oe(n.src,s="data:"+We(r[3])+";base64,"+btoa(r[0].originalCode))||C(n,"src",s),C(n,"alt",i="Original "+le(r[12])),C(n,"class","image-preview svelte-1536g7w")},m(l,a){L(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),e.$set(c),(!o||9&a[0]&&!Oe(n.src,s="data:"+We(l[3])+";base64,"+btoa(l[0].originalCode)))&&C(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+le(l[12])))&&C(n,"alt",i)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){$(e.$$.fragment,l),o=!1},d(l){l&&(h(t),h(n)),M(e,l)}}}function Oo(r){let e;return{c(){e=N("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function So(r){let e,t,n,s;const i=[vo,yo,ko,wo],o=[];function l(a,c){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=i[t](r),{c(){e=A("div"),n.c(),C(e,"class","changes svelte-1536g7w")},m(a,c){g(a,e,c),o[t].m(e,null),s=!0},p(a,c){let u=t;t=l(a),t===u?o[t].p(a,c):(H(),$(o[u],1,1,()=>{o[u]=null}),j(),n=o[t],n?n.p(a,c):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null))},i(a){s||(f(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),o[t].d()}}}function Po(r){let e,t=le(r[12])+"";return{c(){e=N(t)},m(n,s){g(n,e,s)},p(n,s){4096&s[0]&&t!==(t=le(n[12])+"")&&oe(e,t)},d(n){n&&h(e)}}}function Io(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[Po]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};4096&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Nn(r){let e,t,n=tt(r[12])+"";return{c(){e=A("span"),t=N(n),C(e,"class","c-directory svelte-1536g7w")},m(s,i){g(s,e,i),T(e,t)},p(s,i){4096&i[0]&&n!==(n=tt(s[12])+"")&&oe(t,n)},d(s){s&&h(e)}}}function Uo(r){let e,t,n,s=r[23]>0&&qn(r),i=r[22]>0&&On(r);return{c(){e=A("div"),s&&s.c(),t=I(),i&&i.c(),C(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),s&&s.m(e,null),T(e,t),i&&i.m(e,null),n=!0},p(o,l){o[23]>0?s?(s.p(o,l),8388608&l[0]&&f(s,1)):(s=qn(o),s.c(),f(s,1),s.m(e,t)):s&&(H(),$(s,1,1,()=>{s=null}),j()),o[22]>0?i?(i.p(o,l),4194304&l[0]&&f(i,1)):(i=On(o),i.c(),f(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),j())},i(o){n||(f(s),f(i),n=!0)},o(o){$(s),$(i),n=!1},d(o){o&&h(e),s&&s.d(),i&&i.d()}}}function Zo(r){let e;return{c(){e=A("span"),e.textContent="New File",C(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&h(e)}}}function qn(r){let e,t,n;return t=new he({props:{size:1,$$slots:{default:[Vo]},$$scope:{ctx:r}}}),{c(){e=A("span"),z(t.$$.fragment),C(e,"class","additions svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};8388608&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Vo(r){let e,t;return{c(){e=N("+"),t=N(r[23])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){8388608&s[0]&&oe(t,n[23])},d(n){n&&(h(e),h(t))}}}function On(r){let e,t,n;return t=new he({props:{size:1,$$slots:{default:[Ho]},$$scope:{ctx:r}}}),{c(){e=A("span"),z(t.$$.fragment),C(e,"class","deletions svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};4194304&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Ho(r){let e,t;return{c(){e=N("-"),t=N(r[22])},m(n,s){g(n,e,s),g(n,t,s)},p(n,s){4194304&s[0]&&oe(t,n[22])},d(n){n&&(h(e),h(t))}}}function jo(r){let e;return{c(){e=N("Apply")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Wo(r){let e;return{c(){e=N("Applied")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Qo(r){let e,t,n;return t=new ft({}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","applied__icon svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Go(r){let e,t,n;return t=new Lt({props:{iconName:"check"}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","applied svelte-1536g7w")},m(s,i){g(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Jo(r){let e,t,n,s,i;function o(p,w){return p[5]?Wo:jo}let l=o(r),a=l(r);const c=[Go,Qo],u=[];function d(p,w){return p[5]?0:1}return t=d(r),n=u[t]=c[t](r),{c(){a.c(),e=I(),n.c(),s=we()},m(p,w){a.m(p,w),g(p,e,w),u[t].m(p,w),g(p,s,w),i=!0},p(p,w){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let k=t;t=d(p),t!==k&&(H(),$(u[k],1,1,()=>{u[k]=null}),j(),n=u[t],n||(n=u[t]=c[t](p),n.c()),f(n,1),n.m(s.parentNode,s))},i(p){i||(f(n),i=!0)},o(p){$(n),i=!1},d(p){p&&(h(e),h(s)),a.d(p),u[t].d(p)}}}function Yo(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Jo]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[0]&&(i.disabled=n[14]),32&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Sn(r){let e,t;return e=new Ve({props:{content:r[11],triggerOn:[st.Hover],delayDurationMs:300,$$slots:{default:[Ko]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Xo(r){let e,t;return e=new Ht({}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ko(r){let e,t;return e=new pt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Xo]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function el(r){let e,t,n,s,i,o,l,a,c,u,d,p,w,k=tt(r[12]);t=new Gs({}),i=new Ve({props:{content:r[11],triggerOn:[st.Hover],delayDurationMs:300,$$slots:{default:[Io]},$$scope:{ctx:r}}});let x=k&&Nn(r);const E=[Zo,Uo],_=[];function B(D,F){return D[21]?0:1}a=B(r),c=_[a]=E[a](r),d=new Ve({props:{content:r[13],triggerOn:[st.Hover],delayDurationMs:300,$$slots:{default:[Yo]},$$scope:{ctx:r}}});let m=r[5]&&Sn(r);return{c(){e=A("div"),z(t.$$.fragment),n=I(),s=A("div"),z(i.$$.fragment),o=I(),x&&x.c(),l=I(),c.c(),u=I(),z(d.$$.fragment),p=I(),m&&m.c(),C(s,"class","c-path svelte-1536g7w"),C(e,"slot","header"),C(e,"class","header svelte-1536g7w")},m(D,F){g(D,e,F),L(t,e,null),T(e,n),T(e,s),L(i,s,null),T(s,o),x&&x.m(s,null),T(e,l),_[a].m(e,null),T(e,u),L(d,e,null),T(e,p),m&&m.m(e,null),w=!0},p(D,F){const y={};2048&F[0]&&(y.content=D[11]),4096&F[0]|16384&F[1]&&(y.$$scope={dirty:F,ctx:D}),i.$set(y),4096&F[0]&&(k=tt(D[12])),k?x?x.p(D,F):(x=Nn(D),x.c(),x.m(s,null)):x&&(x.d(1),x=null);let R=a;a=B(D),a===R?_[a].p(D,F):(H(),$(_[R],1,1,()=>{_[R]=null}),j(),c=_[a],c?c.p(D,F):(c=_[a]=E[a](D),c.c()),f(c,1),c.m(e,u));const q={};8192&F[0]&&(q.content=D[13]),16416&F[0]|16384&F[1]&&(q.$$scope={dirty:F,ctx:D}),d.$set(q),D[5]?m?(m.p(D,F),32&F[0]&&f(m,1)):(m=Sn(D),m.c(),f(m,1),m.m(e,null)):m&&(H(),$(m,1,1,()=>{m=null}),j())},i(D){w||(f(t.$$.fragment,D),f(i.$$.fragment,D),f(c),f(d.$$.fragment,D),f(m),w=!0)},o(D){$(t.$$.fragment,D),$(i.$$.fragment,D),$(c),$(d.$$.fragment,D),$(m),w=!1},d(D){D&&h(e),M(t),M(i),x&&x.d(),_[a].d(),M(d),m&&m.d()}}}function tl(r){let e,t,n,s,i;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[el],default:[So]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Qs({props:l}),Te.push(()=>Ke(t,"collapsed",o)),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c svelte-1536g7w"),C(e,"id",s=Mn(r[3])),Ae(e,"focused",r[24]===r[3])},m(a,c){g(a,e,c),L(t,e,null),i=!0},p(a,c){const u={};16777211&c[0]|16384&c[1]&&(u.$$scope={dirty:c,ctx:a}),!n&&4&c[0]&&(n=!0,u.collapsed=a[2],et(()=>n=!1)),t.$set(u),(!i||8&c[0]&&s!==(s=Mn(a[3])))&&C(e,"id",s),(!i||16777224&c[0])&&Ae(e,"focused",a[24]===a[3])},i(a){i||(f(t.$$.fragment,a),i=!0)},o(a){$(t.$$.fragment,a),i=!1},d(a){a&&h(e),M(t)}}}function nl(r,e,t){let n,s,i,o,l,a,c,u,d,p,w,k,x,E,_,B,m,D,F,y,R,q,O;Re(r,Is,P=>t(40,q=P));let{path:V}=e,{change:W}=e,{descriptions:ee=[]}=e,{areDescriptionsVisible:de=!0}=e,{isExpandedDefault:ce}=e,{isCollapsed:ae=!ce}=e,{isApplying:J}=e,{hasApplied:$e}=e,{onApplyChanges:De}=e,{onCodeChange:ie}=e,{onOpenFile:ve}=e,{isAgentFromDifferentRepo:ke=!1}=e;const xe=Xe(xo);Re(r,xe,P=>t(24,O=P));const Ne=Xe(Ui.key);let fe=W.modifiedCode,be=F;function ye(){t(11,be=`Open ${F??"file"}`)}return ot(()=>{ye()}),r.$$set=P=>{"path"in P&&t(3,V=P.path),"change"in P&&t(0,W=P.change),"descriptions"in P&&t(4,ee=P.descriptions),"areDescriptionsVisible"in P&&t(1,de=P.areDescriptionsVisible),"isExpandedDefault"in P&&t(29,ce=P.isExpandedDefault),"isCollapsed"in P&&t(2,ae=P.isCollapsed),"isApplying"in P&&t(30,J=P.isApplying),"hasApplied"in P&&t(5,$e=P.hasApplied),"onApplyChanges"in P&&t(31,De=P.onApplyChanges),"onCodeChange"in P&&t(32,ie=P.onCodeChange),"onOpenFile"in P&&t(33,ve=P.onOpenFile),"isAgentFromDifferentRepo"in P&&t(34,ke=P.isAgentFromDifferentRepo)},r.$$.update=()=>{var P;1&r.$$.dirty[0]&&t(6,fe=W.modifiedCode),1&r.$$.dirty[0]&&t(39,n=Mi(W.diff)),256&r.$$.dirty[1]&&t(23,s=n.additions),256&r.$$.dirty[1]&&t(22,i=n.deletions),1&r.$$.dirty[0]&&t(21,o=Ri(W)),1&r.$$.dirty[0]&&t(20,l=Ti(W)),8&r.$$.dirty[0]&&t(38,a=Xs(V)),8&r.$$.dirty[0]&&t(19,c=We(V)),8&r.$$.dirty[0]&&t(37,u=Ks(V)),1&r.$$.dirty[0]&&t(10,d=((P=W.originalCode)==null?void 0:P.length)||0),64&r.$$.dirty[0]&&t(9,p=(fe==null?void 0:fe.length)||0),1024&r.$$.dirty[0]&&t(36,w=At(d)),512&r.$$.dirty[0]&&t(35,k=At(p)),65&r.$$.dirty[0]&&t(8,x=!fe&&!!W.originalCode),65&r.$$.dirty[0]&&t(7,E=!!fe&&!W.originalCode),128&r.$$.dirty[1]&&t(18,_=a),192&r.$$.dirty[1]&&t(17,B=!a&&u),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,m=!a&&!u&&(k||x&&w||E&&k)),512&r.$$.dirty[1]&&t(15,D=Ys(q==null?void 0:q.category,q==null?void 0:q.intensity)),8&r.$$.dirty[0]&&t(12,F=ei(V)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,y=J||ke),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,R=J?"Applying changes...":$e?"Reapply changes to local file":ke?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[W,de,ae,V,ee,$e,fe,E,x,p,d,be,F,R,y,D,m,B,_,c,l,o,i,s,O,xe,function(P){t(6,fe=P.detail.modifiedCode),ie==null||ie(fe)},function(){Ne.reportApplyChangesEvent(),t(0,W.modifiedCode=fe,W),ie==null||ie(fe),De==null||De()},async function(){ve&&(t(11,be="Opening file..."),await ve()?ye():(t(11,be="Failed to open file. Does the file exist?"),setTimeout(()=>{ye()},2e3)))},ce,J,De,ie,ve,ke,k,w,u,a,n,q,function(P){de=P,t(1,de)},function(P){ae=P,t(2,ae)}]}class sl extends te{constructor(e){super(),ne(this,e,nl,tl,K,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}}function Pn(r,e,t){const n=r.slice();return n[1]=e[t],n[3]=t,n}function il(r,e,t){const n=r.slice();return n[1]=e[t],n}function rl(r,e,t){const n=r.slice();return n[1]=e[t],n}function ol(r){let e;return{c(){e=A("div"),e.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',C(e,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(t,n){g(t,e,n)},p:X,d(t){t&&h(e)}}}function ll(r){let e,t,n,s,i=pe(Array(2)),o=[];for(let l=0;l<i.length;l+=1)o[l]=ol(rl(r,i,l));return{c(){e=A("div"),t=A("div"),t.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=I(),s=A("div");for(let l=0;l<o.length;l+=1)o[l].c();C(t,"class","c-skeleton-diff__header svelte-1eiztmz"),C(s,"class","c-skeleton-diff__changes svelte-1eiztmz"),C(e,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){g(l,e,a),T(e,t),T(e,n),T(e,s);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(s,null)},p:X,d(l){l&&h(e),Me(o,l)}}}function In(r){let e,t,n,s,i,o,l=r[3]===0&&function(u){let d;return{c(){d=A("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',C(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,w){g(p,d,w)},d(p){p&&h(d)}}}(),a=pe(Array(2)),c=[];for(let u=0;u<a.length;u+=1)c[u]=ll(il(r,a,u));return{c(){e=A("div"),t=A("div"),n=A("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',s=I(),l&&l.c(),i=I();for(let u=0;u<c.length;u+=1)c[u].c();o=I(),C(n,"class","c-skeleton-diff__content svelte-1eiztmz"),C(t,"class","c-skeleton-diff__header svelte-1eiztmz"),C(e,"class","c-skeleton-diff__section svelte-1eiztmz")},m(u,d){g(u,e,d),T(e,t),T(t,n),T(t,s),l&&l.m(t,null),T(e,i);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(e,null);T(e,o)},p(u,d){},d(u){u&&h(e),l&&l.d(),Me(c,u)}}}function al(r){let e,t=pe(Array(r[0])),n=[];for(let s=0;s<t.length;s+=1)n[s]=In(Pn(r,t,s));return{c(){e=A("div");for(let s=0;s<n.length;s+=1)n[s].c();C(e,"class","c-skeleton-diff svelte-1eiztmz")},m(s,i){g(s,e,i);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(s,[i]){if(1&i){let o;for(t=pe(Array(s[0])),o=0;o<t.length;o+=1){const l=Pn(s,t,o);n[o]?n[o].p(l,i):(n[o]=In(l),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},i:X,o:X,d(s){s&&h(e),Me(n,s)}}}function cl(r,e,t){let{count:n=2}=e;return r.$$set=s=>{"count"in s&&t(0,n=s.count)},[n]}class ul extends te{constructor(e){super(),ne(this,e,cl,al,K,{count:0})}}function dl(r){let e,t;return{c(){e=kt("svg"),t=kt("path"),C(t,"fill-rule","evenodd"),C(t,"clip-rule","evenodd"),C(t,"d","M5.26047 5.79388C5.07301 5.98134 5.07301 6.28525 5.26047 6.47271C5.44792 6.66015 5.75184 6.66015 5.93929 6.47271L7.99988 4.41211L10.0605 6.47271C10.2479 6.66015 10.5518 6.66015 10.7393 6.47271C10.9267 6.28525 10.9267 5.98134 10.7393 5.79388L8.33929 3.39388C8.24926 3.30387 8.12717 3.2533 7.99988 3.2533C7.87257 3.2533 7.75048 3.30387 7.66046 3.39388L5.26047 5.79388ZM10.7393 10.206C10.9267 10.0186 10.9267 9.71467 10.7393 9.52722C10.5518 9.33977 10.2479 9.33977 10.0605 9.52722L7.99988 11.5878L5.93929 9.52722C5.75184 9.33977 5.44792 9.33977 5.26047 9.52722C5.07301 9.71467 5.07301 10.0186 5.26047 10.206L7.66046 12.6061C7.84792 12.7935 8.15184 12.7935 8.33929 12.6061L10.7393 10.206Z"),C(t,"fill","currentColor"),C(t,"fill-opacity","1"),C(e,"width","16"),C(e,"height","16"),C(e,"viewBox","0 0 16 16"),C(e,"fill","none"),C(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){g(n,e,s),T(e,t)},p:X,i:X,o:X,d(n){n&&h(e)}}}class pl extends te{constructor(e){super(),ne(this,e,null,dl,K,{})}}function Un(...r){return"/"+r.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Zn(r){return r.startsWith("/")||r.startsWith("#")}function Nt(r){let e,t;const n=r[5].default,s=_e(n,r,r[4],null);let i=[{id:r[1]}],o={};for(let l=0;l<i.length;l+=1)o=Zs(o,i[l]);return{c(){e=A(`h${r[0].depth}`),s&&s.c(),yt(`h${r[0].depth}`)(e,o)},m(l,a){g(l,e,a),s&&s.m(e,null),t=!0},p(l,a){s&&s.p&&(!t||16&a)&&Be(s,n,l,l[4],t?Le(n,l[4],a,null):ze(l[4]),null),yt(`h${l[0].depth}`)(e,o=Vs(i,[(!t||2&a)&&{id:l[1]}]))},i(l){t||(f(s,l),t=!0)},o(l){$(s,l),t=!1},d(l){l&&h(e),s&&s.d(l)}}}function fl(r){let e,t,n=`h${r[0].depth}`,s=`h${r[0].depth}`&&Nt(r);return{c(){s&&s.c(),e=we()},m(i,o){s&&s.m(i,o),g(i,e,o),t=!0},p(i,[o]){`h${i[0].depth}`?n?K(n,`h${i[0].depth}`)?(s.d(1),s=Nt(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):s.p(i,o):(s=Nt(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):n&&(s.d(1),s=null,n=`h${i[0].depth}`)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function $l(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e,l;return r.$$set=a=>{"token"in a&&t(0,i=a.token),"options"in a&&t(2,o=a.options),"$$scope"in a&&t(4,s=a.$$scope)},r.$$.update=()=>{var a,c;5&r.$$.dirty&&t(1,(a=i.text,c=o.slugger,l=c.slug(a).replace(/--+/g,"-")))},[i,l,o,void 0,s,n]}class gl extends te{constructor(e){super(),ne(this,e,$l,fl,K,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function hl(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("blockquote"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function ml(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Dl extends te{constructor(e){super(),ne(this,e,ml,hl,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Vn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Hn(r){let e,t,n=pe(r[0]),s=[];for(let o=0;o<n.length;o+=1)s[o]=jn(Vn(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=we()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(7&l){let a;for(n=pe(o[0]),a=0;a<n.length;a+=1){const c=Vn(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=jn(c),s[a].c(),f(s[a],1),s[a].m(e.parentNode,e))}for(H(),a=n.length;a<s.length;a+=1)i(a);j()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Me(s,o)}}}function jn(r){let e,t;return e=new di({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token=n[3]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Fl(r){let e,t,n=r[0]&&Hn(r);return{c(){n&&n.c(),e=we()},m(s,i){n&&n.m(s,i),g(s,e,i),t=!0},p(s,[i]){s[0]?n?(n.p(s,i),1&i&&f(n,1)):(n=Hn(s),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(H(),$(n,1,1,()=>{n=null}),j())},i(s){t||(f(n),t=!0)},o(s){$(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Cl(r,e,t){let{tokens:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class Mt extends te{constructor(e){super(),ne(this,e,Cl,Fl,K,{tokens:0,renderers:1,options:2})}}function Wn(r){let e,t,n;var s=r[1][r[0].type];function i(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[kl]},$$scope:{ctx:o}}}}return s&&(e=sn(s,i(r))),{c(){e&&z(e.$$.fragment),t=we()},m(o,l){e&&L(e,o,l),g(o,t,l),n=!0},p(o,l){if(3&l&&s!==(s=o[1][o[0].type])){if(e){H();const a=e;$(a.$$.fragment,1,0,()=>{M(a,1)}),j()}s?(e=sn(s,i(o)),z(e.$$.fragment),f(e.$$.fragment,1),L(e,t.parentNode,t)):e=null}else if(s){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)}},i(o){n||(e&&f(e.$$.fragment,o),n=!0)},o(o){e&&$(e.$$.fragment,o),n=!1},d(o){o&&h(t),e&&M(e,o)}}}function xl(r){let e,t=r[0].raw+"";return{c(){e=N(t)},m(n,s){g(n,e,s)},p(n,s){1&s&&t!==(t=n[0].raw+"")&&oe(e,t)},i:X,o:X,d(n){n&&h(e)}}}function wl(r){let e,t;return e=new Mt({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.tokens=n[0].tokens),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function kl(r){let e,t,n,s;const i=[wl,xl],o=[];function l(a,c){return"tokens"in a[0]&&a[0].tokens?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e===u?o[e].p(a,c):(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t?t.p(a,c):(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function yl(r){let e,t,n=r[1][r[0].type]&&Wn(r);return{c(){n&&n.c(),e=we()},m(s,i){n&&n.m(s,i),g(s,e,i),t=!0},p(s,[i]){s[1][s[0].type]?n?(n.p(s,i),3&i&&f(n,1)):(n=Wn(s),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(H(),$(n,1,1,()=>{n=null}),j())},i(s){t||(f(n),t=!0)},o(s){$(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function vl(r,e,t){let{token:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class di extends te{constructor(e){super(),ne(this,e,vl,yl,K,{token:0,renderers:1,options:2})}}function Qn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Gn(r){let e,t;return e=new di({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token={...n[4]}),2&s&&(i.options=n[1]),4&s&&(i.renderers=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function qt(r){let e,t,n,s=pe(r[0].items),i=[];for(let c=0;c<s.length;c+=1)i[c]=Gn(Qn(r,s,c));const o=c=>$(i[c],1,1,()=>{i[c]=null});let l=[{start:t=r[0].start||1}],a={};for(let c=0;c<l.length;c+=1)a=Zs(a,l[c]);return{c(){e=A(r[3]);for(let c=0;c<i.length;c+=1)i[c].c();yt(r[3])(e,a)},m(c,u){g(c,e,u);for(let d=0;d<i.length;d+=1)i[d]&&i[d].m(e,null);n=!0},p(c,u){if(7&u){let d;for(s=pe(c[0].items),d=0;d<s.length;d+=1){const p=Qn(c,s,d);i[d]?(i[d].p(p,u),f(i[d],1)):(i[d]=Gn(p),i[d].c(),f(i[d],1),i[d].m(e,null))}for(H(),d=s.length;d<i.length;d+=1)o(d);j()}yt(c[3])(e,a=Vs(l,[(!n||1&u&&t!==(t=c[0].start||1))&&{start:t}]))},i(c){if(!n){for(let u=0;u<s.length;u+=1)f(i[u]);n=!0}},o(c){i=i.filter(Boolean);for(let u=0;u<i.length;u+=1)$(i[u]);n=!1},d(c){c&&h(e),Me(i,c)}}}function Al(r){let e,t=r[3],n=r[3]&&qt(r);return{c(){n&&n.c(),e=we()},m(s,i){n&&n.m(s,i),g(s,e,i)},p(s,[i]){s[3]?t?K(t,s[3])?(n.d(1),n=qt(s),t=s[3],n.c(),n.m(e.parentNode,e)):n.p(s,i):(n=qt(s),t=s[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=s[3])},i:X,o(s){$(n,s)},d(s){s&&h(e),n&&n.d(s)}}}function bl(r,e,t){let n,{token:s}=e,{options:i}=e,{renderers:o}=e;return r.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(1,i=l.options),"renderers"in l&&t(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&t(3,n=s.ordered?"ol":"ul")},[s,i,o,n]}class El extends te{constructor(e){super(),ne(this,e,bl,Al,K,{token:0,options:1,renderers:2})}}function _l(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("li"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Bl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class zl extends te{constructor(e){super(),ne(this,e,Bl,_l,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ll(r){let e;return{c(){e=A("br")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&h(e)}}}function Ml(r,e,t){return[void 0,void 0,void 0]}class Rl extends te{constructor(e){super(),ne(this,e,Ml,Ll,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Tl(r){let e,t,n,s,i=r[0].text+"";return{c(){e=A("pre"),t=A("code"),n=N(i),C(t,"class",s=`lang-${r[0].lang}`)},m(o,l){g(o,e,l),T(e,t),T(t,n)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&oe(n,i),1&l&&s!==(s=`lang-${o[0].lang}`)&&C(t,"class",s)},i:X,o:X,d(o){o&&h(e)}}}function Nl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class ql extends te{constructor(e){super(),ne(this,e,Nl,Tl,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ol(r){let e,t,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){e=A("code"),t=N(n)},m(s,i){g(s,e,i),T(e,t)},p(s,[i]){1&i&&n!==(n=s[0].raw.slice(1,s[0].raw.length-1)+"")&&oe(t,n)},i:X,o:X,d(s){s&&h(e)}}}function Sl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Pl extends te{constructor(e){super(),ne(this,e,Sl,Ol,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Jn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Yn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Xn(r,e,t){const n=r.slice();return n[9]=e[t],n}function Kn(r){let e,t,n,s;return t=new Mt({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){e=A("th"),z(t.$$.fragment),n=I(),C(e,"scope","col")},m(i,o){g(i,e,o),L(t,e,null),T(e,n),s=!0},p(i,o){const l={};1&o&&(l.tokens=i[9].tokens),2&o&&(l.options=i[1]),4&o&&(l.renderers=i[2]),t.$set(l)},i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&h(e),M(t)}}}function es(r){let e,t,n;return t=new Mt({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){e=A("td"),z(t.$$.fragment)},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};1&i&&(o.tokens=s[6].tokens),2&i&&(o.options=s[1]),4&i&&(o.renderers=s[2]),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function ts(r){let e,t,n,s=pe(r[3]),i=[];for(let l=0;l<s.length;l+=1)i[l]=es(Yn(r,s,l));const o=l=>$(i[l],1,1,()=>{i[l]=null});return{c(){e=A("tr");for(let l=0;l<i.length;l+=1)i[l].c();t=I()},m(l,a){g(l,e,a);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(e,null);T(e,t),n=!0},p(l,a){if(7&a){let c;for(s=pe(l[3]),c=0;c<s.length;c+=1){const u=Yn(l,s,c);i[c]?(i[c].p(u,a),f(i[c],1)):(i[c]=es(u),i[c].c(),f(i[c],1),i[c].m(e,t))}for(H(),c=s.length;c<i.length;c+=1)o(c);j()}},i(l){if(!n){for(let a=0;a<s.length;a+=1)f(i[a]);n=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)$(i[a]);n=!1},d(l){l&&h(e),Me(i,l)}}}function Il(r){let e,t,n,s,i,o,l=pe(r[0].header),a=[];for(let w=0;w<l.length;w+=1)a[w]=Kn(Xn(r,l,w));const c=w=>$(a[w],1,1,()=>{a[w]=null});let u=pe(r[0].rows),d=[];for(let w=0;w<u.length;w+=1)d[w]=ts(Jn(r,u,w));const p=w=>$(d[w],1,1,()=>{d[w]=null});return{c(){e=A("table"),t=A("thead"),n=A("tr");for(let w=0;w<a.length;w+=1)a[w].c();s=I(),i=A("tbody");for(let w=0;w<d.length;w+=1)d[w].c()},m(w,k){g(w,e,k),T(e,t),T(t,n);for(let x=0;x<a.length;x+=1)a[x]&&a[x].m(n,null);T(e,s),T(e,i);for(let x=0;x<d.length;x+=1)d[x]&&d[x].m(i,null);o=!0},p(w,[k]){if(7&k){let x;for(l=pe(w[0].header),x=0;x<l.length;x+=1){const E=Xn(w,l,x);a[x]?(a[x].p(E,k),f(a[x],1)):(a[x]=Kn(E),a[x].c(),f(a[x],1),a[x].m(n,null))}for(H(),x=l.length;x<a.length;x+=1)c(x);j()}if(7&k){let x;for(u=pe(w[0].rows),x=0;x<u.length;x+=1){const E=Jn(w,u,x);d[x]?(d[x].p(E,k),f(d[x],1)):(d[x]=ts(E),d[x].c(),f(d[x],1),d[x].m(i,null))}for(H(),x=u.length;x<d.length;x+=1)p(x);j()}},i(w){if(!o){for(let k=0;k<l.length;k+=1)f(a[k]);for(let k=0;k<u.length;k+=1)f(d[k]);o=!0}},o(w){a=a.filter(Boolean);for(let k=0;k<a.length;k+=1)$(a[k]);d=d.filter(Boolean);for(let k=0;k<d.length;k+=1)$(d[k]);o=!1},d(w){w&&h(e),Me(a,w),Me(d,w)}}}function Ul(r,e,t){let{token:n}=e,{options:s}=e,{renderers:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,s=o.options),"renderers"in o&&t(2,i=o.renderers)},[n,s,i]}class Zl extends te{constructor(e){super(),ne(this,e,Ul,Il,K,{token:0,options:1,renderers:2})}}function Vl(r){let e,t,n=r[0].text+"";return{c(){e=new vi(!1),t=we(),e.a=t},m(s,i){e.m(n,s,i),g(s,t,i)},p(s,[i]){1&i&&n!==(n=s[0].text+"")&&e.p(n)},i:X,o:X,d(s){s&&(h(t),e.d())}}}function Hl(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class jl extends te{constructor(e){super(),ne(this,e,Hl,Vl,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Wl(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("p"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function Ql(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}let Gl=class extends te{constructor(r){super(),ne(this,r,Ql,Wl,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function Jl(r){let e,t,n,s;const i=r[4].default,o=_e(i,r,r[3],null);return{c(){e=A("a"),o&&o.c(),C(e,"href",t=Zn(r[0].href)?Un(r[1].baseUrl,r[0].href):r[0].href),C(e,"title",n=r[0].title)},m(l,a){g(l,e,a),o&&o.m(e,null),s=!0},p(l,[a]){o&&o.p&&(!s||8&a)&&Be(o,i,l,l[3],s?Le(i,l[3],a,null):ze(l[3]),null),(!s||3&a&&t!==(t=Zn(l[0].href)?Un(l[1].baseUrl,l[0].href):l[0].href))&&C(e,"href",t),(!s||1&a&&n!==(n=l[0].title))&&C(e,"title",n)},i(l){s||(f(o,l),s=!0)},o(l){$(o,l),s=!1},d(l){l&&h(e),o&&o.d(l)}}}function Yl(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e;return r.$$set=l=>{"token"in l&&t(0,i=l.token),"options"in l&&t(1,o=l.options),"$$scope"in l&&t(3,s=l.$$scope)},[i,o,void 0,s,n]}class Xl extends te{constructor(e){super(),ne(this,e,Yl,Jl,K,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Kl(r){let e;const t=r[4].default,n=_e(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Be(n,t,s,s[3],e?Le(t,s[3],i,null):ze(s[3]),null)},i(s){e||(f(n,s),e=!0)},o(s){$(n,s),e=!1},d(s){n&&n.d(s)}}}function ea(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ta extends te{constructor(e){super(),ne(this,e,ea,Kl,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function na(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("dfn"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function sa(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ia extends te{constructor(e){super(),ne(this,e,sa,na,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ra(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("del"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function oa(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class la extends te{constructor(e){super(),ne(this,e,oa,ra,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function aa(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("em"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function ca(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ua extends te{constructor(e){super(),ne(this,e,ca,aa,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function da(r){let e;return{c(){e=A("hr")},m(t,n){g(t,e,n)},p:X,i:X,o:X,d(t){t&&h(e)}}}function pa(r,e,t){return[void 0,void 0,void 0]}class fa extends te{constructor(e){super(),ne(this,e,pa,da,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function $a(r){let e,t;const n=r[4].default,s=_e(n,r,r[3],null);return{c(){e=A("strong"),s&&s.c()},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Be(s,n,i,i[3],t?Le(n,i[3],o,null):ze(i[3]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function ga(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ha extends te{constructor(e){super(),ne(this,e,ga,$a,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ma(r){let e,t,n,s;return{c(){e=A("img"),Oe(e.src,t=r[0].href)||C(e,"src",t),C(e,"title",n=r[0].title),C(e,"alt",s=r[0].text),C(e,"class","markdown-image svelte-z38cge")},m(i,o){g(i,e,o)},p(i,[o]){1&o&&!Oe(e.src,t=i[0].href)&&C(e,"src",t),1&o&&n!==(n=i[0].title)&&C(e,"title",n),1&o&&s!==(s=i[0].text)&&C(e,"alt",s)},i:X,o:X,d(i){i&&h(e)}}}function Da(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Fa extends te{constructor(e){super(),ne(this,e,Da,ma,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ca(r){let e;const t=r[4].default,n=_e(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Be(n,t,s,s[3],e?Le(t,s[3],i,null):ze(s[3]),null)},i(s){e||(f(n,s),e=!0)},o(s){$(n,s),e=!1},d(s){n&&n.d(s)}}}function xa(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ns extends te{constructor(e){super(),ne(this,e,xa,Ca,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function wa(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let lt={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function ss(r){lt=r}const pi=/[&<>"']/,ka=new RegExp(pi.source,"g"),fi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,ya=new RegExp(fi.source,"g"),va={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},is=r=>va[r];function Se(r,e){if(e){if(pi.test(r))return r.replace(ka,is)}else if(fi.test(r))return r.replace(ya,is);return r}const Aa=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function ba(r){return r.replace(Aa,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Ea=/(^|[^\[])\^/g;function Ce(r,e){let t=typeof r=="string"?r:r.source;e=e||"";const n={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(Ea,"$1"),t=t.replace(s,o),n},getRegex:()=>new RegExp(t,e)};return n}function rs(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const ct={exec:()=>null};function os(r,e){const t=r.replace(/\|/g,(s,i,o)=>{let l=!1,a=i;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function Ct(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n;){const i=r.charAt(n-s-1);if(i!==e||t){if(i===e||!t)break;s++}else s++}return r.slice(0,n-s)}function ls(r,e,t,n){const s=e.href,i=e.title?Se(e.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:s,title:i,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:s,title:i,text:Se(o)}}class Et{constructor(e){me(this,"options");me(this,"rules");me(this,"lexer");this.options=e||lt}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:Ct(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],s=function(i,o){const l=i.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(c=>{const u=c.match(/^\s+/);if(u===null)return c;const[d]=u;return d.length>=a.length?c.slice(a.length):c}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:s}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const s=Ct(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=Ct(t[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:t[0],tokens:i,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1,i={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",c=!1;for(;e;){let u=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let d=t[2].split(`
`,1)[0].replace(/^\t+/,_=>" ".repeat(3*_.length)),p=e.split(`
`,1)[0],w=0;this.options.pedantic?(w=2,a=d.trimStart()):(w=t[2].search(/[^ ]/),w=w>4?1:w,a=d.slice(w),w+=t[1].length);let k=!1;if(!d&&/^ *$/.test(p)&&(l+=p+`
`,e=e.substring(p.length+1),u=!0),!u){const _=new RegExp(`^ {0,${Math.min(3,w-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),B=new RegExp(`^ {0,${Math.min(3,w-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),m=new RegExp(`^ {0,${Math.min(3,w-1)}}(?:\`\`\`|~~~)`),D=new RegExp(`^ {0,${Math.min(3,w-1)}}#`);for(;e;){const F=e.split(`
`,1)[0];if(p=F,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),m.test(p)||D.test(p)||_.test(p)||B.test(e))break;if(p.search(/[^ ]/)>=w||!p.trim())a+=`
`+p.slice(w);else{if(k||d.search(/[^ ]/)>=4||m.test(d)||D.test(d)||B.test(d))break;a+=`
`+p}k||p.trim()||(k=!0),l+=F+`
`,e=e.substring(F.length+1),d=p.slice(w)}}i.loose||(c?i.loose=!0:/\n *\n *$/.test(l)&&(c=!0));let x,E=null;this.options.gfm&&(E=/^\[[ xX]\] /.exec(a),E&&(x=E[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:l,task:!!E,checked:x,loose:!1,text:a,tokens:[]}),i.raw+=l}i.items[i.items.length-1].raw=l.trimEnd(),i.items[i.items.length-1].text=a.trimEnd(),i.raw=i.raw.trimEnd();for(let u=0;u<i.items.length;u++)if(this.lexer.state.top=!1,i.items[u].tokens=this.lexer.blockTokens(i.items[u].text,[]),!i.loose){const d=i.items[u].tokens.filter(w=>w.type==="space"),p=d.length>0&&d.some(w=>/\n.*\n/.test(w.raw));i.loose=p}if(i.loose)for(let u=0;u<i.items.length;u++)i.items[u].loose=!0;return i}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),s=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:s,title:i}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=os(t[1]),s=t[2].replace(/^\||\| *$/g,"").split("|"),i=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of i)o.rows.push(os(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Se(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=Ct(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let c=0;for(let u=0;u<l.length;u++)if(l[u]==="\\")u++;else if(l[u]===a[0])c++;else if(l[u]===a[1]&&(c--,c<0))return u;return-1}(t[2],"()");if(o>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let s=t[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);o&&(s=o[1],i=o[3])}else i=t[3]?t[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),ls(t,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const s=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return ls(n,s,n[0],this.lexer)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const i=[...s[0]].length-1;let o,l,a=i,c=0;const u=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,t=t.slice(-1*e.length+i);(s=u.exec(t))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){a+=l;continue}if((s[5]||s[6])&&i%3&&!((i+l)%3)){c+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+c);const d=[...s[0]][0].length,p=e.slice(0,i+s.index+d+l);if(Math.min(i,l)%2){const k=p.slice(1,-1);return{type:"em",raw:p,text:k,tokens:this.lexer.inlineTokens(k)}}const w=p.slice(2,-2);return{type:"strong",raw:p,text:w,tokens:this.lexer.inlineTokens(w)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const s=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return s&&i&&(n=n.substring(1,n.length-1)),n=Se(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,s;return t[2]==="@"?(n=Se(t[1]),s="mailto:"+n):(n=Se(t[1]),s=n),{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let s,i;if(t[2]==="@")s=Se(t[0]),i="mailto:"+s;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);s=Se(t[0]),i=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:Se(t[0]),{type:"text",raw:t[0],text:n}}}}const gt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,$i=/(?:[*+-]|\d{1,9}[.)])/,gi=Ce(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,$i).getRegex(),Qt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Gt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,_a=Ce(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Gt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Ba=Ce(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,$i).getRegex(),Rt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Jt=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,za=Ce("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Jt).replace("tag",Rt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),as=Ce(Qt).replace("hr",gt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Rt).getRegex(),Yt={blockquote:Ce(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",as).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:_a,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:gt,html:za,lheading:gi,list:Ba,newline:/^(?: *(?:\n|$))+/,paragraph:as,table:ct,text:/^[^\n]+/},cs=Ce("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",gt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Rt).getRegex(),La={...Yt,table:cs,paragraph:Ce(Qt).replace("hr",gt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",cs).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Rt).getRegex()},Ma={...Yt,html:Ce(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Jt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ct,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Ce(Qt).replace("hr",gt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",gi).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},hi=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,mi=/^( {2,}|\\)\n(?!\s*$)/,ht="\\p{P}$+<=>`^|~",Ra=Ce(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,ht).getRegex(),Ta=Ce(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,ht).getRegex(),Na=Ce("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,ht).getRegex(),qa=Ce("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,ht).getRegex(),Oa=Ce(/\\([punct])/,"gu").replace(/punct/g,ht).getRegex(),Sa=Ce(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Pa=Ce(Jt).replace("(?:-->|$)","-->").getRegex(),Ia=Ce("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Pa).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),_t=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ua=Ce(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",_t).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),us=Ce(/^!?\[(label)\]\[(ref)\]/).replace("label",_t).replace("ref",Gt).getRegex(),ds=Ce(/^!?\[(ref)\](?:\[\])?/).replace("ref",Gt).getRegex(),Xt={_backpedal:ct,anyPunctuation:Oa,autolink:Sa,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:mi,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ct,emStrongLDelim:Ta,emStrongRDelimAst:Na,emStrongRDelimUnd:qa,escape:hi,link:Ua,nolink:ds,punctuation:Ra,reflink:us,reflinkSearch:Ce("reflink|nolink(?!\\()","g").replace("reflink",us).replace("nolink",ds).getRegex(),tag:Ia,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ct},Za={...Xt,link:Ce(/^!?\[(label)\]\((.*?)\)/).replace("label",_t).getRegex(),reflink:Ce(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",_t).getRegex()},Zt={...Xt,escape:Ce(hi).replace("])","~|])").getRegex(),url:Ce(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Va={...Zt,br:Ce(mi).replace("{2,}","*").getRegex(),text:Ce(Zt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},xt={normal:Yt,gfm:La,pedantic:Ma},at={normal:Xt,gfm:Zt,breaks:Va,pedantic:Za};class Ze{constructor(e){me(this,"tokens");me(this,"options");me(this,"state");me(this,"tokenizer");me(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||lt,this.options.tokenizer=this.options.tokenizer||new Et,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:xt.normal,inline:at.normal};this.options.pedantic?(t.block=xt.pedantic,t.inline=at.pedantic):this.options.gfm&&(t.block=xt.gfm,this.options.breaks?t.inline=at.breaks:t.inline=at.gfm),this.tokenizer.rules=t}static get rules(){return{block:xt,inline:at}}static lex(e,t){return new Ze(t).lex(e)}static lexInline(e,t){return new Ze(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,s,i,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(l,a,c)=>a+"    ".repeat(c.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?t.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(i=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=e.slice(1);let c;this.options.extensions.startBlock.forEach(u=>{c=u.call({lexer:this},a),typeof c=="number"&&c>=0&&(l=Math.min(l,c))}),l<1/0&&l>=0&&(i=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i)))s=t[t.length-1],o&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n),o=i.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n);else if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,s,i,o,l,a,c=e;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)u.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,o.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(u=>!!(n=u.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,c,a))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(i=e,this.options.extensions&&this.options.extensions.startInline){let u=1/0;const d=e.slice(1);let p;this.options.extensions.startInline.forEach(w=>{p=w.call({lexer:this},d),typeof p=="number"&&p>=0&&(u=Math.min(u,p))}),u<1/0&&u>=0&&(i=e.substring(0,u+1))}if(n=this.tokenizer.inlineText(i))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,s=t[t.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(e){const u="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(u);break}throw new Error(u)}}else e=e.substring(n.raw.length),t.push(n);return t}}class Bt{constructor(e){me(this,"options");this.options=e||lt}code(e,t,n){var i;const s=(i=(t||"").match(/^\S*/))==null?void 0:i[0];return e=e.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+Se(s)+'">'+(n?e:Se(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:Se(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const s=t?"ol":"ul";return"<"+s+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+s+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const s=rs(e);if(s===null)return n;let i='<a href="'+(e=s)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>",i}image(e,t,n){const s=rs(e);if(s===null)return n;let i=`<img src="${e=s}" alt="${n}"`;return t&&(i+=` title="${t}"`),i+=">",i}text(e){return e}}class Kt{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class je{constructor(e){me(this,"options");me(this,"renderer");me(this,"textRenderer");this.options=e||lt,this.options.renderer=this.options.renderer||new Bt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Kt}static parse(e,t){return new je(t).parse(e)}static parseInline(e,t){return new je(t).parseInline(e)}parse(e,t=!0){let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(i.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=i;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,ba(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let l="",a="";for(let u=0;u<o.header.length;u++)a+=this.renderer.tablecell(this.parseInline(o.header[u].tokens),{header:!0,align:o.align[u]});l+=this.renderer.tablerow(a);let c="";for(let u=0;u<o.rows.length;u++){const d=o.rows[u];a="";for(let p=0;p<d.length;p++)a+=this.renderer.tablecell(this.parseInline(d[p].tokens),{header:!1,align:o.align[p]});c+=this.renderer.tablerow(a)}n+=this.renderer.table(l,c);continue}case"blockquote":{const o=i,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=i,l=o.ordered,a=o.start,c=o.loose;let u="";for(let d=0;d<o.items.length;d++){const p=o.items[d],w=p.checked,k=p.task;let x="";if(p.task){const E=this.renderer.checkbox(!!w);c?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=E+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=E+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:E+" "}):x+=E+" "}x+=this.parse(p.tokens,c),u+=this.renderer.listitem(x,k,!!w)}n+=this.renderer.list(u,l,a);continue}case"html":{const o=i;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,l=o.tokens?this.parseInline(o.tokens):o.text;for(;s+1<e.length&&e[s+1].type==="text";)o=e[++s],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=o||"";continue}}switch(i.type){case"escape":{const o=i;n+=t.text(o.text);break}case"html":{const o=i;n+=t.html(o.text);break}case"link":{const o=i;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=i;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=i;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=i;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=i;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=i;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=i;n+=t.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class ut{constructor(e){me(this,"options");this.options=e||lt}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}me(ut,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var rt,Vt,Di,Os;const nt=new(Os=class{constructor(...r){tn(this,rt);me(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});me(this,"options",this.setOptions);me(this,"parse",Dt(this,rt,Vt).call(this,Ze.lex,je.parse));me(this,"parseInline",Dt(this,rt,Vt).call(this,Ze.lexInline,je.parseInline));me(this,"Parser",je);me(this,"Renderer",Bt);me(this,"TextRenderer",Kt);me(this,"Lexer",Ze);me(this,"Tokenizer",Et);me(this,"Hooks",ut);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const i of r)switch(t=t.concat(e.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of o.rows)for(const a of l)t=t.concat(this.walkTokens(a.tokens,e));break}case"list":{const o=i;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=i;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);t=t.concat(this.walkTokens(a,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=e.renderers[s.name];e.renderers[s.name]=i?function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=e[s.level];i?i.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new Bt(this.defaults);for(const i in t.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.renderer[o],a=s[o];s[o]=(...c)=>{let u=l.apply(s,c);return u===!1&&(u=a.apply(s,c)),u||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new Et(this.defaults);for(const i in t.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=t.tokenizer[o],a=s[o];s[o]=(...c)=>{let u=l.apply(s,c);return u===!1&&(u=a.apply(s,c)),u}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new ut;for(const i in t.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.hooks[o],a=s[o];ut.passThroughHooks.has(i)?s[o]=c=>{if(this.defaults.async)return Promise.resolve(l.call(s,c)).then(d=>a.call(s,d));const u=l.call(s,c);return a.call(s,u)}:s[o]=(...c)=>{let u=l.apply(s,c);return u===!1&&(u=a.apply(s,c)),u}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,i=t.walkTokens;n.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return Ze.lex(r,e??this.defaults)}parser(r,e){return je.parse(r,e??this.defaults)}},rt=new WeakSet,Vt=function(r,e){return(t,n)=>{const s={...n},i={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=Dt(this,rt,Di).call(this,!!i.silent,!!i.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(l=>r(l,i)).then(l=>i.hooks?i.hooks.processAllTokens(l):l).then(l=>i.walkTokens?Promise.all(this.walkTokens(l,i.walkTokens)).then(()=>l):l).then(l=>e(l,i)).then(l=>i.hooks?i.hooks.postprocess(l):l).catch(o);try{i.hooks&&(t=i.hooks.preprocess(t));let l=r(t,i);i.hooks&&(l=i.hooks.processAllTokens(l)),i.walkTokens&&this.walkTokens(l,i.walkTokens);let a=e(l,i);return i.hooks&&(a=i.hooks.postprocess(a)),a}catch(l){return o(l)}}},Di=function(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+Se(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},Os);function Fe(r,e){return nt.parse(r,e)}Fe.options=Fe.setOptions=function(r){return nt.setOptions(r),Fe.defaults=nt.defaults,ss(Fe.defaults),Fe},Fe.getDefaults=wa,Fe.defaults=lt,Fe.use=function(...r){return nt.use(...r),Fe.defaults=nt.defaults,ss(Fe.defaults),Fe},Fe.walkTokens=function(r,e){return nt.walkTokens(r,e)},Fe.parseInline=nt.parseInline,Fe.Parser=je,Fe.parser=je.parse,Fe.Renderer=Bt,Fe.TextRenderer=Kt,Fe.Lexer=Ze,Fe.lexer=Ze.lex,Fe.Tokenizer=Et,Fe.Hooks=ut,Fe.parse=Fe,Fe.options,Fe.setOptions,Fe.use,Fe.walkTokens,Fe.parseInline,je.parse,Ze.lex;const Ha=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,ja=Object.hasOwnProperty;class Wa{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let s=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(Ha,"").replace(/ /g,"-"))}(e,t===!0);const i=s;for(;ja.call(n.occurrences,s);)n.occurrences[i]++,s=i+"-"+n.occurrences[i];return n.occurrences[s]=0,s}reset(){this.occurrences=Object.create(null)}}function Qa(r){let e,t;return e=new Mt({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.tokens=n[0]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Ga(r,e,t){(function(){const c=console.warn;console.warn=u=>{u.includes("unknown prop")||u.includes("unexpected slot")||c(u)},ot(()=>{console.warn=c})})();let n,s,i,{source:o}=e,{options:l={}}=e,{renderers:a={}}=e;return r.$$set=c=>{"source"in c&&t(3,o=c.source),"options"in c&&t(4,l=c.options),"renderers"in c&&t(5,a=c.renderers)},r.$$.update=()=>{var c;56&r.$$.dirty&&(t(0,(c=o,n=new Ze().lex(c))),t(1,s={heading:gl,blockquote:Dl,list:El,list_item:zl,br:Rl,code:ql,codespan:Pl,table:Zl,html:jl,paragraph:Gl,link:Xl,text:ta,def:ia,del:la,em:ua,hr:fa,strong:ha,image:Fa,space:ns,escape:ns,...a}),t(2,i={baseUrl:"/",slugger:new Wa,...l}))},[n,s,i,o,l,a]}class Ja extends te{constructor(e){super(),ne(this,e,Ga,Qa,K,{source:3,options:4,renderers:5})}}const Ya=r=>({}),ps=r=>({}),Xa=r=>({}),fs=r=>({}),Ka=r=>({}),$s=r=>({});function ec(r){let e,t,n,s,i,o,l,a,c,u,d,p;const w=r[13].topBarLeft,k=_e(w,r,r[12],$s),x=r[13].topBarRight,E=_e(x,r,r[12],fs);function _(F){r[16](F)}let B={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(B.editorInstance=r[0]),o=new Oi({props:B}),Te.push(()=>Ke(o,"editorInstance",_));const m=r[13].actionsBar,D=_e(m,r,r[12],ps);return{c(){e=A("div"),t=A("div"),n=A("div"),k&&k.c(),s=I(),E&&E.c(),i=I(),z(o.$$.fragment),a=I(),c=A("div"),D&&D.c(),C(n,"class","c-codeblock__top-bar-left svelte-mexfz1"),C(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-mexfz1"),C(c,"class","c-codeblock__actions-bar-anchor svelte-mexfz1"),C(e,"class","c-codeblock svelte-mexfz1"),C(e,"role","button"),C(e,"tabindex","0")},m(F,y){g(F,e,y),T(e,t),T(t,n),k&&k.m(n,null),T(t,s),E&&E.m(t,null),T(e,i),L(o,e,null),T(e,a),T(e,c),D&&D.m(c,null),r[17](e),u=!0,d||(p=[dt(window,"focus",r[15]),dt(e,"mouseenter",r[14])],d=!0)},p(F,[y]){k&&k.p&&(!u||4096&y)&&Be(k,w,F,F[12],u?Le(w,F[12],y,Ka):ze(F[12]),$s),E&&E.p&&(!u||4096&y)&&Be(E,x,F,F[12],u?Le(x,F[12],y,Xa):ze(F[12]),fs);const R={};36&y&&(R.options={lineNumbers:"off",wrappingIndent:"same",padding:F[5],wordWrap:F[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&y&&(R.text=F[3].text),24&y&&(R.lang=F[4]||F[3].lang),64&y&&(R.height=F[6]),!l&&1&y&&(l=!0,R.editorInstance=F[0],et(()=>l=!1)),o.$set(R),D&&D.p&&(!u||4096&y)&&Be(D,m,F,F[12],u?Le(m,F[12],y,Ya):ze(F[12]),ps)},i(F){u||(f(k,F),f(E,F),f(o.$$.fragment,F),f(D,F),u=!0)},o(F){$(k,F),$(E,F),$(o.$$.fragment,F),$(D,F),u=!1},d(F){F&&h(e),k&&k.d(F),E&&E.d(F),M(o),D&&D.d(F),r[17](null),d=!1,Us(p)}}}function tc(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{scroll:o=!1}=e,{token:l}=e,{language:a}=e,{padding:c={top:0,bottom:0}}=e,{editorInstance:u}=e,{element:d}=e,{height:p}=e;const w=jt.getContext().monaco;Re(r,w,_=>t(18,n=_));const k=Wt(),x=()=>{if(!u)return;const _=u.getSelections();if(!(_!=null&&_.length))return;const B=u.getModel();if(_.map(m=>(B==null?void 0:B.getValueLengthInRange(m))||0).reduce((m,D)=>m+D,0)!==0)return _.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(m=>(B==null?void 0:B.getValueInRange(m))||"").join(`
`)},E=()=>{if(u)return u.getValue()||""};return r.$$set=_=>{"scroll"in _&&t(2,o=_.scroll),"token"in _&&t(3,l=_.token),"language"in _&&t(4,a=_.language),"padding"in _&&t(5,c=_.padding),"editorInstance"in _&&t(0,u=_.editorInstance),"element"in _&&t(1,d=_.element),"height"in _&&t(6,p=_.height),"$$scope"in _&&t(12,i=_.$$scope)},r.$$.update=()=>{var _;32&r.$$.dirty&&(_=c,u==null||u.updateOptions({padding:_})),65&r.$$.dirty&&(u==null||u.updateOptions({scrollbar:{vertical:p!==void 0?"auto":"hidden"}}))},[u,d,o,l,a,c,p,w,k,()=>u&&(x()||E())||"",x,E,i,s,function(_){Ai.call(this,r,_)},()=>k.requestLayout(),function(_){u=_,t(0,u)},function(_){Te[_?"unshift":"push"](()=>{d=_,t(1,d)})}]}class gs extends te{constructor(e){super(),ne(this,e,tc,ec,K,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const nc=r=>({codespanContents:2&r}),hs=r=>({codespanContents:r[1]});function sc(r){let e,t,n;const s=r[4].default,i=_e(s,r,r[3],hs),o=i||function(l){let a;return{c(){a=N(l[1])},m(c,u){g(c,a,u)},p(c,u){2&u&&oe(a,c[1])},d(c){c&&h(a)}}}(r);return{c(){e=A("span"),t=A("code"),o&&o.c(),C(t,"class","markdown-codespan svelte-1dofrdh")},m(l,a){g(l,e,a),T(e,t),o&&o.m(t,null),r[5](e),n=!0},p(l,[a]){i?i.p&&(!n||10&a)&&Be(i,s,l,l[3],n?Le(s,l[3],a,nc):ze(l[3]),hs):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(f(o,l),n=!0)},o(l){$(o,l),n=!1},d(l){l&&h(e),o&&o.d(l),r[5](null)}}}function ic(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{token:o}=e,{element:l}=e;return r.$$set=a=>{"token"in a&&t(2,o=a.token),"element"in a&&t(0,l=a.element),"$$scope"in a&&t(3,i=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,i,s,function(a){Te[a?"unshift":"push"](()=>{l=a,t(0,l)})}]}class ms extends te{constructor(e){super(),ne(this,e,ic,sc,K,{token:2,element:0})}}function rc(r){let e,t,n,s,i=r[0].text+"";return{c(){e=A("span"),t=N("~"),n=N(i),s=N("~")},m(o,l){g(o,e,l),T(e,t),T(e,n),T(e,s)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&oe(n,i)},i:X,o:X,d(o){o&&h(e)}}}function oc(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n]}class Ds extends te{constructor(e){super(),ne(this,e,oc,rc,K,{token:0})}}function lc(r){let e,t;const n=r[1].default,s=_e(n,r,r[0],null);return{c(){e=A("p"),s&&s.c(),C(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(i,o){g(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||1&o)&&Be(s,n,i,i[0],t?Le(n,i[0],o,null):ze(i[0]),null)},i(i){t||(f(s,i),t=!0)},o(i){$(s,i),t=!1},d(i){i&&h(e),s&&s.d(i)}}}function ac(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(0,s=i.$$scope)},[s,n]}class Fs extends te{constructor(e){super(),ne(this,e,ac,lc,K,{})}}function cc(r){let e,t,n;return t=new Ja({props:{source:r[0],renderers:{codespan:ms,code:gs,paragraph:Fs,del:Ds,...r[1]}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c-markdown svelte-n6ddeo")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,[i]){const o={};1&i&&(o.source=s[0]),2&i&&(o.renderers={codespan:ms,code:gs,paragraph:Fs,del:Ds,...s[1]}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function uc(r,e,t){let{markdown:n}=e,{renderers:s={}}=e;return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown),"renderers"in i&&t(1,s=i.renderers)},[n,s]}class dc extends te{constructor(e){super(),ne(this,e,uc,cc,K,{markdown:0,renderers:1})}}function pc(r){let e;return{c(){e=N(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&oe(e,t[1])},d(t){t&&h(e)}}}function fc(r){let e;return{c(){e=N(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&oe(e,t[1])},d(t){t&&h(e)}}}function $c(r){let e,t,n;function s(l,a){return l[2]?fc:pc}let i=s(r),o=i(r);return{c(){e=A("span"),t=A("code"),o.c(),C(t,"class","markdown-codespan svelte-11ta4gi"),C(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ae(t,"markdown-string",r[4])},m(l,a){g(l,e,a),T(e,t),o.m(t,null),r[6](e)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&C(t,"style",n),16&a&&Ae(t,"markdown-string",l[4])},i:X,o:X,d(l){l&&h(e),o.d(),r[6](null)}}}function gc(r,e,t){let n,s,i,o,{token:l}=e,{element:a}=e;return r.$$set=c=>{"token"in c&&t(5,l=c.token),"element"in c&&t(0,a=c.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(c){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(c))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let u,d,p;return c.length===4?(u=parseInt(c.charAt(1),16),d=parseInt(c.charAt(2),16),p=parseInt(c.charAt(3),16),u*=17,d*=17,p*=17):(u=parseInt(c.slice(1,3),16),d=parseInt(c.slice(3,5),16),p=parseInt(c.slice(5,7),16)),.299*u+.587*d+.114*p<130}(n))},[a,n,i,o,s,l,function(c){Te[c?"unshift":"push"](()=>{a=c,t(0,a)})}]}class hc extends te{constructor(e){super(),ne(this,e,gc,$c,K,{token:5,element:0})}}function mc(r){let e,t;return e=new dc({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Dc(r,e,t){let{markdown:n}=e;const s={codespan:hc};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}class Fc extends te{constructor(e){super(),ne(this,e,Dc,mc,K,{markdown:0})}}function Cs(r,e,t){const n=r.slice();return n[4]=e[t],n}function Cc(r){let e;return{c(){e=N(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`)},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function xc(r){let e;return{c(){e=N("The following files have merge conflicts that need to be resolved manually.")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function wc(r){let e,t,n,s;return e=new ni({props:{filename:r[4]}}),n=new Vi({props:{filepath:r[4]}}),{c(){z(e.$$.fragment),t=I(),z(n.$$.fragment)},m(i,o){L(e,i,o),g(i,t,o),L(n,i,o),s=!0},p(i,o){const l={};1&o&&(l.filename=i[4]),e.$set(l);const a={};1&o&&(a.filepath=i[4]),n.$set(a)},i(i){s||(f(e.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),$(n.$$.fragment,i),s=!1},d(i){i&&h(t),M(e,i),M(n,i)}}}function kc(r){let e,t;return e=new Ht({}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function yc(r){let e,t;return e=new pt({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[kc]},$$scope:{ctx:r}}}),e.$on("click",function(){return r[3](r[4])}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){r=n;const i={};128&s&&(i.$$scope={dirty:s,ctx:r}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function xs(r){let e,t,n,s,i,o;return t=new Ve({props:{content:r[4],nested:!0,$$slots:{default:[wc]},$$scope:{ctx:r}}}),s=new Ve({props:{content:"Open file",$$slots:{default:[yc]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),z(s.$$.fragment),i=I(),C(e,"class","c-conflicts-card__file svelte-1bce35u")},m(l,a){g(l,e,a),L(t,e,null),T(e,n),L(s,e,null),T(e,i),o=!0},p(l,a){const c={};1&a&&(c.content=l[4]),129&a&&(c.$$scope={dirty:a,ctx:l}),t.$set(c);const u={};133&a&&(u.$$scope={dirty:a,ctx:l}),s.$set(u)},i(l){o||(f(t.$$.fragment,l),f(s.$$.fragment,l),o=!0)},o(l){$(t.$$.fragment,l),$(s.$$.fragment,l),o=!1},d(l){l&&h(e),M(t),M(s)}}}function vc(r){let e,t,n,s,i,o,l,a,c,u,d,p,w,k=r[0].size+"";function x(F,y){return F[1]?xc:Cc}n=new Js({});let E=x(r),_=E(r),B=pe(r[0]),m=[];for(let F=0;F<B.length;F+=1)m[F]=xs(Cs(r,B,F));const D=F=>$(m[F],1,1,()=>{m[F]=null});return{c(){e=A("div"),t=A("div"),z(n.$$.fragment),s=I(),i=A("span"),o=N("Conflicts ("),l=N(k),a=N(")"),c=I(),u=A("div"),_.c(),d=I();for(let F=0;F<m.length;F+=1)m[F].c();p=we(),C(t,"class","c-conflicts-card__icon svelte-1bce35u"),C(i,"class","c-conflicts-card__title svelte-1bce35u"),C(e,"class","c-conflicts-card__header svelte-1bce35u"),C(u,"class","c-conflicts-card__description svelte-1bce35u")},m(F,y){g(F,e,y),T(e,t),L(n,t,null),T(e,s),T(e,i),T(i,o),T(i,l),T(i,a),g(F,c,y),g(F,u,y),_.m(u,null),g(F,d,y);for(let R=0;R<m.length;R+=1)m[R]&&m[R].m(F,y);g(F,p,y),w=!0},p(F,y){if((!w||1&y)&&k!==(k=F[0].size+"")&&oe(l,k),E!==(E=x(F))&&(_.d(1),_=E(F),_&&(_.c(),_.m(u,null))),5&y){let R;for(B=pe(F[0]),R=0;R<B.length;R+=1){const q=Cs(F,B,R);m[R]?(m[R].p(q,y),f(m[R],1)):(m[R]=xs(q),m[R].c(),f(m[R],1),m[R].m(p.parentNode,p))}for(H(),R=B.length;R<m.length;R+=1)D(R);j()}},i(F){if(!w){f(n.$$.fragment,F);for(let y=0;y<B.length;y+=1)f(m[y]);w=!0}},o(F){$(n.$$.fragment,F),m=m.filter(Boolean);for(let y=0;y<m.length;y+=1)$(m[y]);w=!1},d(F){F&&(h(e),h(c),h(u),h(d),h(p)),M(n),_.d(),Me(m,F)}}}function Ac(r){let e,t,n;return t=new Pi({props:{includeBackground:!1,$$slots:{default:[vc]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c-conflicts-card")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,[i]){const o={};135&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function bc(r,e,t){let{files:n}=e,{hasAppliedAll:s}=e,{onOpenFile:i}=e;return r.$$set=o=>{"files"in o&&t(0,n=o.files),"hasAppliedAll"in o&&t(1,s=o.hasAppliedAll),"onOpenFile"in o&&t(2,i=o.onOpenFile)},[n,s,i,o=>i==null?void 0:i(o)]}class Ec extends te{constructor(e){super(),ne(this,e,bc,Ac,K,{files:0,hasAppliedAll:1,onOpenFile:2})}}function _c(r){let e,t,n,s;return t=new Ni({props:{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}}),{c(){e=N(`There are unstaged changes in your working directory. Please commit your changes or we will
      run
      `),z(t.$$.fragment),n=N(`
      to stash your changes before applying changes from the remote agent.`)},m(i,o){g(i,e,o),L(t,i,o),g(i,n,o),s=!0},p:X,i(i){s||(f(t.$$.fragment,i),s=!0)},o(i){$(t.$$.fragment,i),s=!1},d(i){i&&(h(e),h(n)),M(t,i)}}}function ws(r){let e,t;return e=new he({props:{$$slots:{default:[Bc]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};130&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Bc(r){let e;return{c(){e=N(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&oe(e,t[1])},d(t){t&&h(e)}}}function zc(r){let e,t,n,s;t=new he({props:{$$slots:{default:[_c]},$$scope:{ctx:r}}});let i=r[1]&&ws(r);return{c(){e=A("div"),z(t.$$.fragment),n=I(),i&&i.c(),C(e,"class","c-unstaged-changes-modal__body svelte-9eyy34")},m(o,l){g(o,e,l),L(t,e,null),T(e,n),i&&i.m(e,null),s=!0},p(o,l){const a={};128&l&&(a.$$scope={dirty:l,ctx:o}),t.$set(a),o[1]?i?(i.p(o,l),2&l&&f(i,1)):(i=ws(o),i.c(),f(i,1),i.m(e,null)):i&&(H(),$(i,1,1,()=>{i=null}),j())},i(o){s||(f(t.$$.fragment,o),f(i),s=!0)},o(o){$(t.$$.fragment,o),$(i),s=!1},d(o){o&&h(e),M(t),i&&i.d()}}}function ks(r){let e,t,n;return t=new zt({props:{size:1}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c-unstaged-changes-modal__stash-button-loading svelte-9eyy34")},m(s,i){g(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function Lc(r){let e,t,n,s=r[2]&&ks();return{c(){s&&s.c(),e=I(),t=A("span"),t.textContent="Stash & Apply Locally",C(t,"class","c-unstaged-changes-modal__stash-button-text svelte-9eyy34"),Ae(t,"loading",r[2])},m(i,o){s&&s.m(i,o),g(i,e,o),g(i,t,o),n=!0},p(i,o){i[2]?s?4&o&&f(s,1):(s=ks(),s.c(),f(s,1),s.m(e.parentNode,e)):s&&(H(),$(s,1,1,()=>{s=null}),j()),(!n||4&o)&&Ae(t,"loading",i[2])},i(i){n||(f(s),n=!0)},o(i){$(s),n=!1},d(i){i&&(h(e),h(t)),s&&s.d(i)}}}function Mc(r){let e;return{c(){e=N("Abort")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Rc(r){let e,t,n,s,i;return t=new Pe({props:{variant:"solid",color:"accent",disabled:!!r[1]||r[2],$$slots:{default:[Lc]},$$scope:{ctx:r}}}),t.$on("click",r[3]),s=new Pe({props:{variant:"solid",color:"neutral",disabled:r[2],$$slots:{default:[Mc]},$$scope:{ctx:r}}}),s.$on("click",r[4]),{c(){e=A("div"),z(t.$$.fragment),n=I(),z(s.$$.fragment),C(e,"class","c-unstaged-changes-modal__footer svelte-9eyy34"),C(e,"slot","footer")},m(o,l){g(o,e,l),L(t,e,null),T(e,n),L(s,e,null),i=!0},p(o,l){const a={};6&l&&(a.disabled=!!o[1]||o[2]),132&l&&(a.$$scope={dirty:l,ctx:o}),t.$set(a);const c={};4&l&&(c.disabled=o[2]),128&l&&(c.$$scope={dirty:l,ctx:o}),s.$set(c)},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&h(e),M(t),M(s)}}}function Tc(r){let e,t;return e=new Hi({props:{show:r[0],title:"Unstaged changes",$$slots:{footer:[Rc],default:[zc]},$$scope:{ctx:r}}}),e.$on("cancel",r[4]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.show=n[0]),134&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Nc(r,e,t){let{showModal:n=!1}=e,{applyAllChanges:s}=e;const i=Xe(it.key);let o,l=!1;return r.$$set=a=>{"showModal"in a&&t(0,n=a.showModal),"applyAllChanges"in a&&t(5,s=a.applyAllChanges)},[n,o,l,async function(){if(t(2,l=!0),!await i.stashUnstagedChanges())return t(1,o="Failed to stash changes. Please manually stash or commit your unstaged changes."),void t(2,l=!1);await new Promise(a=>setTimeout(a,1500)),t(1,o=void 0),t(0,n=!1),s(),t(2,l=!1)},function(){t(0,n=!1),t(1,o=void 0)},s]}class qc extends te{constructor(e){super(),ne(this,e,Nc,Tc,K,{showModal:0,applyAllChanges:5})}}const{Boolean:Fi,Map:Oc}=_i;function ys(r,e,t){const n=r.slice();return n[55]=e[t],n[56]=e,n[57]=t,n}function vs(r,e,t){const n=r.slice();return n[58]=e[t],n[59]=e,n[60]=t,n}function As(r,e,t){const n=r.slice();return n[61]=e[t],n[62]=e,n[63]=t,n}function Ot(r){const e=r.slice(),t=e[11]?e[6]:e[23];return e[64]=t,e}function bs(r){let e,t,n,s,i,o,l,a;t=new si({}),o=new Pe({props:{variant:"ghost",size:1,$$slots:{default:[Sc]},$$scope:{ctx:r}}}),o.$on("click",r[34]);let c=r[4]&&Es(r);return{c(){e=A("div"),z(t.$$.fragment),n=I(),s=N(r[20]),i=I(),z(o.$$.fragment),l=I(),c&&c.c(),C(e,"class","c-diff-view__error svelte-ibi4q5")},m(u,d){g(u,e,d),L(t,e,null),T(e,n),T(e,s),T(e,i),L(o,e,null),T(e,l),c&&c.m(e,null),a=!0},p(u,d){(!a||1048576&d[0])&&oe(s,u[20]);const p={};8&d[2]&&(p.$$scope={dirty:d,ctx:u}),o.$set(p),u[4]?c?(c.p(u,d),16&d[0]&&f(c,1)):(c=Es(u),c.c(),f(c,1),c.m(e,null)):c&&(H(),$(c,1,1,()=>{c=null}),j())},i(u){a||(f(t.$$.fragment,u),f(o.$$.fragment,u),f(c),a=!0)},o(u){$(t.$$.fragment,u),$(o.$$.fragment,u),$(c),a=!1},d(u){u&&h(e),M(t),M(o),c&&c.d()}}}function Sc(r){let e;return{c(){e=N("Retry")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Es(r){let e,t;return e=new Pe({props:{variant:"ghost",size:1,$$slots:{default:[Pc]},$$scope:{ctx:r}}}),e.$on("click",function(){bi(r[4])&&r[4].apply(this,arguments)}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){r=n;const i={};8&s[2]&&(i.$$scope={dirty:s,ctx:r}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Pc(r){let e;return{c(){e=N("Render as list")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Ic(r){let e,t,n,s,i,o,l,a,c,u,d,p,w,k=r[1]&&r[2]!==r[1]&&_s(r),x=r[2]&&Bs(r);o=new he({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Wc]},$$scope:{ctx:r}}}),a=new ui({props:{changedFiles:r[0]}});const E=[Gc,Qc],_=[];function B(m,D){return m[18]&&m[17].length===0?0:m[7]&&m[7].length>0?1:-1}return~(d=B(r))&&(p=_[d]=E[d](r)),{c(){e=A("div"),t=A("div"),n=A("div"),k&&k.c(),s=I(),x&&x.c(),i=I(),z(o.$$.fragment),l=I(),z(a.$$.fragment),c=I(),u=A("div"),p&&p.c(),C(n,"class","c-diff-view__tree__header svelte-ibi4q5"),C(t,"class","c-diff-view__tree svelte-ibi4q5"),C(u,"class","c-diff-view__explanation svelte-ibi4q5"),C(e,"class","c-diff-view__layout svelte-ibi4q5")},m(m,D){g(m,e,D),T(e,t),T(t,n),k&&k.m(n,null),T(n,s),x&&x.m(n,null),T(n,i),L(o,n,null),T(n,l),L(a,n,null),T(e,c),T(e,u),~d&&_[d].m(u,null),w=!0},p(m,D){m[1]&&m[2]!==m[1]?k?(k.p(m,D),6&D[0]&&f(k,1)):(k=_s(m),k.c(),f(k,1),k.m(n,s)):k&&(H(),$(k,1,1,()=>{k=null}),j()),m[2]?x?(x.p(m,D),4&D[0]&&f(x,1)):(x=Bs(m),x.c(),f(x,1),x.m(n,i)):x&&(H(),$(x,1,1,()=>{x=null}),j());const F={};8&D[2]&&(F.$$scope={dirty:D,ctx:m}),o.$set(F);const y={};1&D[0]&&(y.changedFiles=m[0]),a.$set(y);let R=d;d=B(m),d===R?~d&&_[d].p(m,D):(p&&(H(),$(_[R],1,1,()=>{_[R]=null}),j()),~d?(p=_[d],p?p.p(m,D):(p=_[d]=E[d](m),p.c()),f(p,1),p.m(u,null)):p=null)},i(m){w||(f(k),f(x),f(o.$$.fragment,m),f(a.$$.fragment,m),f(p),w=!0)},o(m){$(k),$(x),$(o.$$.fragment,m),$(a.$$.fragment,m),$(p),w=!1},d(m){m&&h(e),k&&k.d(),x&&x.d(),M(o),M(a),~d&&_[d].d()}}}function Uc(r){let e,t,n;return t=new he({props:{size:2,color:"secondary",$$slots:{default:[xu]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c-diff-view__empty svelte-ibi4q5")},m(s,i){g(s,e,i),L(t,e,null),n=!0},p(s,i){const o={};8&i[2]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function _s(r){let e,t,n,s;return e=new he({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Zc]},$$scope:{ctx:r}}}),n=new he({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Vc]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=I(),z(n.$$.fragment)},m(i,o){L(e,i,o),g(i,t,o),L(n,i,o),s=!0},p(i,o){const l={};8&o[2]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const a={};2&o[0]|8&o[2]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(f(e.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),$(n.$$.fragment,i),s=!1},d(i){i&&h(t),M(e,i),M(n,i)}}}function Zc(r){let e;return{c(){e=N("Changes from agent")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Vc(r){let e;return{c(){e=N(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n[0]&&oe(e,t[1])},d(t){t&&h(e)}}}function Bs(r){let e,t,n,s;return e=new he({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Hc]},$$scope:{ctx:r}}}),n=new he({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[jc]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment),t=I(),z(n.$$.fragment)},m(i,o){L(e,i,o),g(i,t,o),L(n,i,o),s=!0},p(i,o){const l={};8&o[2]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const a={};4&o[0]|8&o[2]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(f(e.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),$(n.$$.fragment,i),s=!1},d(i){i&&h(t),M(e,i),M(n,i)}}}function Hc(r){let e;return{c(){e=N("Last user prompt")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function jc(r){let e;return{c(){e=N(r[2])},m(t,n){g(t,e,n)},p(t,n){4&n[0]&&oe(e,t[2])},d(t){t&&h(e)}}}function Wc(r){let e;return{c(){e=N("Changed files")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Qc(r){let e,t,n=pe(r[7]),s=[];for(let o=0;o<n.length;o+=1)s[o]=Ts(ys(r,n,o));const i=o=>$(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=we()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(1793708008&l[0]|3&l[1]){let a;for(n=pe(o[7]),a=0;a<n.length;a+=1){const c=ys(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=Ts(c),s[a].c(),f(s[a],1),s[a].m(e.parentNode,e))}for(H(),a=n.length;a<s.length;a+=1)i(a);j()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(s[l]);t=!0}},o(o){s=s.filter(Fi);for(let l=0;l<s.length;l+=1)$(s[l]);t=!1},d(o){o&&h(e),Me(s,o)}}}function Gc(r){let e,t,n,s,i;return t=new Ve({props:{content:r[10]?"Applying changes...":r[11]?"All changes applied":r[12]?"Apply all changes":"No changes to apply",$$slots:{default:[Cu]},$$scope:{ctx:r}}}),s=new ul({props:{count:2}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),z(s.$$.fragment),C(e,"class","c-diff-view__controls svelte-ibi4q5")},m(o,l){g(o,e,l),L(t,e,null),g(o,n,l),L(s,o,l),i=!0},p(o,l){const a={};7168&l[0]&&(a.content=o[10]?"Applying changes...":o[11]?"All changes applied":o[12]?"Apply all changes":"No changes to apply"),15360&l[0]|8&l[2]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&(h(e),h(n)),M(t),M(s,o)}}}function Jc(r){let e,t=r[55].title+"";return{c(){e=N(t)},m(n,s){g(n,e,s)},p(n,s){128&s[0]&&t!==(t=n[55].title+"")&&oe(e,t)},d(n){n&&h(e)}}}function Yc(r){let e;return{c(){e=A("div"),C(e,"class","c-diff-view__skeleton-title svelte-ibi4q5")},m(t,n){g(t,e,n)},p:X,d(t){t&&h(e)}}}function Xc(r){let e,t;return e=new Fc({props:{markdown:r[55].description}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};128&s[0]&&(i.markdown=n[55].description),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Kc(r){let e,t,n;return{c(){e=A("div"),t=I(),n=A("div"),C(e,"class","c-diff-view__skeleton-text svelte-ibi4q5"),C(n,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(s,i){g(s,e,i),g(s,t,i),g(s,n,i)},p:X,i:X,o:X,d(s){s&&(h(e),h(t),h(n))}}}function eu(r){let e,t,n;return e=new pl({}),{c(){z(e.$$.fragment),t=N(`
                        Expand All`)},m(s,i){L(e,s,i),g(s,t,i),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&h(t),M(e,s)}}}function tu(r){let e,t,n;return e=new qi({}),{c(){z(e.$$.fragment),t=N(`
                        Collapse All`)},m(s,i){L(e,s,i),g(s,t,i),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&h(t),M(e,s)}}}function nu(r){let e,t,n,s;const i=[tu,eu],o=[];function l(a,c){return a[27]?1:0}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e!==u&&(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function su(r){let e,t,n,s;return n=new ft({}),{c(){e=N(`Apply All
                          `),t=A("div"),z(n.$$.fragment),C(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(i,o){g(i,e,o),g(i,t,o),L(n,t,null),s=!0},p:X,i(i){s||(f(n.$$.fragment,i),s=!0)},o(i){$(n.$$.fragment,i),s=!1},d(i){i&&(h(e),h(t)),M(n)}}}function iu(r){let e,t,n,s,i,o;t=new he({props:{size:2,$$slots:{default:[ou]},$$scope:{ctx:r}}});const l=[au,lu],a=[];function c(u,d){return u[14]?0:1}return s=c(r),i=a[s]=l[s](r),{c(){e=A("div"),z(t.$$.fragment),n=I(),i.c(),C(e,"class","c-diff-view__applied svelte-ibi4q5")},m(u,d){g(u,e,d),L(t,e,null),T(e,n),a[s].m(e,null),o=!0},p(u,d){const p={};8&d[2]&&(p.$$scope={dirty:d,ctx:u}),t.$set(p);let w=s;s=c(u),s!==w&&(H(),$(a[w],1,1,()=>{a[w]=null}),j(),i=a[s],i||(i=a[s]=l[s](u),i.c()),f(i,1),i.m(e,null))},i(u){o||(f(t.$$.fragment,u),f(i),o=!0)},o(u){$(t.$$.fragment,u),$(i),o=!1},d(u){u&&h(e),M(t),a[s].d()}}}function ru(r){let e,t,n,s,i;return t=new zt({props:{size:1,useCurrentColor:!0}}),s=new he({props:{size:2,$$slots:{default:[cu]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),z(s.$$.fragment),C(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),L(t,e,null),T(e,n),L(s,e,null),i=!0},p(o,l){const a={};8&l[2]&&(a.$$scope={dirty:l,ctx:o}),s.$set(a)},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&h(e),M(t),M(s)}}}function ou(r){let e;return{c(){e=N("Applied")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function lu(r){let e,t;return e=new Lt({props:{iconName:"check"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function au(r){let e,t;return e=new Js({props:{slot:"rightIcon"}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function cu(r){let e;return{c(){e=N("Applying...")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function uu(r){let e,t,n,s;const i=[ru,iu,su],o=[];function l(a,c){return a[10]?0:a[11]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e===u?o[e].p(a,c):(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t?t.p(a,c):(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function du(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[15],$$slots:{default:[uu]},$$scope:{ctx:r}}}),e.$on("click",r[32]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};32768&s[0]&&(i.disabled=n[15]),19456&s[0]|8&s[2]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function zs(r){let e,t;return e=new Ec({props:{files:r[64],hasAppliedAll:r[11],onOpenFile:r[3]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};8390720&s[0]&&(i.files=n[64]),2048&s[0]&&(i.hasAppliedAll=n[11]),8&s[0]&&(i.onOpenFile=n[3]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function pu(r){let e,t=r[58].title+"";return{c(){e=N(t)},m(n,s){g(n,e,s)},p(n,s){128&s[0]&&t!==(t=n[58].title+"")&&oe(e,t)},d(n){n&&h(e)}}}function fu(r){let e;return{c(){e=A("div"),C(e,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(t,n){g(t,e,n)},p:X,d(t){t&&h(e)}}}function Ls(r){let e,t,n,s,i,o=r[58].warning+"";return t=new si({}),{c(){e=A("div"),z(t.$$.fragment),n=I(),s=N(o),C(e,"class","c-diff-view__warning svelte-ibi4q5")},m(l,a){g(l,e,a),L(t,e,null),T(e,n),T(e,s),i=!0},p(l,a){(!i||128&a[0])&&o!==(o=l[58].warning+"")&&oe(s,o)},i(l){i||(f(t.$$.fragment,l),i=!0)},o(l){$(t.$$.fragment,l),i=!1},d(l){l&&h(e),M(t)}}}function Ms(r,e){let t,n,s,i,o,l=e[57],a=e[60],c=e[61];function u(...B){return e[42](e[61],...B)}function d(){return e[43](e[61])}function p(){return e[44](e[61])}function w(B){e[45](B,e[61])}const k=()=>e[46](n,l,a,c),x=()=>e[46](null,l,a,c);function E(B){e[47](B)}let _={path:e[61].path,change:e[61],descriptions:e[58].descriptions,isExpandedDefault:e[9][e[61].path]!==void 0?!e[9][e[61].path]:e[8],isApplying:e[16][e[61].path]==="pending",hasApplied:e[16][e[61].path]==="applied",onCodeChange:u,onApplyChanges:d,onOpenFile:e[3]?p:void 0,isAgentFromDifferentRepo:e[5]};return e[9][e[61].path]!==void 0&&(_.isCollapsed=e[9][e[61].path]),e[22]!==void 0&&(_.areDescriptionsVisible=e[22]),n=new sl({props:_}),Te.push(()=>Ke(n,"isCollapsed",w)),k(),Te.push(()=>Ke(n,"areDescriptionsVisible",E)),{key:r,first:null,c(){t=A("div"),z(n.$$.fragment),C(t,"class","c-diff-view__changes-item svelte-ibi4q5"),this.first=t},m(B,m){g(B,t,m),L(n,t,null),o=!0},p(B,m){l===(e=B)[57]&&a===e[60]&&c===e[61]||(x(),l=e[57],a=e[60],c=e[61],k());const D={};128&m[0]&&(D.path=e[61].path),128&m[0]&&(D.change=e[61]),128&m[0]&&(D.descriptions=e[58].descriptions),896&m[0]&&(D.isExpandedDefault=e[9][e[61].path]!==void 0?!e[9][e[61].path]:e[8]),65664&m[0]&&(D.isApplying=e[16][e[61].path]==="pending"),65664&m[0]&&(D.hasApplied=e[16][e[61].path]==="applied"),128&m[0]&&(D.onCodeChange=u),128&m[0]&&(D.onApplyChanges=d),136&m[0]&&(D.onOpenFile=e[3]?p:void 0),32&m[0]&&(D.isAgentFromDifferentRepo=e[5]),!s&&640&m[0]&&(s=!0,D.isCollapsed=e[9][e[61].path],et(()=>s=!1)),!i&&4194304&m[0]&&(i=!0,D.areDescriptionsVisible=e[22],et(()=>i=!1)),n.$set(D)},i(B){o||(f(n.$$.fragment,B),o=!0)},o(B){$(n.$$.fragment,B),o=!1},d(B){B&&h(t),x(),M(n)}}}function Rs(r){let e,t,n,s,i,o,l,a,c,u,d,p=[],w=new Oc;function k(D,F){return D[19]&&D[58].descriptions.length===0?fu:pu}i=new $o({props:{type:r[58].type}});let x=k(r),E=x(r),_=!r[19]&&r[58].warning&&Ls(r),B=pe(r[58].changes);const m=D=>D[61].id;for(let D=0;D<B.length;D+=1){let F=As(r,B,D),y=m(F);w.set(y,p[D]=Ms(y,F))}return{c(){e=A("div"),t=A("div"),n=A("div"),s=A("div"),z(i.$$.fragment),o=I(),l=A("h5"),E.c(),a=I(),_&&_.c(),c=I(),u=A("div");for(let D=0;D<p.length;D+=1)p[D].c();C(s,"class","c-diff-view__icon svelte-ibi4q5"),C(l,"class","c-diff-view__title svelte-ibi4q5"),C(n,"class","c-diff-view__content svelte-ibi4q5"),C(t,"class","c-diff-view__header svelte-ibi4q5"),C(u,"class","c-diff-view__changes svelte-ibi4q5"),C(e,"class","c-diff-view__subsection svelte-ibi4q5"),C(e,"id",`subsection-${r[57]}-${r[60]}`)},m(D,F){g(D,e,F),T(e,t),T(t,n),T(n,s),L(i,s,null),T(n,o),T(n,l),E.m(l,null),T(n,a),_&&_.m(n,null),T(e,c),T(e,u);for(let y=0;y<p.length;y+=1)p[y]&&p[y].m(u,null);d=!0},p(D,F){const y={};128&F[0]&&(y.type=D[58].type),i.$set(y),x===(x=k(D))&&E?E.p(D,F):(E.d(1),E=x(D),E&&(E.c(),E.m(l,null))),!D[19]&&D[58].warning?_?(_.p(D,F),524416&F[0]&&f(_,1)):(_=Ls(D),_.c(),f(_,1),_.m(n,null)):_&&(H(),$(_,1,1,()=>{_=null}),j()),1080099752&F[0]|1&F[1]&&(B=pe(D[58].changes),H(),p=Hs(p,F,m,1,D,B,w,u,js,Ms,null,As),j())},i(D){if(!d){f(i.$$.fragment,D),f(_);for(let F=0;F<B.length;F+=1)f(p[F]);d=!0}},o(D){$(i.$$.fragment,D),$(_);for(let F=0;F<p.length;F+=1)$(p[F]);d=!1},d(D){D&&h(e),M(i),E.d(),_&&_.d();for(let F=0;F<p.length;F+=1)p[F].d()}}}function Ts(r){let e,t,n,s,i,o,l,a,c,u,d,p,w;function k(O,V){return O[19]&&O[55].title==="Loading..."?Yc:Jc}let x=k(r),E=x(r);const _=[Kc,Xc],B=[];function m(O,V){return O[19]&&O[55].description===""?0:1}l=m(r),a=B[l]=_[l](r);let D=r[57]===0&&function(O){let V,W,ee,de,ce;return W=new Pe({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[nu]},$$scope:{ctx:O}}}),W.$on("click",O[29]),de=new Ve({props:{content:O[25],$$slots:{default:[du]},$$scope:{ctx:O}}}),{c(){V=A("div"),z(W.$$.fragment),ee=I(),z(de.$$.fragment),C(V,"class","c-diff-view__controls svelte-ibi4q5")},m(ae,J){g(ae,V,J),L(W,V,null),T(V,ee),L(de,V,null),ce=!0},p(ae,J){const $e={};134217728&J[0]|8&J[2]&&($e.$$scope={dirty:J,ctx:ae}),W.$set($e);const De={};33554432&J[0]&&(De.content=ae[25]),52224&J[0]|8&J[2]&&(De.$$scope={dirty:J,ctx:ae}),de.$set(De)},i(ae){ce||(f(W.$$.fragment,ae),f(de.$$.fragment,ae),ce=!0)},o(ae){$(W.$$.fragment,ae),$(de.$$.fragment,ae),ce=!1},d(ae){ae&&h(V),M(W),M(de)}}}(r),F=(r[11]&&r[6].size>0||!r[11]&&r[23].size>0)&&r[57]===0&&zs(Ot(r)),y=pe(r[55].sections||[]),R=[];for(let O=0;O<y.length;O+=1)R[O]=Rs(vs(r,y,O));const q=O=>$(R[O],1,1,()=>{R[O]=null});return{c(){e=A("div"),t=A("div"),n=A("div"),s=A("h5"),E.c(),i=I(),o=A("div"),a.c(),c=I(),D&&D.c(),u=I(),F&&F.c(),d=I();for(let O=0;O<R.length;O+=1)R[O].c();p=I(),C(s,"class","c-diff-view__title svelte-ibi4q5"),C(o,"class","c-diff-view__description svelte-ibi4q5"),C(n,"class","c-diff-view__content svelte-ibi4q5"),C(t,"class","c-diff-view__header svelte-ibi4q5"),C(e,"class","c-diff-view__section svelte-ibi4q5"),C(e,"id",`section-${r[57]}`)},m(O,V){g(O,e,V),T(e,t),T(t,n),T(n,s),E.m(s,null),T(n,i),T(n,o),B[l].m(o,null),T(t,c),D&&D.m(t,null),T(e,u),F&&F.m(e,null),T(e,d);for(let W=0;W<R.length;W+=1)R[W]&&R[W].m(e,null);T(e,p),w=!0},p(O,V){x===(x=k(O))&&E?E.p(O,V):(E.d(1),E=x(O),E&&(E.c(),E.m(s,null)));let W=l;if(l=m(O),l===W?B[l].p(O,V):(H(),$(B[W],1,1,()=>{B[W]=null}),j(),a=B[l],a?a.p(O,V):(a=B[l]=_[l](O),a.c()),f(a,1),a.m(o,null)),O[57]===0&&D.p(O,V),(O[11]&&O[6].size>0||!O[11]&&O[23].size>0)&&O[57]===0?F?(F.p(Ot(O),V),8390720&V[0]&&f(F,1)):(F=zs(Ot(O)),F.c(),f(F,1),F.m(e,d)):F&&(H(),$(F,1,1,()=>{F=null}),j()),1080624040&V[0]|1&V[1]){let ee;for(y=pe(O[55].sections||[]),ee=0;ee<y.length;ee+=1){const de=vs(O,y,ee);R[ee]?(R[ee].p(de,V),f(R[ee],1)):(R[ee]=Rs(de),R[ee].c(),f(R[ee],1),R[ee].m(e,p))}for(H(),ee=y.length;ee<R.length;ee+=1)q(ee);j()}},i(O){if(!w){f(a),f(D),f(F);for(let V=0;V<y.length;V+=1)f(R[V]);w=!0}},o(O){$(a),$(D),$(F),R=R.filter(Fi);for(let V=0;V<R.length;V+=1)$(R[V]);w=!1},d(O){O&&h(e),E.d(),B[l].d(),D&&D.d(),F&&F.d(),Me(R,O)}}}function $u(r){let e,t,n,s;return n=new ft({}),{c(){e=N(`Apply All
                  `),t=A("div"),z(n.$$.fragment),C(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(i,o){g(i,e,o),g(i,t,o),L(n,t,null),s=!0},i(i){s||(f(n.$$.fragment,i),s=!0)},o(i){$(n.$$.fragment,i),s=!1},d(i){i&&(h(e),h(t)),M(n)}}}function gu(r){let e,t,n;return t=new he({props:{size:2,$$slots:{default:[mu]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),C(e,"class","c-diff-view__applied svelte-ibi4q5")},m(s,i){g(s,e,i),L(t,e,null),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t)}}}function hu(r){let e,t,n,s,i;return t=new zt({props:{size:1,useCurrentColor:!0}}),s=new he({props:{size:2,$$slots:{default:[Du]},$$scope:{ctx:r}}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),z(s.$$.fragment),C(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),L(t,e,null),T(e,n),L(s,e,null),i=!0},i(o){i||(f(t.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&h(e),M(t),M(s)}}}function mu(r){let e,t,n;return t=new Lt({props:{iconName:"check"}}),{c(){e=N(`Applied
                      `),z(t.$$.fragment)},m(s,i){g(s,e,i),L(t,s,i),n=!0},p:X,i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){s&&h(e),M(t,s)}}}function Du(r){let e;return{c(){e=N("Applying...")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function Fu(r){let e,t,n,s;const i=[hu,gu,$u],o=[];function l(a,c){return a[10]?0:a[11]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e!==u&&(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t||(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function Cu(r){let e,t;return e=new Pe({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[10]||r[11]||r[13].length>0||!r[12],$$slots:{default:[Fu]},$$scope:{ctx:r}}}),e.$on("click",r[32]),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};15360&s[0]&&(i.disabled=n[10]||n[11]||n[13].length>0||!n[12]),3072&s[0]|8&s[2]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function xu(r){let e;return{c(){e=N("No files changed")},m(t,n){g(t,e,n)},d(t){t&&h(e)}}}function wu(r){let e,t,n,s,i,o,l,a,c=r[20]&&bs(r);const u=[Uc,Ic],d=[];function p(x,E){return x[26]?0:1}function w(x){r[48](x)}n=p(r),s=d[n]=u[n](r);let k={applyAllChanges:r[33]};return r[24]!==void 0&&(k.showModal=r[24]),o=new qc({props:k}),Te.push(()=>Ke(o,"showModal",w)),{c(){e=A("div"),c&&c.c(),t=I(),s.c(),i=I(),z(o.$$.fragment),C(e,"class","c-diff-view svelte-ibi4q5")},m(x,E){g(x,e,E),c&&c.m(e,null),T(e,t),d[n].m(e,null),g(x,i,E),L(o,x,E),a=!0},p(x,E){x[20]?c?(c.p(x,E),1048576&E[0]&&f(c,1)):(c=bs(x),c.c(),f(c,1),c.m(e,t)):c&&(H(),$(c,1,1,()=>{c=null}),j());let _=n;n=p(x),n===_?d[n].p(x,E):(H(),$(d[_],1,1,()=>{d[_]=null}),j(),s=d[n],s?s.p(x,E):(s=d[n]=u[n](x),s.c()),f(s,1),s.m(e,null));const B={};!l&&16777216&E[0]&&(l=!0,B.showModal=x[24],et(()=>l=!1)),o.$set(B)},i(x){a||(f(c),f(s),f(o.$$.fragment,x),a=!0)},o(x){$(c),$(s),$(o.$$.fragment,x),a=!1},d(x){x&&(h(e),h(i)),c&&c.d(),d[n].d(),M(o,x)}}}function ku(r,e,t){let n,s,i,o,l,a,c,u,d,{changedFiles:p}=e,{agentLabel:w}=e,{latestUserPrompt:k}=e,{onApplyChanges:x}=e,{onOpenFile:E}=e,{onRenderBackup:_}=e,{preloadedExplanation:B}=e,{isAgentFromDifferentRepo:m=!1}=e,{conflictFiles:D=new Set}=e;const F=Xe(it.key);let y="",R=!1,q=[],O=[],V=!1,W=!1,ee=null,de=!0,ce={},ae=[],J=!1,$e=!1,De=!0,ie=new Set,ve=!1;const ke=Ye({});Re(r,ke,b=>t(16,d=b));let xe={};function Ne(b,U){t(38,xe[b]=U,xe)}async function fe(b,U,Z){if(x)return ke.update(Q=>(Q[b]="pending",Q)),new Promise(Q=>{x==null||x(b,U,Z).then(()=>{ke.update(se=>(se[b]="applied",se)),Q()})})}function be(){if(!x)return;F.reportApplyChangesEvent(),t(10,J=!0),t(11,$e=!1);const{filesToApply:b,areAllPathsApplied:U}=rn(q,p,xe);U||b.length===0?t(11,$e=U):Ei(b,fe).then(()=>{t(10,J=!1),t(11,$e=!0)})}function ye(b){const U={title:"Changed Files",description:`${b.length} files were changed`,sections:[]},Z=[],Q=[],se=[];return b.forEach(G=>{G.old_path?G.new_path?Q.push(G):se.push(G):Z.push(G)}),Z.length>0&&U.sections.push(P("Added files","feature",Z)),Q.length>0&&U.sections.push(P("Modified files","fix",Q)),se.length>0&&U.sections.push(P("Deleted files","chore",se)),[U]}function P(b,U,Z){const Q=[];return Z.forEach(se=>{const G=se.new_path||se.old_path,re=se.old_contents||"",Y=se.new_contents||"",ge=se.old_path?se.old_path:"",ue=vt(ge,se.new_path||"/dev/null",re,Y,"","",{context:3}),qe=`${He(G)}-${He(re+Y)}`;Q.push({id:qe,path:G,diff:ue,originalCode:re,modifiedCode:Y})}),{title:b,descriptions:[],type:U,changes:Q}}async function Ge(){if(!R)return;if(t(18,V=!0),t(19,W=!1),t(20,ee=null),t(17,O=[]),t(7,q=[]),l)return void t(18,V=!1);const b=102400;let U=0;if(p.forEach(Z=>{var Q,se;U+=(((Q=Z.old_contents)==null?void 0:Q.length)||0)+(((se=Z.new_contents)==null?void 0:se.length)||0)}),p.length>12||U>512e3){try{t(7,q=ye(p))}catch(Z){console.error("Failed to create simple explanation:",Z),t(20,ee="Failed to create explanation for large changes.")}t(18,V=!1)}else try{const Z=new Zi(G=>Ws.postMessage(G)),Q=new Map,se=p.map(G=>{const re=G.new_path||G.old_path,Y=G.old_contents||"",ge=G.new_contents||"",ue=`${He(re)}-${He(Y+ge)}`;return Q.set(ue,{old_path:G.old_path,new_path:G.new_path,old_contents:Y,new_contents:ge,change_type:G.change_type}),{id:ue,old_path:G.old_path,new_path:G.new_path,change_type:G.change_type}});try{const G=se.length===1;let re=[];G?re=se.map(Y=>({path:Y.new_path||Y.old_path,changes:[{id:Y.id,path:Y.new_path||Y.old_path,diff:`File: ${Y.new_path||Y.old_path}
Change type: ${Y.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):re=(await Z.send({type:"get-diff-group-changes-request",data:{changedFiles:se,changesById:!0,apikey:y}},3e4)).data.groupedChanges,t(17,O=re.map(Y=>({path:Y.path,changes:Y.changes.map(ge=>{if(ge.id&&Q.has(ge.id)){const ue=Q.get(ge.id);let qe=ge.diff;return qe&&!qe.startsWith("File:")||(qe=vt(ue.old_path||"",ue.new_path||"",ue.old_contents||"",ue.new_contents||"")),{...ge,diff:qe,old_path:ue.old_path,new_path:ue.new_path,old_contents:ue.old_contents,new_contents:ue.new_contents,change_type:ue.change_type,originalCode:ue.old_contents||"",modifiedCode:ue.new_contents||""}}return ge})})))}catch(G){console.error("Failed to group changes with LLM, falling back to simple grouping:",G);try{const re=se.map(Y=>{if(Y.id&&Q.has(Y.id)){const ge=Q.get(Y.id);return{...Y,old_path:ge.old_path,new_path:ge.new_path,old_contents:ge.old_contents||"",new_contents:ge.new_contents||"",change_type:ge.change_type}}return Y});t(7,q=ye(re)),t(17,O=q[0].sections.map(Y=>({path:Y.title,changes:Y.changes}))),t(19,W=!1)}catch(re){console.error("Failed to create simple explanation:",re),t(20,ee="Failed to group changes. Please try again.")}}if(t(18,V=!1),!O||O.length===0)throw new Error("Failed to group changes");if(!q||q.length===0){t(7,q=function(re){const Y={title:"Loading...",description:"",sections:[]};return re.forEach(ge=>{const ue=ge.changes.map(Ie=>{if(Ie.id)return Ie;const Qe=He(Ie.path),Je=He(Ie.originalCode+Ie.modifiedCode);return{...Ie,id:`${Qe}-${Je}`}}),qe={title:ge.path,descriptions:[],type:"other",changes:ue};Y.sections.push(qe)}),[Y]}(O));const G=q[0].sections.map(re=>({path:re.title,changes:re.changes.map(Y=>{var Ie,Qe,Je;const ge=((Ie=Y.originalCode)==null?void 0:Ie.length)||0,ue=((Qe=Y.modifiedCode)==null?void 0:Qe.length)||0,qe=((Je=Y.diff)==null?void 0:Je.length)||0;return ge>b||ue>b||qe>b?{id:Y.id,path:Y.path,diff:`File: ${Y.path}
Content too large to include in explanation request (${Math.max(ge,ue,qe)} bytes)`,originalCode:ge>b?`[File content too large: ${ge} bytes]`:Y.originalCode,modifiedCode:ue>b?`[File content too large: ${ue} bytes]`:Y.modifiedCode}:{id:Y.id,path:Y.path,diff:Y.diff,originalCode:Y.originalCode,modifiedCode:Y.modifiedCode}})}));t(19,W=!0);try{const{explanation:re,error:Y}=await F.getDescriptions(G,y);if(Y==="Token limit exceeded")return t(7,q=ye(p)),t(18,V=!1),void t(19,W=!1);re&&re.length>0&&re.forEach((ge,ue)=>{ge.sections&&ge.sections.forEach((qe,Ie)=>{qe.changes&&qe.changes.forEach(Qe=>{const Je=q[ue];if(Je&&Je.sections){const Tt=Je.sections[Ie];if(Tt&&Tt.changes){const mt=Tt.changes.find(Ci=>Ci.id===Qe.id);mt&&(Qe.originalCode=mt.originalCode,Qe.modifiedCode=mt.modifiedCode,Qe.diff=mt.diff)}}})})}),t(7,q=re)}catch(re){console.error("Failed to get descriptions, using skeleton explanation:",re)}}q.length===0&&t(20,ee="Failed to generate explanation.")}catch(Z){console.error("Failed to get explanation:",Z),t(20,ee=Z instanceof Error?Z.message:"An error occurred while generating the explanation.")}finally{t(18,V=!1),t(19,W=!1)}}ot(()=>{const b=localStorage.getItem("anthropic_apikey");b&&(y=b),t(37,R=!0)});let v="",S="Apply all changes locally";return r.$$set=b=>{"changedFiles"in b&&t(0,p=b.changedFiles),"agentLabel"in b&&t(1,w=b.agentLabel),"latestUserPrompt"in b&&t(2,k=b.latestUserPrompt),"onApplyChanges"in b&&t(35,x=b.onApplyChanges),"onOpenFile"in b&&t(3,E=b.onOpenFile),"onRenderBackup"in b&&t(4,_=b.onRenderBackup),"preloadedExplanation"in b&&t(36,B=b.preloadedExplanation),"isAgentFromDifferentRepo"in b&&t(5,m=b.isAgentFromDifferentRepo),"conflictFiles"in b&&t(6,D=b.conflictFiles)},r.$$.update=()=>{if(65537&r.$$.dirty[0]&&p&&ke.set(p.reduce((b,U)=>{const Z=U.new_path||U.old_path;return b[Z]=d[Z]??"none",b},{})),1&r.$$.dirty[0]&&t(41,a=JSON.stringify(p)),1376&r.$$.dirty[1]&&R&&a&&a!==v&&(t(39,v=a),B&&B.length>0?(t(7,q=B),t(18,V=!1),t(19,W=!1)):Ge(),t(10,J=!1),t(11,$e=!1),t(38,xe={})),896&r.$$.dirty[0]&&q&&q.length>0){const b=Ft(q);Array.from(b).forEach(Q=>{ce[Q]===void 0&&t(9,ce[Q]=!de,ce)});const U=Object.keys(ce).filter(Q=>ce[Q]),Z=Array.from(b);Z.length>0&&t(8,de=!Z.some(Q=>U.includes(Q)))}if(512&r.$$.dirty[0]&&t(27,n=Object.values(ce).some(Boolean)),128&r.$$.dirty[0]|128&r.$$.dirty[1]&&q&&q.length>0&&q.flatMap(b=>b.sections||[]).flatMap(b=>b.changes).forEach(b=>{xe[b.path]||t(38,xe[b.path]=b.modifiedCode,xe)}),128&r.$$.dirty[0]&&t(40,s=JSON.stringify(q)),65664&r.$$.dirty[0]|512&r.$$.dirty[1]&&t(12,i=(()=>{if(s&&d){const b=Ft(q);return b.size!==0&&Array.from(b).some(U=>d[U]!=="applied")}return!1})()),65536&r.$$.dirty[0]&&t(11,$e=Object.keys(d).every(b=>d[b]==="applied")),65536&r.$$.dirty[0]&&t(13,o=Object.keys(d).filter(b=>d[b]==="pending")),129&r.$$.dirty[0]|128&r.$$.dirty[1]&&async function(b,U,Z){const{filesToApply:Q}=rn(b,U,Z),se=new Set;for(const G of Q)(await F.previewApplyChanges(G.path,G.originalCode,G.newCode)).hasConflicts&&se.add(G.path);t(23,ie=se)}(q,p,xe),1&r.$$.dirty[0]&&t(26,l=p.length===0),67712&r.$$.dirty[0]|512&r.$$.dirty[1]&&s&&$e){const b=Ft(q);Array.from(b).every(U=>d[U]==="applied")||t(11,$e=!1)}2112&r.$$.dirty[0]&&t(14,c=$e&&D.size>0),15392&r.$$.dirty[0]&&t(15,u=m||J||$e||o.length>0||!i),64544&r.$$.dirty[0]&&(u?m?t(25,S="Cannot apply changes from a different repository locally"):J?t(25,S="Applying changes..."):c?t(25,S="All changes applied, but conflicts need to be resolved manually"):$e?t(25,S="All changes applied"):o.length>0?t(25,S="Waiting for changes to apply"):i||t(25,S="No changes to apply"):t(25,S="Apply all changes locally"))},[p,w,k,E,_,m,D,q,de,ce,J,$e,i,o,c,u,d,O,V,W,ee,ae,De,ie,ve,S,l,n,ke,function(){const b=Ft(q),U=Object.values(ce).some(Boolean);t(8,de=U),Array.from(b).forEach(Z=>{t(9,ce[Z]=!de,ce)})},Ne,fe,async function(){const b=await F.canApplyChanges();b.canApply?be():b.hasUnstagedChanges&&t(24,ve=!0)},be,Ge,x,B,R,xe,v,s,a,(b,U)=>{Ne(b.path,U)},b=>{fe(b.path,b.originalCode,b.modifiedCode)},b=>E(b.path),function(b,U){r.$$.not_equal(ce[U.path],b)&&(ce[U.path]=b,t(9,ce),t(7,q),t(8,de),t(37,R),t(41,a),t(39,v),t(36,B),t(0,p))},function(b,U,Z,Q){Te[b?"unshift":"push"](()=>{ae[100*U+10*Z+Q.path.length%10]=b,t(21,ae)})},function(b){De=b,t(22,De)},function(b){ve=b,t(24,ve)}]}class yu extends te{constructor(e){super(),ne(this,e,ku,wu,K,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:35,onOpenFile:3,onRenderBackup:4,preloadedExplanation:36,isAgentFromDifferentRepo:5,conflictFiles:6},null,[-1,-1,-1])}}function Ns(r){let e,t,n=r[8].opts,s=qs(r);return{c(){e=A("div"),s.c(),C(e,"class","file-explorer-contents svelte-5tfpo4")},m(i,o){g(i,e,o),s.m(e,null),t=!0},p(i,o){256&o&&K(n,n=i[8].opts)?(H(),$(s,1,1,X),j(),s=qs(i),s.c(),f(s,1),s.m(e,null)):s.p(i,o)},i(i){t||(f(s),t=!0)},o(i){$(s),t=!1},d(i){i&&h(e),s.d(i)}}}function vu(r){var n,s;let e,t;return e=new yu({props:{changedFiles:r[0],onApplyChanges:r[10],onOpenFile:r[11],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[12],preloadedExplanation:(s=(n=r[8])==null?void 0:n.opts)==null?void 0:s.preloadedExplanation,isAgentFromDifferentRepo:r[5],conflictFiles:r[6]}}),{c(){z(e.$$.fragment)},m(i,o){L(e,i,o),t=!0},p(i,o){var a,c;const l={};1&o&&(l.changedFiles=i[0]),8&o&&(l.agentLabel=i[3]),16&o&&(l.latestUserPrompt=i[4]),128&o&&(l.onRenderBackup=i[12]),256&o&&(l.preloadedExplanation=(c=(a=i[8])==null?void 0:a.opts)==null?void 0:c.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=i[5]),64&o&&(l.conflictFiles=i[6]),e.$set(l)},i(i){t||(f(e.$$.fragment,i),t=!0)},o(i){$(e.$$.fragment,i),t=!1},d(i){M(e,i)}}}function Au(r){let e,t;return e=new co({props:{changedFiles:r[0],onApplyChanges:r[10],onOpenFile:r[11],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0]),2&s&&(i.pendingFiles=n[1]),4&s&&(i.appliedFiles=n[2]),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function qs(r){let e,t,n,s;const i=[Au,vu],o=[];function l(a,c){return a[7]==="changedFiles"?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=we()},m(a,c){o[e].m(a,c),g(a,n,c),s=!0},p(a,c){let u=e;e=l(a),e===u?o[e].p(a,c):(H(),$(o[u],1,1,()=>{o[u]=null}),j(),t=o[e],t?t.p(a,c):(t=o[e]=i[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){s||(f(t),s=!0)},o(a){$(t),s=!1},d(a){a&&h(n),o[e].d(a)}}}function bu(r){let e,t,n,s=r[0]&&Ns(r);return{c(){e=A("div"),t=A("div"),s&&s.c(),C(t,"class","file-explorer-main svelte-5tfpo4"),C(e,"class","diff-page svelte-5tfpo4")},m(i,o){g(i,e,o),T(e,t),s&&s.m(t,null),n=!0},p(i,[o]){i[0]?s?(s.p(i,o),1&o&&f(s,1)):(s=Ns(i),s.c(),f(s,1),s.m(t,null)):s&&(H(),$(s,1,1,()=>{s=null}),j())},i(i){n||(f(s),n=!0)},o(i){$(s),n=!1},d(i){i&&h(e),s&&s.d()}}}function Eu(r,e,t){let n,{changedFiles:s=[]}=e,{pendingFiles:i=[]}=e,{appliedFiles:o=[]}=e,{agentLabel:l}=e,{latestUserPrompt:a}=e,{isAgentFromDifferentRepo:c=!1}=e,u=new Set;const d=Xe(it.key),p=Xe(bt.key);Re(r,p,k=>t(8,n=k));let w="summary";return function(k){k.subscribe(x=>{if(x){const E=document.getElementById(It(x));E&&E.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(k=null){const x=Ye(k);return St(li,x),x}(null)),r.$$set=k=>{"changedFiles"in k&&t(0,s=k.changedFiles),"pendingFiles"in k&&t(1,i=k.pendingFiles),"appliedFiles"in k&&t(2,o=k.appliedFiles),"agentLabel"in k&&t(3,l=k.agentLabel),"latestUserPrompt"in k&&t(4,a=k.latestUserPrompt),"isAgentFromDifferentRepo"in k&&t(5,c=k.isAgentFromDifferentRepo)},[s,i,o,l,a,c,u,w,n,p,async(k,x,E)=>{const{success:_,hasConflicts:B}=await d.applyChanges(k,x,E);_&&B&&t(6,u=new Set([...u,k]))},k=>d.openFile(k),()=>{t(7,w="changedFiles")}]}class _u extends te{constructor(e){super(),ne(this,e,Eu,bu,K,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function Bu(r){let e,t,n,s,i;return t=new zt({props:{size:1}}),{c(){e=A("div"),z(t.$$.fragment),n=I(),s=A("p"),s.textContent="Loading diff view...",C(e,"class","l-center svelte-ccste2")},m(o,l){g(o,e,l),L(t,e,null),T(e,n),T(e,s),i=!0},p:X,i(o){i||(f(t.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),i=!1},d(o){o&&h(e),M(t)}}}function zu(r){let e,t;return e=new _u({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){z(e.$$.fragment)},m(n,s){L(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0].changedFiles),1&s&&(i.agentLabel=n[0].sessionSummary),1&s&&(i.latestUserPrompt=n[0].userPrompt),1&s&&(i.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),e.$set(i)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){M(e,n)}}}function Lu(r){let e,t,n,s;const i=[zu,Bu],o=[];function l(a,c){return a[0]?0:1}return t=l(r),n=o[t]=i[t](r),{c(){e=A("div"),n.c(),C(e,"class","l-main svelte-ccste2")},m(a,c){g(a,e,c),o[t].m(e,null),s=!0},p(a,c){let u=t;t=l(a),t===u?o[t].p(a,c):(H(),$(o[u],1,1,()=>{o[u]=null}),j(),n=o[t],n?n.p(a,c):(n=o[t]=i[t](a),n.c()),f(n,1),n.m(e,null))},i(a){s||(f(n),s=!0)},o(a){$(n),s=!1},d(a){a&&h(e),o[t].d()}}}function Mu(r){let e,t,n,s;return e=new Si.Root({props:{$$slots:{default:[Lu]},$$scope:{ctx:r}}}),{c(){z(e.$$.fragment)},m(i,o){L(e,i,o),t=!0,n||(s=dt(window,"message",r[1].onMessageFromExtension),n=!0)},p(i,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){t||(f(e.$$.fragment,i),t=!0)},o(i){$(e.$$.fragment,i),t=!1},d(i){M(e,i),n=!1,s()}}}function Ru(r,e,t){let n,s,i=new Bi(Ws),o=new bt(i);Re(r,o,a=>t(4,s=a)),i.registerConsumer(o);let l=new it(i);return St(it.key,l),St(bt.key,o),ot(()=>(o.onPanelLoaded(),()=>{i.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&t(0,n=s.opts)},[n,i,o,l,s]}new class extends te{constructor(r){super(),ne(this,r,Ru,Mu,K,{})}}({target:document.getElementById("app")});

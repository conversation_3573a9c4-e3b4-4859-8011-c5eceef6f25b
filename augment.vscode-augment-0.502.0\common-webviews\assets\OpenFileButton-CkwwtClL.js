import{S as J,i as K,s as Q,a5 as no,a6 as so,D as U,E as j,c as A,e as R,F as q,a7 as io,u as d,t as C,h as D,G as P,a3 as I,a as _,j as to,N as X,q as Y,r as Z,W as h,g as oo,ad as eo,J as x,K as O,L as w,M as L,V as M,I as lo,ai as co,aj as ao,n as ro}from"./SpinnerAugment-CL9SZpf8.js";import{g as uo,R as po}from"./chat-context-CLxziAX3.js";import{I as $o}from"./IconButtonAugment-C4xMcLhX.js";import{A as fo}from"./arrow-up-right-from-square-DUrpll74.js";import{a as mo,T as ho}from"./CardAugment-bwPj7Y67.js";import{B as Co}from"./ButtonAugment-iwbEjzvh.js";const yo=e=>({}),S=e=>({slot:"iconLeft"}),go=e=>({}),E=e=>({slot:"iconRight"}),ko=e=>({}),W=e=>({}),vo=e=>({}),B=e=>({});function xo(e){let o,s;const n=[e[6],{color:e[2]},{variant:e[5]}];let i={$$slots:{iconRight:[No],iconLeft:[Lo],default:[wo]},$$scope:{ctx:e}};for(let t=0;t<n.length;t+=1)i=_(i,n[t]);return o=new Co({props:i}),o.$on("click",e[8]),o.$on("keyup",e[27]),o.$on("keydown",e[28]),o.$on("mousedown",e[29]),o.$on("mouseover",e[30]),o.$on("focus",e[31]),o.$on("mouseleave",e[32]),o.$on("blur",e[33]),o.$on("contextmenu",e[34]),{c(){j(o.$$.fragment)},m(t,c){q(o,t,c),s=!0},p(t,c){const a=100&c[0]?oo(n,[64&c[0]&&eo(t[6]),4&c[0]&&{color:t[2]},32&c[0]&&{variant:t[5]}]):{};32&c[1]&&(a.$$scope={dirty:c,ctx:t}),o.$set(a)},i(t){s||(d(o.$$.fragment,t),s=!0)},o(t){C(o.$$.fragment,t),s=!1},d(t){P(o,t)}}}function Oo(e){let o,s;const n=[e[6],{color:e[2]},{variant:e[5]}];let i={$$slots:{default:[zo]},$$scope:{ctx:e}};for(let t=0;t<n.length;t+=1)i=_(i,n[t]);return o=new $o({props:i}),o.$on("click",e[8]),o.$on("keyup",e[19]),o.$on("keydown",e[20]),o.$on("mousedown",e[21]),o.$on("mouseover",e[22]),o.$on("focus",e[23]),o.$on("mouseleave",e[24]),o.$on("blur",e[25]),o.$on("contextmenu",e[26]),{c(){j(o.$$.fragment)},m(t,c){q(o,t,c),s=!0},p(t,c){const a=100&c[0]?oo(n,[64&c[0]&&eo(t[6]),4&c[0]&&{color:t[2]},32&c[0]&&{variant:t[5]}]):{};32&c[1]&&(a.$$scope={dirty:c,ctx:t}),o.$set(a)},i(t){s||(d(o.$$.fragment,t),s=!0)},o(t){C(o.$$.fragment,t),s=!1},d(t){P(o,t)}}}function wo(e){let o;const s=e[18].default,n=x(s,e,e[36],null);return{c(){n&&n.c()},m(i,t){n&&n.m(i,t),o=!0},p(i,t){n&&n.p&&(!o||32&t[1])&&O(n,s,i,i[36],o?L(s,i[36],t,null):w(i[36]),null)},i(i){o||(d(n,i),o=!0)},o(i){C(n,i),o=!1},d(i){n&&n.d(i)}}}function Lo(e){let o;const s=e[18].iconLeft,n=x(s,e,e[36],S);return{c(){n&&n.c()},m(i,t){n&&n.m(i,t),o=!0},p(i,t){n&&n.p&&(!o||32&t[1])&&O(n,s,i,i[36],o?L(s,i[36],t,yo):w(i[36]),S)},i(i){o||(d(n,i),o=!0)},o(i){C(n,i),o=!1},d(i){n&&n.d(i)}}}function No(e){let o;const s=e[18].iconRight,n=x(s,e,e[36],E);return{c(){n&&n.c()},m(i,t){n&&n.m(i,t),o=!0},p(i,t){n&&n.p&&(!o||32&t[1])&&O(n,s,i,i[36],o?L(s,i[36],t,go):w(i[36]),E)},i(i){o||(d(n,i),o=!0)},o(i){C(n,i),o=!1},d(i){n&&n.d(i)}}}function zo(e){let o,s,n;const i=e[18].iconLeft,t=x(i,e,e[36],B),c=e[18].default,a=x(c,e,e[36],null),u=e[18].iconRight,$=x(u,e,e[36],W);return{c(){t&&t.c(),o=M(),a&&a.c(),s=M(),$&&$.c()},m(r,f){t&&t.m(r,f),R(r,o,f),a&&a.m(r,f),R(r,s,f),$&&$.m(r,f),n=!0},p(r,f){t&&t.p&&(!n||32&f[1])&&O(t,i,r,r[36],n?L(i,r[36],f,vo):w(r[36]),B),a&&a.p&&(!n||32&f[1])&&O(a,c,r,r[36],n?L(c,r[36],f,null):w(r[36]),null),$&&$.p&&(!n||32&f[1])&&O($,u,r,r[36],n?L(u,r[36],f,ko):w(r[36]),W)},i(r){n||(d(t,r),d(a,r),d($,r),n=!0)},o(r){C(t,r),C(a,r),C($,r),n=!1},d(r){r&&(D(o),D(s)),t&&t.d(r),a&&a.d(r),$&&$.d(r)}}}function To(e){let o,s,n,i;const t=[Oo,xo],c=[];function a(u,$){return u[0]?0:1}return o=a(e),s=c[o]=t[o](e),{c(){s.c(),n=X()},m(u,$){c[o].m(u,$),R(u,n,$),i=!0},p(u,$){let r=o;o=a(u),o===r?c[o].p(u,$):(Y(),C(c[r],1,1,()=>{c[r]=null}),Z(),s=c[o],s?s.p(u,$):(s=c[o]=t[o](u),s.c()),d(s,1),s.m(n.parentNode,n))},i(u){i||(d(s),i=!0)},o(u){C(s),i=!1},d(u){u&&D(n),c[o].d(u)}}}function bo(e){let o,s,n,i;function t(a){e[35](a)}let c={onOpenChange:e[7],content:e[4],triggerOn:[mo.Hover],nested:e[1],$$slots:{default:[To]},$$scope:{ctx:e}};return e[3]!==void 0&&(c.requestClose=e[3]),s=new ho({props:c}),no.push(()=>so(s,"requestClose",t)),{c(){o=U("div"),j(s.$$.fragment),A(o,"class","c-successful-button svelte-1dvyzw2")},m(a,u){R(a,o,u),q(s,o,null),i=!0},p(a,u){const $={};16&u[0]&&($.content=a[4]),2&u[0]&&($.nested=a[1]),101&u[0]|32&u[1]&&($.$$scope={dirty:u,ctx:a}),!n&&8&u[0]&&(n=!0,$.requestClose=a[3],io(()=>n=!1)),s.$set($)},i(a){i||(d(s.$$.fragment,a),i=!0)},o(a){C(s.$$.fragment,a),i=!1},d(a){a&&D(o),P(s)}}}function Ro(e,o,s){let n,i,t;const c=["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"];let a,u,$=I(o,c),{$$slots:r={},$$scope:f}=o,{defaultColor:k}=o,{tooltip:y}=o,{stateVariant:v}=o,{onClick:N}=o,{tooltipDuration:z=1500}=o,{icon:F=!1}=o,{stickyColor:g=!0}=o,{persistOnTooltipClose:T=!1}=o,{tooltipNested:p}=o,m="neutral",V=k,b=y==null?void 0:y.neutral;return e.$$set=l=>{o=_(_({},o),to(l)),s(38,$=I(o,c)),"defaultColor"in l&&s(9,k=l.defaultColor),"tooltip"in l&&s(10,y=l.tooltip),"stateVariant"in l&&s(11,v=l.stateVariant),"onClick"in l&&s(12,N=l.onClick),"tooltipDuration"in l&&s(13,z=l.tooltipDuration),"icon"in l&&s(0,F=l.icon),"stickyColor"in l&&s(14,g=l.stickyColor),"persistOnTooltipClose"in l&&s(15,T=l.persistOnTooltipClose),"tooltipNested"in l&&s(1,p=l.tooltipNested),"$$scope"in l&&s(36,f=l.$$scope)},e.$$.update=()=>{s(17,{variant:n,...i}=$,n,(s(6,i),s(38,$))),198656&e.$$.dirty[0]&&s(5,t=(v==null?void 0:v[m])??n),66048&e.$$.dirty[0]&&s(2,V=m==="success"?"success":m==="failure"?"error":k)},[F,p,V,a,b,t,i,function(l){T||l||(clearTimeout(u),u=void 0,s(4,b=y==null?void 0:y.neutral),g||s(16,m="neutral"))},async function(l){try{s(16,m=await N(l)??"neutral")}catch{s(16,m="failure")}s(4,b=y==null?void 0:y[m]),clearTimeout(u),u=setTimeout(()=>{a==null||a(),g||s(16,m="neutral")},z)},k,y,v,N,z,g,T,m,n,r,function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){h.call(this,e,l)},function(l){a=l,s(3,a)},f]}class Do extends J{constructor(o){super(),K(this,o,Ro,bo,Q,{defaultColor:9,tooltip:10,stateVariant:11,onClick:12,tooltipDuration:13,icon:0,stickyColor:14,persistOnTooltipClose:15,tooltipNested:1},null,[-1,-1])}}const Fo=e=>({}),G=e=>({});function H(e){let o,s,n,i;return s=new Do({props:{defaultColor:e[1],stickyColor:e[3],size:e[0],variant:e[2],tooltip:{neutral:"Open File In Editor",success:"Opening file..."},stateVariant:{success:"soft"},onClick:e[4],icon:!e[7].text,$$slots:{iconLeft:[jo],default:[Vo]},$$scope:{ctx:e}}}),{c(){o=U("span"),j(s.$$.fragment),A(o,"class",n="c-open-file-button-container c-open-file-button__size--"+e[0]+" svelte-pdfhuj")},m(t,c){R(t,o,c),q(s,o,null),i=!0},p(t,c){const a={};2&c&&(a.defaultColor=t[1]),8&c&&(a.stickyColor=t[3]),1&c&&(a.size=t[0]),4&c&&(a.variant=t[2]),16&c&&(a.onClick=t[4]),128&c&&(a.icon=!t[7].text),32768&c&&(a.$$scope={dirty:c,ctx:t}),s.$set(a),(!i||1&c&&n!==(n="c-open-file-button-container c-open-file-button__size--"+t[0]+" svelte-pdfhuj"))&&A(o,"class",n)},i(t){i||(d(s.$$.fragment,t),i=!0)},o(t){C(s.$$.fragment,t),i=!1},d(t){t&&D(o),P(s)}}}function Vo(e){let o;const s=e[14].text,n=x(s,e,e[15],G);return{c(){n&&n.c()},m(i,t){n&&n.m(i,t),o=!0},p(i,t){n&&n.p&&(!o||32768&t)&&O(n,s,i,i[15],o?L(s,i[15],t,Fo):w(i[15]),G)},i(i){o||(d(n,i),o=!0)},o(i){C(n,i),o=!1},d(i){n&&n.d(i)}}}function jo(e){let o,s;return o=new fo({props:{slot:"iconLeft"}}),{c(){j(o.$$.fragment)},m(n,i){q(o,n,i),s=!0},p:ro,i(n){s||(d(o.$$.fragment,n),s=!0)},o(n){C(o.$$.fragment,n),s=!1},d(n){P(o,n)}}}function qo(e){let o,s,n=e[5]&&H(e);return{c(){n&&n.c(),o=X()},m(i,t){n&&n.m(i,t),R(i,o,t),s=!0},p(i,[t]){i[5]?n?(n.p(i,t),32&t&&d(n,1)):(n=H(i),n.c(),d(n,1),n.m(o.parentNode,o)):n&&(Y(),C(n,1,1,()=>{n=null}),Z())},i(i){s||(d(n),s=!0)},o(i){C(n),s=!1},d(i){i&&D(o),n&&n.d(i)}}}function Po(e,o,s){let n,i,t,c,{$$slots:a={},$$scope:u}=o;const $=lo(a);let{path:r}=o,{start:f=0}=o,{stop:k=0}=o,{size:y=0}=o,{color:v="neutral"}=o,{variant:N="ghost-block"}=o,{stickyColor:z=!1}=o,{onOpenLocalFile:F=async function(p){var V,b;if((V=p==null?void 0:p.stopPropagation)==null||V.call(p),(b=p==null?void 0:p.preventDefault)==null||b.call(p),!r)return;const m=await(g==null?void 0:g.extensionClient.resolvePath({rootPath:"",relPath:r}));return g==null||g.extensionClient.openFile({repoRoot:(m==null?void 0:m.repoRoot)??"",pathName:(m==null?void 0:m.pathName)??"",range:{start:Math.max(f,0),stop:Math.max(k,0)}}),"success"}}=o;const g=uo(),T=co(po.key);return ao(e,T,p=>s(13,c=p)),e.$$set=p=>{"path"in p&&s(8,r=p.path),"start"in p&&s(9,f=p.start),"stop"in p&&s(10,k=p.stop),"size"in p&&s(0,y=p.size),"color"in p&&s(1,v=p.color),"variant"in p&&s(2,N=p.variant),"stickyColor"in p&&s(3,z=p.stickyColor),"onOpenLocalFile"in p&&s(4,F=p.onOpenLocalFile),"$$scope"in p&&s(15,u=p.$$scope)},e.$$.update=()=>{8192&e.$$.dirty&&s(11,n=c==null?void 0:c.isRemoteAgentSshWindow),8192&e.$$.dirty&&s(12,i=!!(c!=null&&c.isActive)),6144&e.$$.dirty&&s(5,t=!i||n)},[y,v,N,z,F,t,T,$,r,f,k,n,i,c,a,u]}class Bo extends J{constructor(o){super(),K(this,o,Po,qo,Q,{path:8,start:9,stop:10,size:0,color:1,variant:2,stickyColor:3,onOpenLocalFile:4})}}export{Bo as O,Do as S};

import{_ as a,aE as ai,l as $t,a1 as Mt,a0 as oi,F as bt,E as Bt,D as hi,i as ri,r as zt,s as li,g as ci,q as gi,b as ui,c as xi,x as pi,I as di,k as mi}from"./AugmentMessage-DSvQSfka.js";import{i as fi}from"./init-g68aIKmP.js";import{o as yi}from"./ordinal-_rw2EY4v.js";import{l as It}from"./linear-BtztiuVY.js";import"./IconButtonAugment-C4xMcLhX.js";import"./SpinnerAugment-CL9SZpf8.js";import"./CardAugment-bwPj7Y67.js";import"./arrow-up-right-from-square-DUrpll74.js";import"./index-BAWb-tvr.js";import"./message-broker-SEbJxN6J.js";import"./async-messaging-CtwQrvzD.js";import"./BaseTextInput-BAWt2_LS.js";import"./types-CGlLNakm.js";import"./file-paths-BPg3etNg.js";import"./mcp-logo-BBF9ZFwB.js";import"./CalloutAugment-C-hloZHD.js";import"./folder-opened-B3jucdqG.js";import"./index-BskWw2a8.js";import"./diff-utils-CRbaKECg.js";import"./LanguageIcon-BQz1eaw5.js";import"./preload-helper-Dv6uf1Os.js";import"./index-iuo-Ho0S.js";import"./keypress-DD1aQVr0.js";import"./await_block-DQGV_eqb.js";import"./chat-context-CLxziAX3.js";import"./types-DDm27S8B.js";import"./utils-0kgBFNBQ.js";import"./ra-diff-ops-model-DUTpCop3.js";import"./CollapseButtonAugment-Ba7Np-LE.js";import"./ButtonAugment-iwbEjzvh.js";import"./MaterialIcon-Bh8QWD0w.js";import"./CopyButton-BWQVMcne.js";import"./copy-BFy87Ryv.js";import"./ellipsis-B3ZqaMmA.js";import"./IconFilePath-j5ToM371.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-C3pDA31_.js";import"./augment-logo-CDzRYJ1a.js";import"./terminal-D0NX0vvY.js";import"./pen-to-square-DY0HDzb8.js";import"./TextAreaAugment-CiMTZgUO.js";function pt(){var e,t,i=yi().unknown(void 0),s=i.domain,n=i.range,l=0,c=1,p=!1,x=0,_=0,T=.5;function w(){var f=s().length,L=c<l,k=L?c:l,P=L?l:c;e=(P-k)/Math.max(1,f-x+2*_),p&&(e=Math.floor(e)),k+=(P-k-e*(f-x))*T,t=e*(1-x),p&&(k=Math.round(k),t=Math.round(t));var m=function(A,r,D){A=+A,r=+r,D=(z=arguments.length)<2?(r=A,A=0,1):z<3?1:+D;for(var $=-1,z=0|Math.max(0,Math.ceil((r-A)/D)),Z=new Array(z);++$<z;)Z[$]=A+$*D;return Z}(f).map(function(A){return k+e*A});return n(L?m.reverse():m)}return delete i.unknown,i.domain=function(f){return arguments.length?(s(f),w()):s()},i.range=function(f){return arguments.length?([l,c]=f,l=+l,c=+c,w()):[l,c]},i.rangeRound=function(f){return[l,c]=f,l=+l,c=+c,p=!0,w()},i.bandwidth=function(){return t},i.step=function(){return e},i.round=function(f){return arguments.length?(p=!!f,w()):p},i.padding=function(f){return arguments.length?(x=Math.min(1,_=+f),w()):x},i.paddingInner=function(f){return arguments.length?(x=Math.min(1,f),w()):x},i.paddingOuter=function(f){return arguments.length?(_=+f,w()):_},i.align=function(f){return arguments.length?(T=Math.max(0,Math.min(1,f)),w()):T},i.copy=function(){return pt(s(),[l,c]).round(p).paddingInner(x).paddingOuter(_).align(T)},fi.apply(w(),arguments)}var dt=function(){var e=a(function(h,b,g,u){for(g=g||{},u=h.length;u--;g[h[u]]=b);return g},"o"),t=[1,10,12,14,16,18,19,21,23],i=[2,6],s=[1,3],n=[1,5],l=[1,6],c=[1,7],p=[1,5,10,12,14,16,18,19,21,23,34,35,36],x=[1,25],_=[1,26],T=[1,28],w=[1,29],f=[1,30],L=[1,31],k=[1,32],P=[1,33],m=[1,34],A=[1,35],r=[1,36],D=[1,37],$=[1,43],z=[1,42],Z=[1,47],et=[1,50],C=[1,10,12,14,16,18,19,21,23,34,35,36],ct=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],E=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],Rt=[1,64],gt={trace:a(function(){},"trace"),yy:{},symbols_:{error:2,start:3,eol:4,XYCHART:5,chartConfig:6,document:7,CHART_ORIENTATION:8,statement:9,title:10,text:11,X_AXIS:12,parseXAxis:13,Y_AXIS:14,parseYAxis:15,LINE:16,plotData:17,BAR:18,acc_title:19,acc_title_value:20,acc_descr:21,acc_descr_value:22,acc_descr_multiline_value:23,SQUARE_BRACES_START:24,commaSeparatedNumbers:25,SQUARE_BRACES_END:26,NUMBER_WITH_DECIMAL:27,COMMA:28,xAxisData:29,bandData:30,ARROW_DELIMITER:31,commaSeparatedTexts:32,yAxisData:33,NEWLINE:34,SEMI:35,EOF:36,alphaNum:37,STR:38,MD_STR:39,alphaNumToken:40,AMP:41,NUM:42,ALPHA:43,PLUS:44,EQUALS:45,MULT:46,DOT:47,BRKT:48,MINUS:49,UNDERSCORE:50,$accept:0,$end:1},terminals_:{2:"error",5:"XYCHART",8:"CHART_ORIENTATION",10:"title",12:"X_AXIS",14:"Y_AXIS",16:"LINE",18:"BAR",19:"acc_title",20:"acc_title_value",21:"acc_descr",22:"acc_descr_value",23:"acc_descr_multiline_value",24:"SQUARE_BRACES_START",26:"SQUARE_BRACES_END",27:"NUMBER_WITH_DECIMAL",28:"COMMA",31:"ARROW_DELIMITER",34:"NEWLINE",35:"SEMI",36:"EOF",38:"STR",39:"MD_STR",41:"AMP",42:"NUM",43:"ALPHA",44:"PLUS",45:"EQUALS",46:"MULT",47:"DOT",48:"BRKT",49:"MINUS",50:"UNDERSCORE"},productions_:[0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],performAction:a(function(h,b,g,u,S,o,J){var d=o.length-1;switch(S){case 5:u.setOrientation(o[d]);break;case 9:u.setDiagramTitle(o[d].text.trim());break;case 12:u.setLineData({text:"",type:"text"},o[d]);break;case 13:u.setLineData(o[d-1],o[d]);break;case 14:u.setBarData({text:"",type:"text"},o[d]);break;case 15:u.setBarData(o[d-1],o[d]);break;case 16:this.$=o[d].trim(),u.setAccTitle(this.$);break;case 17:case 18:this.$=o[d].trim(),u.setAccDescription(this.$);break;case 19:case 27:this.$=o[d-1];break;case 20:this.$=[Number(o[d-2]),...o[d]];break;case 21:this.$=[Number(o[d])];break;case 22:u.setXAxisTitle(o[d]);break;case 23:u.setXAxisTitle(o[d-1]);break;case 24:u.setXAxisTitle({type:"text",text:""});break;case 25:u.setXAxisBand(o[d]);break;case 26:u.setXAxisRangeData(Number(o[d-2]),Number(o[d]));break;case 28:this.$=[o[d-2],...o[d]];break;case 29:this.$=[o[d]];break;case 30:u.setYAxisTitle(o[d]);break;case 31:u.setYAxisTitle(o[d-1]);break;case 32:u.setYAxisTitle({type:"text",text:""});break;case 33:u.setYAxisRangeData(Number(o[d-2]),Number(o[d]));break;case 37:case 38:this.$={text:o[d],type:"text"};break;case 39:this.$={text:o[d],type:"markdown"};break;case 40:this.$=o[d];break;case 41:this.$=o[d-1]+""+o[d]}},"anonymous"),table:[e(t,i,{3:1,4:2,7:4,5:s,34:n,35:l,36:c}),{1:[3]},e(t,i,{4:2,7:4,3:8,5:s,34:n,35:l,36:c}),e(t,i,{4:2,7:4,6:9,3:10,5:s,8:[1,11],34:n,35:l,36:c}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},e(p,[2,34]),e(p,[2,35]),e(p,[2,36]),{1:[2,1]},e(t,i,{4:2,7:4,3:21,5:s,34:n,35:l,36:c}),{1:[2,3]},e(p,[2,5]),e(t,[2,7],{4:22,34:n,35:l,36:c}),{11:23,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},{11:39,13:38,24:$,27:z,29:40,30:41,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},{11:45,15:44,27:Z,33:46,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},{11:49,17:48,24:et,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},{11:52,17:51,24:et,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},{20:[1,53]},{22:[1,54]},e(C,[2,18]),{1:[2,2]},e(C,[2,8]),e(C,[2,9]),e(ct,[2,37],{40:55,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D}),e(ct,[2,38]),e(ct,[2,39]),e(E,[2,40]),e(E,[2,42]),e(E,[2,43]),e(E,[2,44]),e(E,[2,45]),e(E,[2,46]),e(E,[2,47]),e(E,[2,48]),e(E,[2,49]),e(E,[2,50]),e(E,[2,51]),e(C,[2,10]),e(C,[2,22],{30:41,29:56,24:$,27:z}),e(C,[2,24]),e(C,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},e(C,[2,11]),e(C,[2,30],{33:60,27:Z}),e(C,[2,32]),{31:[1,61]},e(C,[2,12]),{17:62,24:et},{25:63,27:Rt},e(C,[2,14]),{17:65,24:et},e(C,[2,16]),e(C,[2,17]),e(E,[2,41]),e(C,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},e(C,[2,31]),{27:[1,69]},e(C,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},e(C,[2,15]),e(C,[2,26]),e(C,[2,27]),{11:59,32:72,37:24,38:x,39:_,40:27,41:T,42:w,43:f,44:L,45:k,46:P,47:m,48:A,49:r,50:D},e(C,[2,33]),e(C,[2,19]),{25:73,27:Rt},{26:[2,28]},{26:[2,20]}],defaultActions:{8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},parseError:a(function(h,b){if(!b.recoverable){var g=new Error(h);throw g.hash=b,g}this.trace(h)},"parseError"),parse:a(function(h){var b=this,g=[0],u=[],S=[null],o=[],J=this.table,d="",nt=0,Dt=0,si=o.slice.call(arguments,1),R=Object.create(this.lexer),O={yy:{}};for(var ut in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ut)&&(O.yy[ut]=this.yy[ut]);R.setInput(h,O.yy),O.yy.lexer=R,O.yy.parser=this,R.yylloc===void 0&&(R.yylloc={});var xt=R.yylloc;o.push(xt);var ni=R.options&&R.options.ranges;function Lt(){var I;return typeof(I=u.pop()||R.lex()||1)!="number"&&(I instanceof Array&&(I=(u=I).pop()),I=b.symbols_[I]||I),I}typeof O.yy.parseError=="function"?this.parseError=O.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,a(function(I){g.length=g.length-2*I,S.length=S.length-I,o.length=o.length-I},"popStack"),a(Lt,"lex");for(var v,W,M,Pt,at,B,vt,ot,F={};;){if(W=g[g.length-1],this.defaultActions[W]?M=this.defaultActions[W]:(v==null&&(v=Lt()),M=J[W]&&J[W][v]),M===void 0||!M.length||!M[0]){var Et="";for(at in ot=[],J[W])this.terminals_[at]&&at>2&&ot.push("'"+this.terminals_[at]+"'");Et=R.showPosition?"Parse error on line "+(nt+1)+`:
`+R.showPosition()+`
Expecting `+ot.join(", ")+", got '"+(this.terminals_[v]||v)+"'":"Parse error on line "+(nt+1)+": Unexpected "+(v==1?"end of input":"'"+(this.terminals_[v]||v)+"'"),this.parseError(Et,{text:R.match,token:this.terminals_[v]||v,line:R.yylineno,loc:xt,expected:ot})}if(M[0]instanceof Array&&M.length>1)throw new Error("Parse Error: multiple actions possible at state: "+W+", token: "+v);switch(M[0]){case 1:g.push(v),S.push(R.yytext),o.push(R.yylloc),g.push(M[1]),v=null,Dt=R.yyleng,d=R.yytext,nt=R.yylineno,xt=R.yylloc;break;case 2:if(B=this.productions_[M[1]][1],F.$=S[S.length-B],F._$={first_line:o[o.length-(B||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(B||1)].first_column,last_column:o[o.length-1].last_column},ni&&(F._$.range=[o[o.length-(B||1)].range[0],o[o.length-1].range[1]]),(Pt=this.performAction.apply(F,[d,Dt,nt,O.yy,M[1],S,o].concat(si)))!==void 0)return Pt;B&&(g=g.slice(0,-1*B*2),S=S.slice(0,-1*B),o=o.slice(0,-1*B)),g.push(this.productions_[M[1]][0]),S.push(F.$),o.push(F._$),vt=J[g[g.length-2]][g[g.length-1]],g.push(vt);break;case 3:return!0}}return!0},"parse")},ei=function(){return{EOF:1,parseError:a(function(h,b){if(!this.yy.parser)throw new Error(h);this.yy.parser.parseError(h,b)},"parseError"),setInput:a(function(h,b){return this.yy=b||this.yy||{},this._input=h,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:a(function(){var h=this._input[0];return this.yytext+=h,this.yyleng++,this.offset++,this.match+=h,this.matched+=h,h.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),h},"input"),unput:a(function(h){var b=h.length,g=h.split(/(?:\r\n?|\n)/g);this._input=h+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-b),this.offset-=b;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var S=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===u.length?this.yylloc.first_column:0)+u[u.length-g.length].length-g[0].length:this.yylloc.first_column-b},this.options.ranges&&(this.yylloc.range=[S[0],S[0]+this.yyleng-b]),this.yyleng=this.yytext.length,this},"unput"),more:a(function(){return this._more=!0,this},"more"),reject:a(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:a(function(h){this.unput(this.match.slice(h))},"less"),pastInput:a(function(){var h=this.matched.substr(0,this.matched.length-this.match.length);return(h.length>20?"...":"")+h.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:a(function(){var h=this.match;return h.length<20&&(h+=this._input.substr(0,20-h.length)),(h.substr(0,20)+(h.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:a(function(){var h=this.pastInput(),b=new Array(h.length+1).join("-");return h+this.upcomingInput()+`
`+b+"^"},"showPosition"),test_match:a(function(h,b){var g,u,S;if(this.options.backtrack_lexer&&(S={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(S.yylloc.range=this.yylloc.range.slice(0))),(u=h[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+h[0].length},this.yytext+=h[0],this.match+=h[0],this.matches=h,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(h[0].length),this.matched+=h[0],g=this.performAction.call(this,this.yy,this,b,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var o in S)this[o]=S[o];return!1}return!1},"test_match"),next:a(function(){if(this.done)return this.EOF;var h,b,g,u;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var S=this._currentRules(),o=0;o<S.length;o++)if((g=this._input.match(this.rules[S[o]]))&&(!b||g[0].length>b[0].length)){if(b=g,u=o,this.options.backtrack_lexer){if((h=this.test_match(g,S[o]))!==!1)return h;if(this._backtrack){b=!1;continue}return!1}if(!this.options.flex)break}return b?(h=this.test_match(b,S[u]))!==!1&&h:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:a(function(){var h=this.next();return h||this.lex()},"lex"),begin:a(function(h){this.conditionStack.push(h)},"begin"),popState:a(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:a(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:a(function(h){return(h=this.conditionStack.length-1-Math.abs(h||0))>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:a(function(h){this.begin(h)},"pushState"),stateStackSize:a(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:a(function(h,b,g,u){switch(g){case 0:case 1:case 5:case 43:break;case 2:case 3:return this.popState(),34;case 4:return 34;case 6:return 10;case 7:return this.pushState("acc_title"),19;case 8:return this.popState(),"acc_title_value";case 9:return this.pushState("acc_descr"),21;case 10:return this.popState(),"acc_descr_value";case 11:this.pushState("acc_descr_multiline");break;case 12:case 25:case 27:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 5;case 15:return 8;case 16:return this.pushState("axis_data"),"X_AXIS";case 17:return this.pushState("axis_data"),"Y_AXIS";case 18:return this.pushState("axis_band_data"),24;case 19:return 31;case 20:return this.pushState("data"),16;case 21:return this.pushState("data"),18;case 22:return this.pushState("data_inner"),24;case 23:return 27;case 24:return this.popState(),26;case 26:this.pushState("string");break;case 28:return"STR";case 29:return 24;case 30:return 26;case 31:return 43;case 32:return"COLON";case 33:return 44;case 34:return 28;case 35:return 45;case 36:return 46;case 37:return 48;case 38:return 50;case 39:return 47;case 40:return 41;case 41:return 49;case 42:return 42;case 44:return 35;case 45:return 36}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:(\r?\n))/i,/^(?:(\r?\n))/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:\{)/i,/^(?:[^\}]*)/i,/^(?:xychart-beta\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\b)/i,/^(?:y-axis\b)/i,/^(?:\[)/i,/^(?:-->)/i,/^(?:line\b)/i,/^(?:bar\b)/i,/^(?:\[)/i,/^(?:[+-]?(?:\d+(?:\.\d+)?|\.\d+))/i,/^(?:\])/i,/^(?:(?:`\)                                    \{ this\.pushState\(md_string\); \}\n<md_string>\(\?:\(\?!`"\)\.\)\+                  \{ return MD_STR; \}\n<md_string>\(\?:`))/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s+)/i,/^(?:;)/i,/^(?:$)/i],conditions:{data_inner:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},data:{rules:[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_band_data:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_data:{rules:[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},title:{rules:[],inclusive:!1},md_string:{rules:[],inclusive:!1},string:{rules:[27,28],inclusive:!1},INITIAL:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0}}}}();function st(){this.yy={}}return gt.lexer=ei,a(st,"Parser"),st.prototype=gt,gt.Parser=st,new st}();dt.parser=dt;var bi=dt;function mt(e){return e.type==="bar"}function At(e){return e.type==="band"}function N(e){return e.type==="linear"}a(mt,"isBarPlot"),a(At,"isBandAxisData"),a(N,"isLinearAxisData");var V,Ot=(V=class{constructor(t){this.parentGroup=t}getMaxDimension(t,i){if(!this.parentGroup)return{width:t.reduce((l,c)=>Math.max(c.length,l),0)*i,height:i};const s={width:0,height:0},n=this.parentGroup.append("g").attr("visibility","hidden").attr("font-size",i);for(const l of t){const c=ai(n,1,l),p=c?c.width:l.length*i,x=c?c.height:i;s.width=Math.max(s.width,p),s.height=Math.max(s.height,x)}return n.remove(),s}},a(V,"TextDimensionCalculatorWithFont"),V),X,Wt=(X=class{constructor(t,i,s,n){this.axisConfig=t,this.title=i,this.textDimensionCalculator=s,this.axisThemeConfig=n,this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left",this.showTitle=!1,this.showLabel=!1,this.showTick=!1,this.showAxisLine=!1,this.outerPadding=0,this.titleTextHeight=0,this.labelTextHeight=0,this.range=[0,10],this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left"}setRange(t){this.range=t,this.axisPosition==="left"||this.axisPosition==="right"?this.boundingRect.height=t[1]-t[0]:this.boundingRect.width=t[1]-t[0],this.recalculateScale()}getRange(){return[this.range[0]+this.outerPadding,this.range[1]-this.outerPadding]}setAxisPosition(t){this.axisPosition=t,this.setRange(this.range)}getTickDistance(){const t=this.getRange();return Math.abs(t[0]-t[1])/this.getTickValues().length}getAxisOuterPadding(){return this.outerPadding}getLabelDimension(){return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map(t=>t.toString()),this.axisConfig.labelFontSize)}recalculateOuterPaddingToDrawBar(){.7*this.getTickDistance()>2*this.outerPadding&&(this.outerPadding=Math.floor(.7*this.getTickDistance()/2)),this.recalculateScale()}calculateSpaceIfDrawnHorizontally(t){let i=t.height;if(this.axisConfig.showAxisLine&&i>this.axisConfig.axisLineWidth&&(i-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){const s=this.getLabelDimension(),n=.2*t.width;this.outerPadding=Math.min(s.width/2,n);const l=s.height+2*this.axisConfig.labelPadding;this.labelTextHeight=s.height,l<=i&&(i-=l,this.showLabel=!0)}if(this.axisConfig.showTick&&i>=this.axisConfig.tickLength&&(this.showTick=!0,i-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){const s=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),n=s.height+2*this.axisConfig.titlePadding;this.titleTextHeight=s.height,n<=i&&(i-=n,this.showTitle=!0)}this.boundingRect.width=t.width,this.boundingRect.height=t.height-i}calculateSpaceIfDrawnVertical(t){let i=t.width;if(this.axisConfig.showAxisLine&&i>this.axisConfig.axisLineWidth&&(i-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){const s=this.getLabelDimension(),n=.2*t.height;this.outerPadding=Math.min(s.height/2,n);const l=s.width+2*this.axisConfig.labelPadding;l<=i&&(i-=l,this.showLabel=!0)}if(this.axisConfig.showTick&&i>=this.axisConfig.tickLength&&(this.showTick=!0,i-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){const s=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),n=s.height+2*this.axisConfig.titlePadding;this.titleTextHeight=s.height,n<=i&&(i-=n,this.showTitle=!0)}this.boundingRect.width=t.width-i,this.boundingRect.height=t.height}calculateSpace(t){return this.axisPosition==="left"||this.axisPosition==="right"?this.calculateSpaceIfDrawnVertical(t):this.calculateSpaceIfDrawnHorizontally(t),this.recalculateScale(),{width:this.boundingRect.width,height:this.boundingRect.height}}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}getDrawableElementsForLeftAxis(){const t=[];if(this.showAxisLine){const i=this.boundingRect.x+this.boundingRect.width-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["left-axis","axisl-line"],data:[{path:`M ${i},${this.boundingRect.y} L ${i},${this.boundingRect.y+this.boundingRect.height} `,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["left-axis","label"],data:this.getTickValues().map(i=>({text:i.toString(),x:this.boundingRect.x+this.boundingRect.width-(this.showLabel?this.axisConfig.labelPadding:0)-(this.showTick?this.axisConfig.tickLength:0)-(this.showAxisLine?this.axisConfig.axisLineWidth:0),y:this.getScaleValue(i),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"middle",horizontalPos:"right"}))}),this.showTick){const i=this.boundingRect.x+this.boundingRect.width-(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["left-axis","ticks"],data:this.getTickValues().map(s=>({path:`M ${i},${this.getScaleValue(s)} L ${i-this.axisConfig.tickLength},${this.getScaleValue(s)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["left-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.axisConfig.titlePadding,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:270,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForBottomAxis(){const t=[];if(this.showAxisLine){const i=this.boundingRect.y+this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["bottom-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${i} L ${this.boundingRect.x+this.boundingRect.width},${i}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["bottom-axis","label"],data:this.getTickValues().map(i=>({text:i.toString(),x:this.getScaleValue(i),y:this.boundingRect.y+this.axisConfig.labelPadding+(this.showTick?this.axisConfig.tickLength:0)+(this.showAxisLine?this.axisConfig.axisLineWidth:0),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){const i=this.boundingRect.y+(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["bottom-axis","ticks"],data:this.getTickValues().map(s=>({path:`M ${this.getScaleValue(s)},${i} L ${this.getScaleValue(s)},${i+this.axisConfig.tickLength}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["bottom-axis","title"],data:[{text:this.title,x:this.range[0]+(this.range[1]-this.range[0])/2,y:this.boundingRect.y+this.boundingRect.height-this.axisConfig.titlePadding-this.titleTextHeight,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForTopAxis(){const t=[];if(this.showAxisLine){const i=this.boundingRect.y+this.boundingRect.height-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["top-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${i} L ${this.boundingRect.x+this.boundingRect.width},${i}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["top-axis","label"],data:this.getTickValues().map(i=>({text:i.toString(),x:this.getScaleValue(i),y:this.boundingRect.y+(this.showTitle?this.titleTextHeight+2*this.axisConfig.titlePadding:0)+this.axisConfig.labelPadding,fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){const i=this.boundingRect.y;t.push({type:"path",groupTexts:["top-axis","ticks"],data:this.getTickValues().map(s=>({path:`M ${this.getScaleValue(s)},${i+this.boundingRect.height-(this.showAxisLine?this.axisConfig.axisLineWidth:0)} L ${this.getScaleValue(s)},${i+this.boundingRect.height-this.axisConfig.tickLength-(this.showAxisLine?this.axisConfig.axisLineWidth:0)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["top-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.axisConfig.titlePadding,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElements(){if(this.axisPosition==="left")return this.getDrawableElementsForLeftAxis();if(this.axisPosition==="right")throw Error("Drawing of right axis is not implemented");return this.axisPosition==="bottom"?this.getDrawableElementsForBottomAxis():this.axisPosition==="top"?this.getDrawableElementsForTopAxis():[]}},a(X,"BaseAxis"),X),Y,Ai=(Y=class extends Wt{constructor(t,i,s,n,l){super(t,n,l,i),this.categories=s,this.scale=pt().domain(this.categories).range(this.getRange())}setRange(t){super.setRange(t)}recalculateScale(){this.scale=pt().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(.5),$t.trace("BandAxis axis final categories, range: ",this.categories,this.getRange())}getTickValues(){return this.categories}getScaleValue(t){return this.scale(t)??this.getRange()[0]}},a(Y,"BandAxis"),Y),U,Si=(U=class extends Wt{constructor(t,i,s,n,l){super(t,n,l,i),this.domain=s,this.scale=It().domain(this.domain).range(this.getRange())}getTickValues(){return this.scale.ticks()}recalculateScale(){const t=[...this.domain];this.axisPosition==="left"&&t.reverse(),this.scale=It().domain(t).range(this.getRange())}getScaleValue(t){return this.scale(t)}},a(U,"LinearAxis"),U);function ft(e,t,i,s){const n=new Ot(s);return At(e)?new Ai(t,i,e.categories,e.title,n):new Si(t,i,[e.min,e.max],e.title,n)}a(ft,"getAxis");var H,wi=(H=class{constructor(t,i,s,n){this.textDimensionCalculator=t,this.chartConfig=i,this.chartData=s,this.chartThemeConfig=n,this.boundingRect={x:0,y:0,width:0,height:0},this.showChartTitle=!1}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){const i=this.textDimensionCalculator.getMaxDimension([this.chartData.title],this.chartConfig.titleFontSize),s=Math.max(i.width,t.width),n=i.height+2*this.chartConfig.titlePadding;return i.width<=s&&i.height<=n&&this.chartConfig.showTitle&&this.chartData.title&&(this.boundingRect.width=s,this.boundingRect.height=n,this.showChartTitle=!0),{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){const t=[];return this.showChartTitle&&t.push({groupTexts:["chart-title"],type:"text",data:[{fontSize:this.chartConfig.titleFontSize,text:this.chartData.title,verticalPos:"middle",horizontalPos:"center",x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.chartThemeConfig.titleColor,rotation:0}]}),t}},a(H,"ChartTitle"),H);function Ft(e,t,i,s){const n=new Ot(s);return new wi(n,e,t,i)}a(Ft,"getChartTitleComponent");var G,Ci=(G=class{constructor(t,i,s,n,l){this.plotData=t,this.xAxis=i,this.yAxis=s,this.orientation=n,this.plotIndex=l}getDrawableElement(){const t=this.plotData.data.map(s=>[this.xAxis.getScaleValue(s[0]),this.yAxis.getScaleValue(s[1])]);let i;return i=this.orientation==="horizontal"?Mt().y(s=>s[0]).x(s=>s[1])(t):Mt().x(s=>s[0]).y(s=>s[1])(t),i?[{groupTexts:["plot",`line-plot-${this.plotIndex}`],type:"path",data:[{path:i,strokeFill:this.plotData.strokeFill,strokeWidth:this.plotData.strokeWidth}]}]:[]}},a(G,"LinePlot"),G),j,ki=(j=class{constructor(t,i,s,n,l,c){this.barData=t,this.boundingRect=i,this.xAxis=s,this.yAxis=n,this.orientation=l,this.plotIndex=c}getDrawableElement(){const t=this.barData.data.map(n=>[this.xAxis.getScaleValue(n[0]),this.yAxis.getScaleValue(n[1])]),i=.95*Math.min(2*this.xAxis.getAxisOuterPadding(),this.xAxis.getTickDistance()),s=i/2;return this.orientation==="horizontal"?[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(n=>({x:this.boundingRect.x,y:n[0]-s,height:i,width:n[1]-this.boundingRect.x,fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]:[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(n=>({x:n[0]-s,y:n[1],width:i,height:this.boundingRect.y+this.boundingRect.height-n[1],fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]}},a(j,"BarPlot"),j),Q,_i=(Q=class{constructor(t,i,s){this.chartConfig=t,this.chartData=i,this.chartThemeConfig=s,this.boundingRect={x:0,y:0,width:0,height:0}}setAxes(t,i){this.xAxis=t,this.yAxis=i}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){return this.boundingRect.width=t.width,this.boundingRect.height=t.height,{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){if(!this.xAxis||!this.yAxis)throw Error("Axes must be passed to render Plots");const t=[];for(const[i,s]of this.chartData.plots.entries())switch(s.type){case"line":{const n=new Ci(s,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,i);t.push(...n.getDrawableElement())}break;case"bar":{const n=new ki(s,this.boundingRect,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,i);t.push(...n.getDrawableElement())}}return t}},a(Q,"BasePlot"),Q);function Nt(e,t,i){return new _i(e,t,i)}a(Nt,"getPlotComponent");var Vt,K,Ti=(K=class{constructor(t,i,s,n){this.chartConfig=t,this.chartData=i,this.componentStore={title:Ft(t,i,s,n),plot:Nt(t,i,s),xAxis:ft(i.xAxis,t.xAxis,{titleColor:s.xAxisTitleColor,labelColor:s.xAxisLabelColor,tickColor:s.xAxisTickColor,axisLineColor:s.xAxisLineColor},n),yAxis:ft(i.yAxis,t.yAxis,{titleColor:s.yAxisTitleColor,labelColor:s.yAxisLabelColor,tickColor:s.yAxisTickColor,axisLineColor:s.yAxisLineColor},n)}}calculateVerticalSpace(){let t=this.chartConfig.width,i=this.chartConfig.height,s=0,n=0,l=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),c=Math.floor(i*this.chartConfig.plotReservedSpacePercent/100),p=this.componentStore.plot.calculateSpace({width:l,height:c});t-=p.width,i-=p.height,p=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:i}),n=p.height,i-=p.height,this.componentStore.xAxis.setAxisPosition("bottom"),p=this.componentStore.xAxis.calculateSpace({width:t,height:i}),i-=p.height,this.componentStore.yAxis.setAxisPosition("left"),p=this.componentStore.yAxis.calculateSpace({width:t,height:i}),s=p.width,t-=p.width,t>0&&(l+=t,t=0),i>0&&(c+=i,i=0),this.componentStore.plot.calculateSpace({width:l,height:c}),this.componentStore.plot.setBoundingBoxXY({x:s,y:n}),this.componentStore.xAxis.setRange([s,s+l]),this.componentStore.xAxis.setBoundingBoxXY({x:s,y:n+c}),this.componentStore.yAxis.setRange([n,n+c]),this.componentStore.yAxis.setBoundingBoxXY({x:0,y:n}),this.chartData.plots.some(x=>mt(x))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateHorizontalSpace(){let t=this.chartConfig.width,i=this.chartConfig.height,s=0,n=0,l=0,c=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),p=Math.floor(i*this.chartConfig.plotReservedSpacePercent/100),x=this.componentStore.plot.calculateSpace({width:c,height:p});t-=x.width,i-=x.height,x=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:i}),s=x.height,i-=x.height,this.componentStore.xAxis.setAxisPosition("left"),x=this.componentStore.xAxis.calculateSpace({width:t,height:i}),t-=x.width,n=x.width,this.componentStore.yAxis.setAxisPosition("top"),x=this.componentStore.yAxis.calculateSpace({width:t,height:i}),i-=x.height,l=s+x.height,t>0&&(c+=t,t=0),i>0&&(p+=i,i=0),this.componentStore.plot.calculateSpace({width:c,height:p}),this.componentStore.plot.setBoundingBoxXY({x:n,y:l}),this.componentStore.yAxis.setRange([n,n+c]),this.componentStore.yAxis.setBoundingBoxXY({x:n,y:s}),this.componentStore.xAxis.setRange([l,l+p]),this.componentStore.xAxis.setBoundingBoxXY({x:0,y:l}),this.chartData.plots.some(_=>mt(_))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateSpace(){this.chartConfig.chartOrientation==="horizontal"?this.calculateHorizontalSpace():this.calculateVerticalSpace()}getDrawableElement(){this.calculateSpace();const t=[];this.componentStore.plot.setAxes(this.componentStore.xAxis,this.componentStore.yAxis);for(const i of Object.values(this.componentStore))t.push(...i.getDrawableElements());return t}},a(K,"Orchestrator"),K),q,Ri=(q=class{static build(t,i,s,n){return new Ti(t,i,s,n).getDrawableElement()}},a(q,"XYChartBuilder"),q),tt=0,ht=Ct(),it=wt(),y=Xt(),yt=it.plotColorPalette.split(",").map(e=>e.trim()),rt=!1,St=!1;function wt(){const e=oi(),t=bt();return Bt(e.xyChart,t.themeVariables.xyChart)}function Ct(){const e=bt();return Bt(hi.xyChart,e.xyChart)}function Xt(){return{yAxis:{type:"linear",title:"",min:1/0,max:-1/0},xAxis:{type:"band",title:"",categories:[]},title:"",plots:[]}}function lt(e){const t=bt();return ri(e.trim(),t)}function Yt(e){Vt=e}function Ut(e){ht.chartOrientation=e==="horizontal"?"horizontal":"vertical"}function Ht(e){y.xAxis.title=lt(e.text)}function kt(e,t){y.xAxis={type:"linear",title:y.xAxis.title,min:e,max:t},rt=!0}function Gt(e){y.xAxis={type:"band",title:y.xAxis.title,categories:e.map(t=>lt(t.text))},rt=!0}function jt(e){y.yAxis.title=lt(e.text)}function Qt(e,t){y.yAxis={type:"linear",title:y.yAxis.title,min:e,max:t},St=!0}function Kt(e){const t=Math.min(...e),i=Math.max(...e),s=N(y.yAxis)?y.yAxis.min:1/0,n=N(y.yAxis)?y.yAxis.max:-1/0;y.yAxis={type:"linear",title:y.yAxis.title,min:Math.min(s,t),max:Math.max(n,i)}}function _t(e){let t=[];if(e.length===0)return t;if(!rt){const i=N(y.xAxis)?y.xAxis.min:1/0,s=N(y.xAxis)?y.xAxis.max:-1/0;kt(Math.min(i,1),Math.max(s,e.length))}if(St||Kt(e),At(y.xAxis)&&(t=y.xAxis.categories.map((i,s)=>[i,e[s]])),N(y.xAxis)){const i=y.xAxis.min,s=y.xAxis.max,n=(s-i)/(e.length-1),l=[];for(let c=i;c<=s;c+=n)l.push(`${c}`);t=l.map((c,p)=>[c,e[p]])}return t}function Tt(e){return yt[e===0?0:e%yt.length]}function qt(e,t){const i=_t(t);y.plots.push({type:"line",strokeFill:Tt(tt),strokeWidth:2,data:i}),tt++}function Zt(e,t){const i=_t(t);y.plots.push({type:"bar",fill:Tt(tt),data:i}),tt++}function Jt(){if(y.plots.length===0)throw Error("No Plot to render, please provide a plot with some data");return y.title=zt(),Ri.build(ht,y,it,Vt)}function ti(){return it}function ii(){return ht}a(wt,"getChartDefaultThemeConfig"),a(Ct,"getChartDefaultConfig"),a(Xt,"getChartDefaultData"),a(lt,"textSanitizer"),a(Yt,"setTmpSVGG"),a(Ut,"setOrientation"),a(Ht,"setXAxisTitle"),a(kt,"setXAxisRangeData"),a(Gt,"setXAxisBand"),a(jt,"setYAxisTitle"),a(Qt,"setYAxisRangeData"),a(Kt,"setYAxisRangeFromPlotData"),a(_t,"transformDataWithoutCategory"),a(Tt,"getPlotColorFromPalette"),a(qt,"setLineData"),a(Zt,"setBarData"),a(Jt,"getDrawableElem"),a(ti,"getChartThemeConfig"),a(ii,"getChartConfig");var de={parser:bi,db:{getDrawableElem:Jt,clear:a(function(){pi(),tt=0,ht=Ct(),y={yAxis:{type:"linear",title:"",min:1/0,max:-1/0},xAxis:{type:"band",title:"",categories:[]},title:"",plots:[]},it=wt(),yt=it.plotColorPalette.split(",").map(e=>e.trim()),rt=!1,St=!1},"clear"),setAccTitle:li,getAccTitle:ci,setDiagramTitle:gi,getDiagramTitle:zt,getAccDescription:ui,setAccDescription:xi,setOrientation:Ut,setXAxisTitle:Ht,setXAxisRangeData:kt,setXAxisBand:Gt,setYAxisTitle:jt,setYAxisRangeData:Qt,setLineData:qt,setBarData:Zt,setTmpSVGG:Yt,getChartThemeConfig:ti,getChartConfig:ii},renderer:{draw:a((e,t,i,s)=>{const n=s.db,l=n.getChartThemeConfig(),c=n.getChartConfig();function p(m){return m==="top"?"text-before-edge":"middle"}function x(m){return m==="left"?"start":m==="right"?"end":"middle"}function _(m){return`translate(${m.x}, ${m.y}) rotate(${m.rotation||0})`}a(p,"getDominantBaseLine"),a(x,"getTextAnchor"),a(_,"getTextTransformation"),$t.debug(`Rendering xychart chart
`+e);const T=di(t),w=T.append("g").attr("class","main"),f=w.append("rect").attr("width",c.width).attr("height",c.height).attr("class","background");mi(T,c.height,c.width,!0),T.attr("viewBox",`0 0 ${c.width} ${c.height}`),f.attr("fill",l.backgroundColor),n.setTmpSVGG(T.append("g").attr("class","mermaid-tmp-group"));const L=n.getDrawableElem(),k={};function P(m){let A=w,r="";for(const[D]of m.entries()){let $=w;D>0&&k[r]&&($=k[r]),r+=m[D],A=k[r],A||(A=k[r]=$.append("g").attr("class",m[D]))}return A}a(P,"getGroup");for(const m of L){if(m.data.length===0)continue;const A=P(m.groupTexts);switch(m.type){case"rect":A.selectAll("rect").data(m.data).enter().append("rect").attr("x",r=>r.x).attr("y",r=>r.y).attr("width",r=>r.width).attr("height",r=>r.height).attr("fill",r=>r.fill).attr("stroke",r=>r.strokeFill).attr("stroke-width",r=>r.strokeWidth);break;case"text":A.selectAll("text").data(m.data).enter().append("text").attr("x",0).attr("y",0).attr("fill",r=>r.fill).attr("font-size",r=>r.fontSize).attr("dominant-baseline",r=>p(r.verticalPos)).attr("text-anchor",r=>x(r.horizontalPos)).attr("transform",r=>_(r)).text(r=>r.text);break;case"path":A.selectAll("path").data(m.data).enter().append("path").attr("d",r=>r.path).attr("fill",r=>r.fill?r.fill:"none").attr("stroke",r=>r.strokeFill).attr("stroke-width",r=>r.strokeWidth)}}},"draw")}};export{de as diagram};

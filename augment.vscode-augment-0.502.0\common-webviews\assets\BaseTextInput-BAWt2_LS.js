import{S as x,i as m,s as v,X as b,a as y,D as z,E as g,a1 as u,a2 as p,e as h,F as j,g as k,u as d,t as f,h as w,G as B,a0 as D,J as E,K as F,L as G,M as J}from"./SpinnerAugment-CL9SZpf8.js";import"./IconButtonAugment-C4xMcLhX.js";function K(c){let s;const n=c[5].default,t=E(n,c,c[6],null);return{c(){t&&t.c()},m(a,i){t&&t.m(a,i),s=!0},p(a,i){t&&t.p&&(!s||64&i)&&F(t,n,a,a[6],s?J(n,a[6],i,null):G(a[6]),null)},i(a){s||(d(t,a),s=!0)},o(a){f(t,a),s=!1},d(a){t&&t.d(a)}}}function L(c){let s,n,t,a;n=new b({props:{type:c[2],size:c[1],$$slots:{default:[K]},$$scope:{ctx:c}}});let i=[c[4],{class:t=`c-base-text-input c-base-text-input--${c[0]} c-base-text-input--size-${c[1]}`}],l={};for(let e=0;e<i.length;e+=1)l=y(l,i[e]);return{c(){s=z("div"),g(n.$$.fragment),u(s,l),p(s,"c-base-text-input--has-color",c[3]!==void 0),p(s,"svelte-1jrck44",!0)},m(e,o){h(e,s,o),j(n,s,null),a=!0},p(e,[o]){const $={};4&o&&($.type=e[2]),2&o&&($.size=e[1]),64&o&&($.$$scope={dirty:o,ctx:e}),n.$set($),u(s,l=k(i,[16&o&&e[4],(!a||3&o&&t!==(t=`c-base-text-input c-base-text-input--${e[0]} c-base-text-input--size-${e[1]}`))&&{class:t}])),p(s,"c-base-text-input--has-color",e[3]!==void 0),p(s,"svelte-1jrck44",!0)},i(e){a||(d(n.$$.fragment,e),a=!0)},o(e){f(n.$$.fragment,e),a=!1},d(e){e&&w(s),B(n)}}}function M(c,s,n){let t,{$$slots:a={},$$scope:i}=s,{variant:l="surface"}=s,{size:e=2}=s,{type:o="default"}=s,{color:$}=s;return c.$$set=r=>{"variant"in r&&n(0,l=r.variant),"size"in r&&n(1,e=r.size),"type"in r&&n(2,o=r.type),"color"in r&&n(3,$=r.color),"$$scope"in r&&n(6,i=r.$$scope)},c.$$.update=()=>{8&c.$$.dirty&&n(4,t=D($||"accent"))},[l,e,o,$,t,a,i]}class q extends x{constructor(s){super(),m(this,s,M,L,v,{variant:0,size:1,type:2,color:3})}}export{q as B};
